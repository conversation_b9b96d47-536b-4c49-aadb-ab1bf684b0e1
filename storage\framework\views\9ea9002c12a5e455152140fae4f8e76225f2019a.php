<?php $__env->startPush('css'); ?>
    <link href="<?php echo e(asset('plugins/components/datatables/jquery.dataTables.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">User Management</h2>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin')): ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">Users List</h3>
                    <a  class="btn btn-success pull-right" href="<?php echo e(url('user/create')); ?>"><i class="icon-plus"></i> Add user</a>
                    <div class="clearfix"></div>
                    <hr>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table id="myTable" class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Name</th>
                                        <th>Role</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($key+1); ?></td>
                                            <td><?php echo e($user->name); ?></td>
                                            <td><?php echo e($user->roles()->pluck('name')->implode(', ')); ?></td>
                                            <th>
                                                <a href="<?php echo e(url('user/edit/'.$user->id)); ?>"><i class="icon-pencil"></i> Edit</a> &nbsp;&nbsp;
                                                <a class="delete" href="<?php echo e(url('user/delete/'.$user->id)); ?>"><i class="icon-trash"></i> Delete</a>
                                            </th>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php echo $__env->make('layouts.partials.right-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
    <?php elseif(auth()->user()->hasRole('user')): ?>
        <section class="projects_wrapper custom_dropdown pagination_scroll_tbl">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_projects_tabs scrollable_tbl">
                            <div class="cms_tabs">
                                <ul class="nav projects nav-pills">
                                    
                                    <li class="active"><a href="#buyer" data-toggle="tab" aria-expanded="false">Buyer</a></li>
                                    <li><a href="#service_provider" data-toggle="tab" aria-expanded="true">Service Provider</a></li>
                                </ul>
                            </div>
                            <div class="custom_search_filter custom_flex">
                                <div class="txt_field custom_search">
                                    <input type="text" placeholder="Search" class="custom_search_box">
                                </div>
                            </div>
                            <div class="tab-content custom_tab_content">
                                
                                    
                                        
                                            
                                                
                                                
                                                    
                                                    
                                                    
                                                    
                                                    
                                                
                                                
                                                
                                                    
                                                    
                                                            
                                                            
                                                            
                                                        
                                                        
                                                                
                                                                    
                                                                
                                                                
                                                                        
                                                                    
                                                                    
                                                                
                                                            
                                                        
                                                    
                                                    
                                                
                                            
                                        
                                    
                                
                                <div class="tab-pane active" id="buyer" >
                                    <div class="table-responsive">
                                        <div class="custom_table">
                                            <table id="" class="table table-striped myTable datatable">
                                                <thead>
                                                <tr>
                                                    <th>User Name</th>
                                                    <th>On-Board Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <?php $__currentLoopData = $users->filter(function($user) { return $user->roles->contains('name', 'buyer'); }); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><a href="<?php echo e(url('user_view')); ?>/<?php echo e($user->id); ?>"><?php echo e($user->name??'---'); ?></a></td>
                                                        <td><?php echo e($user->created_at->format('d-m-y') ?? '---'); ?></td>
                                                        <td><div class="dropdown">
                                                                <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                                    <i class="fa-solid fa-ellipsis"></i>
                                                                </button>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                                    <li><a class="dropdown-item" href="<?php echo e(url('user_view')); ?>/<?php echo e($user->id); ?>"><i class="fa-solid fa-eye"></i>View</a></li>


                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane" id="service_provider" >
                                    <div class="table-responsive">
                                        <div class="custom_table">
                                            <table id="" class="table table-striped myTable datatable">
                                                <thead>
                                                <tr>
                                                    <th>User Name</th>
                                                    <th>On-Board Date</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <?php $__currentLoopData = $users->filter(function($user) { return $user->roles->contains('name', 'seller'); }); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $seller): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($seller->name ??'---'); ?></td>
                                                        <td><?php echo e($seller->created_at->format('d-m-y') ?? '---'); ?></td>
                                                        <td>
                                                            <span class="badge <?php echo e($seller->UserStatus['class']); ?>"><?php echo e($seller->UserStatus['text']); ?></span>
                                                        </td>
                                                        <td><div class="dropdown">
                                                                <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                                    <i class="fa-solid fa-ellipsis"></i>
                                                                </button>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                                    <li><a class="dropdown-item" href="<?php echo e(url('user_view')); ?>/<?php echo e($seller->id); ?>"><i class="fa-solid fa-eye"></i>View</a></li>


                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('plugins/components/toast-master/js/jquery.toast.js')); ?>"></script>
    <script src="<?php echo e(asset('plugins/components/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.2/js/dataTables.buttons.min.js"></script>
    <script>
        $(document).ready(function () {
            $(document).on('click','.delete',function (e) {
                if(confirm('Are you sure want to delete?'))
                {
                }
                else
                {
                    return false;
                }
            });
            <?php if(\Session::has('message')): ?>
            $.toast({
                heading: 'Success!',
                position: 'top-center',
                text: '<?php echo e(session()->get('message')); ?>',
                loaderBg: '#ff6849',
                icon: 'success',
                hideAfter: 3000,
                stack: 6
            });
            <?php endif; ?>
        })

        $(function() {
            $('#myTable').DataTable({
                "columns": [
                    null, null,null, {"orderable": false}
                ]
            });

        });
    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/users/index.blade.php ENDPATH**/ ?>