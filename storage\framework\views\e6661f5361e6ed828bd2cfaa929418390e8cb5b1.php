

<?php $__env->startPush("css"); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="my_projects_page">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h3 class="projects_title">My Projects</h3>
                </div>
                <div class="col-md-12">
                    <div class="table_box table_padding">
                        <div class="heading_part">
                            <ul class="nav nav-pills " id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active tab_button" id="pills-all-tab" data-bs-toggle="pill" data-bs-target="#pills-all" type="button" role="tab" aria-controls="pills-all" aria-selected="true">All</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link tab_button" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">On-Going</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link tab_button" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Posted</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link tab_button" id="pills-completed-tab" data-bs-toggle="pill" data-bs-target="#pills-completed" type="button" role="tab" aria-controls="pills-completed" aria-selected="false">Completed</button>
                                </li>
                            </ul>
                        </div>
                        <div class="custom_search_filter ">
                            <div class="txt_field custom_search">
                                <input type="text" placeholder="Search" class="custom_search_box">
                            </div>
                        </div>
                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-all" role="tabpanel" aria-labelledby="pills-all-tab" tabindex="0">
                                <div class="table-responsive">
                                    <div class="custom_table">
                                        <table  class="table myTable datatable">
                                            <thead>
                                            <tr>
                                                <th>Project Name</th>
                                                <th>Service Provider</th>
                                                <th>Location</th>
                                                <th>Email </th>
                                                <th>Starting Date</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>

                                                    <td>
                                                        <?php if($job->status == 'on_going'): ?>
                                                            <a href="<?php echo e(url('my_projects_ongoing_view')); ?>/<?php echo e($job->id); ?>">
                                                                <?php echo e($job->project_number ?? '---'); ?> / <?php echo e($job->project_title ?? '---'); ?>

                                                            </a>
                                                        <?php elseif(in_array($job->status, ['posted', 'pending'])): ?>
                                                            <a href="<?php echo e(url('posted_view')); ?>/<?php echo e($job->id); ?>">
                                                                <?php echo e($job->project_number ?? '---'); ?> / <?php echo e($job->project_title ?? '---'); ?>

                                                            </a>
                                                        <?php elseif($job->status == 'completed'): ?>
                                                            <a href="<?php echo e(url('my_projects_completed_view')); ?>/<?php echo e($job->id); ?>">
                                                                <?php echo e($job->project_number ?? '---'); ?> / <?php echo e($job->project_title ?? '---'); ?>

                                                            </a>
                                                        <?php endif; ?>

                                                    </td>
                                                    <td> <?php echo e($job->assignSeller->name??'Not selected yet'); ?></td>
                                                    <td><?php echo e($job->state??'----'); ?> / <?php echo e($job->city??'----'); ?></td>
                                                    <td><?php echo e($job->user->email ?? '----'); ?></td>
                                                    <td><?php echo e($job->visit_date ?? '--'); ?></td>
                                                    <td>
                                                        <span class="<?php echo e($job->status == 'completed' ? 'success' : ($job->status == 'on_going' ? 'ongoing' : 'pending')); ?>">
                                                            <?php echo e(str_replace('_', ' ', $job->status) ?? '---'); ?>

                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="dropdown">
                                                            <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="fa-solid fa-ellipsis"></i>
                                                            </button>





                                                            <?php if($job->status == 'on_going'): ?>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                                    <li><a class="dropdown-item" href="<?php echo e(url('my_projects_ongoing_view')); ?>/<?php echo e($job->id ??''); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                                </ul
                                                            <?php elseif(in_array($job->status, ['posted', 'pending'])): ?>

                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                                    <li><a class="dropdown-item" href="<?php echo e(url('posted_view')); ?>/<?php echo e($job->id); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                                </ul
                                                            <?php elseif($job->status == 'completed'): ?>

                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                                    <li><a class="dropdown-item"  href="<?php echo e(url('my_projects_completed_view')); ?>/<?php echo e($job->id ?? ''); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                                </ul
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">
                                <div class="table-responsive">
                                 <div class="custom_table">
                                    <table  class="table myTable datatable">
                                        <thead>
                                        <tr>
                                            <th>Project Name</th>
                                            <th>Service Provider</th>
                                            <th>Location</th>
                                            <th>Email </th>
                                            <th>Starting Date</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $jobs->where('status','on_going'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                    <td><a href="<?php echo e(url('my_projects_ongoing_view')); ?>/<?php echo e($job->id); ?>"><?php echo e($job->project_number ?? '---'); ?> / <?php echo e($job->project_title ?? '---'); ?></a></td>
                                                    <td><?php echo e($job->assignSeller->name??'Not selected yet'); ?></td>
                                                    <td><?php echo e($job->state??'----'); ?> / <?php echo e($job->city??'----'); ?></td>
                                                    <td><?php echo e($job->user->email ?? '----'); ?></td>
                                                    <td><?php echo e($job->visit_date ?? '----'); ?></td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                                <li><a class="dropdown-item" href="<?php echo e(url('my_projects_ongoing_view')); ?>/<?php echo e($job->id); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            </div>
                            <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">
                                <div class="table-responsive">
                                    <table  class="table myTable datatable">
                                        <thead>
                                        <tr>
                                            <th>Project Name</th>
                                                <th>Service Provider</th>
                                            <th>Location</th>
                                            <th>Email </th>
                                            <th>Starting Date</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(in_array($job->status, ['posted', 'pending'])): ?>
                                            <tr>
                                                    <td><a href="<?php echo e(url('posted_view')); ?>/<?php echo e($job->id); ?>"><?php echo e($job->project_number ?? '---'); ?> / <?php echo e($job->project_title ?? '---'); ?></a></td>
                                                    <td> <?php echo e($job->assignSeller->name??'Not selected yet'); ?></td>
                                                    <td><?php echo e($job->state??'----'); ?> / <?php echo e($job->city??'----'); ?></td>
                                                    <td><?php echo e($job->user->email ?? '----'); ?></td>
                                                    <td><?php echo e($job->visit_date ?? '----'); ?></td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                                <li><a class="dropdown-item" href="<?php echo e(url('posted_view')); ?>/<?php echo e($job->id); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>

                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade  " id="pills-completed" role="tabpanel" aria-labelledby="pills-completed-tab" tabindex="0">
                                <div class="table-responsive">
                                    <table  class="table myTable datatable">
                                        <thead>
                                        <tr>
                                            <th>Project Name</th>
                                            <th>Service Provider</th>
                                            <th>Location</th>
                                            <th>Email </th>
                                            <th>Starting Date</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $jobs->where('status','completed'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>





















                                            <tr>
                                                        <td><a href="<?php echo e(route('my_projects_completed_view')); ?>/<?php echo e($job->id); ?>"><?php echo e($job->project_number ?? '---'); ?> / <?php echo e($job->project_title ?? '---'); ?></a></td>
                                                        <td><?php echo e($job->assignSeller->name??'Not selected yet'); ?></td>
                                                        <td><?php echo e($job->state??'----'); ?> / <?php echo e($job->city??'----'); ?></td>
                                                        <td><?php echo e($job->user->email ?? '----'); ?></td>
                                                        <td><?php echo e($job->visit_date ?? '----'); ?></td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                                    <li><a class="dropdown-item" href="<?php echo e(route('my_projects_completed_view')); ?>/<?php echo e($job->id); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                        
                            
                            
                            
                            
                            
                        
                    

                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(function() {
            var dataTable = $('.myTable'). DataTable({
                "searching": true,
                "bLengthChange": false,
                "paging": true,
                "info": true,
            });
            $(document).on("input", '.custom_search_box', function () {
                var searchValue = $(this).val();
                dataTable.search(searchValue).draw();
            });
        });

    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mrdoall-git\resources\views/website/buyer/my_projects.blade.php ENDPATH**/ ?>