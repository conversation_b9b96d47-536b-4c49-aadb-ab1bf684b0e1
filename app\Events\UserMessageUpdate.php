<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;

class UserMessageUpdate implements ShouldBroadcast
{
    use SerializesModels;

    public $messageData;
    public $targetUsers; // array of user IDs who should receive this event

    public function __construct($messageData = null, $targetUsers = [])
    {
        $this->messageData = $messageData;
        $this->targetUsers = $targetUsers;
    }

    public function broadcastOn()
    {
        // Broadcast to specific users involved in the conversation
        if (!empty($this->targetUsers)) {
            $channels = [];
            foreach ($this->targetUsers as $userId) {
                $channels[] = new Channel('user-messages-' . $userId);
            }
            return $channels;
        }

        // Fallback to global channel if no specific users
        return new Channel('user-messages-global');
    }

    public function broadcastAs()
    {
        return 'new-message';
    }
}
