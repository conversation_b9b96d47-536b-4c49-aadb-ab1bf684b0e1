<?php $__env->startPush("css"); ?>
    <link rel="stylesheet"href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css" />
    <link rel="stylesheet" href="<?php echo e(asset('plugins/components/dropify/dist/css/dropify.min.css')); ?>">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
    <style>
        header,footer{display: none;}
        .error {
            border-color: red;
        }
        .error-message {
            color: red;
            font-size: 12px;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section id="" class="login_register signup">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-7 col-md-6 custom_column_padding">
                    <div class="custom_banner">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                            <div class="banner_content custom_login_title">
                                <a href="<?php echo e(url('home')); ?>">
                                    <div class="site_logo">
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png">
                                    </div>
                                </a>
                                <div class="site_title">
                                    <h1>Tackle any home improvement project, effortlessly</h1>
                                </div>
                                <div class="site_key_parameter">
                                    <a href="javascript:void(0)"><i class="fa-regular fa-circle-check"></i>Free consultation</a>
                                    <a href="javascript:void(0)"><i class="fa-solid fa-award"></i>Satisfaction Guaranteed</a>
                                    <a href="javascript:void(0)"><img src="<?php echo e(asset('website')); ?>/assets/images/banner_sheild.png">Protected Payments</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5 col-md-6 custom_column_padding">
                    <?php if(request()->query('role_id') == 'client'): ?>
                        <div class="custom_scrollbar">
                            <div class="login_box custom_roles" id="clientRole">
                                <form class="form-horizontal form-material" id="loginform" method="POST" action="<?php echo e(route('register')); ?>">
                                    <?php echo e(csrf_field()); ?>

                                    <input type="hidden" name="role_id" value="client">
                                    <div class="row custom_row">
                                        <div class="col-md-12">
                                            <h1>Register</h1>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label for="salutation" class="form-label">Salutation</label>
                                                <select name="salutation" id="salutation" class="salutation_select">
                                                    <?php if(isset($salutations) && $salutations->count()>0): ?>
                                                        <?php $__currentLoopData = $salutations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $salutation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($salutation->id); ?>" <?php echo e(old('salutation') == $salutation->id ? 'selected' : ''); ?>><?php echo e($salutation->name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field">
                                                <label for="" class="form-label">First Name</label>
                                                <input id="first_name" type="text" class="form-control" placeholder="Enter First Name" name="first_name" value="<?php echo e(session()->has('google_user') ? session('google_user')['name'] : old('first_name')); ?>" required autofocus >
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Last Name</label>
                                                <input id="last_name" type="text" class="form-control" placeholder="Enter Last Name" name="last_name" value="<?php echo e(old('last_name')); ?>" required autofocus>
                                            </div>
                                        </div>
                                        
                                        
                                        
                                        
                                        
                                        
                                        <div class="col-md-12">
                                            <div class="txt_field country_code">
                                                <label for="" class="form-label">Phone</label>
                                                <input  type="tel" class="form-control phone" placeholder="Enter Phone" name="phone" value="<?php echo e(old('phone')); ?>" required autofocus>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Service Address</label>
                                                <input id="address_client" type="text" class="form-control" placeholder="Enter Address" name="address" value="<?php echo e(old('address')); ?>" required autofocus>
                                                <input id="longitude" type="hidden" class="form-control"  name="longitude" value="<?php echo e(old('longitude')); ?>"  >
                                                <input id="latitude" type="hidden" class="form-control" name="latitude" value="<?php echo e(old('latitude')); ?>"  >
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field country_code">
                                                <label for="" class="form-label">Apt,Suite,Floor</label>
                                                <input id="suite_or_floor" type="tel" class="form-control phone" placeholder="Enter Apt,Suite,Floor" name="suite_or_floor" value="<?php echo e(old('suite_or_floor')); ?>" autofocus>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field">
                                                <label for="" class="form-label">City</label>
                                                <input type="text" class="form-control" placeholder="Enter City" name="city" id="city" value="<?php echo e(old('city')); ?>" required autofocus>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field">
                                                <label for="" class="form-label">State</label>
                                                <input  type="text" class="form-control" placeholder="Enter State" name="state" id="state" value="<?php echo e(old('state')); ?>" required autofocus>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Zip Code</label>
                                                <input  type="text" class="form-control" placeholder="Enter Zip Code" name="zip_code" id="zip_code" value="<?php echo e(old('zip_code')); ?>" required autofocus>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Country</label>
                                                <input  type="text" class="form-control" placeholder="Enter Country" name="country" id="country" value="<?php echo e(old('country')); ?>" required autofocus>
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Email</label>
                                                <input id="client_email" type="email" autocomplete="new-password" class="form-control<?php echo e($errors->has('email') ? ' is-invalid' : ''); ?> email" name="email" placeholder="Enter Email" value="<?php echo e(session('google_user') ? session('google_user')['email'] : old('email')); ?>" required  <?php if(session('google_user')): ?>  readonly <?php endif; ?>>
                                                <?php if($errors->has('email')): ?>
                                                    <span class="invalid-feedback">
                                                        <strong><?php echo e($errors->first('email')); ?></strong>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Password</label>
                                                <input id="password" type="password" autocomplete="new-password" class="form-control<?php echo e($errors->has('password') ? ' is-invalid' : ''); ?> password_eye" placeholder="Enter Password" name="password" required>
                                                <i class="fa-solid custom_eye_icon fa-eye"></i>
                                                <i class="fa-solid custom_eye_icon fa-eye-slash"></i>
                                                <span id="password-error" class="invalid-feedback" style="display:none;">
                                                            <strong>Password must be at least 8 characters, contain one uppercase letter, one number, and one special character.</strong>
                                                        </span>
                                                <?php if($errors->has('password')): ?>
                                                    <span class="invalid-feedback">
                                                        <strong><?php echo e($errors->first('password')); ?></strong>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Confirm Password</label>
                                                <input id="password_confirmation" type="password"  autocomplete="new-password" class="form-control<?php echo e($errors->has('password_confirmation') ? ' is-invalid' : ''); ?> password_eye password_confirmation" placeholder="Confirm Password" name="password_confirmation" required>
                                                <i class="fa-solid custom_eye_icon fa-eye"></i>
                                                <i class="fa-solid custom_eye_icon fa-eye-slash"></i>
                                                <?php if($errors->has('password_confirmation')): ?>
                                                    <span class="invalid-feedback">
                                                        <strong><?php echo e($errors->first('password_confirmation')); ?></strong>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="g-recaptcha"  name="recaptcha" data-sitekey="<?php echo e(env('RECAPTCHA_SITE_KEY')); ?>"></div>
                                            <span class="text-danger" id="recaptcha-error"></span>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="submit_btn">
                                                <button type="submit" id="login_btn" class="btn btn_black btn_sign_up disable-on-submit">Sign Up
                                                    <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <h5>Already have an account? <a href="<?php echo e(route('login')); ?>" class=""><b>Sign In</b></a></h5>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="custom_scrollbar">
                            <div class="login_box custom_roles" id="contractorRole">
                                <form class="form-horizontal form-material"  method="POST" action="<?php echo e(route('register')); ?>" id="login_cont_form" enctype="multipart/form-data">
                                    <?php echo csrf_field(); ?>
                                    <div class="stepper">
                                        <div class="step active" id="step1">
                                            <h1>Step 1: Register</h1>
                                            <input type="hidden" name="role_id" value="contractor">
                                            <div class="row custom_row">
                                                <div class="col-md-12">
                                                    <h1>Register</h1>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label class="form-label">Company Name</label>
                                                        <input type="text" class="form-control" placeholder="Enter Company Name" name="company_name" value="<?php echo e(old('company_name')); ?>" required autofocus>
                                                        <?php if($errors->has('company_name')): ?>
                                                            <span class="invalid-feedback">
                                                            <strong><?php echo e($errors->first('company_name')); ?></strong>
                                                        </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <!-- Salutation Field -->
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label for="salutation" class="form-label">Salutation</label>
                                                        <select name="salutation" id="salutation" class="salutation_select">
                                                            <?php if(isset($salutations) && $salutations->count()>0): ?>
                                                                <?php $__currentLoopData = $salutations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $salutation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($salutation->id); ?>" <?php echo e(old('salutation') == $salutation->id ? 'selected' : ''); ?>><?php echo e($salutation->name); ?></option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- First Name Field -->
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field">
                                                        <label class="form-label">First Name</label>
                                                        <input type="text" class="form-control" placeholder="Enter First Name" name="first_name"value="<?php echo e(session()->has('google_user') ? session('google_user')['name'] : old('first_name')); ?>" required  >
                                                    </div>
                                                </div>
                                                <!-- Last Name Field -->
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field">
                                                        <label class="form-label">Last Name</label>
                                                        <input type="text" class="form-control" placeholder="Enter Last Name" name="last_name" value="<?php echo e(old('last_name')); ?>" required>
                                                    </div>
                                                </div>
                                                <!-- Email Field -->
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label class="form-label">Email</label>
                                                        <input id="email" type="email" autocomplete="username" class="form-control<?php echo e($errors->has('email') ? ' is-invalid' : ''); ?>" placeholder="Enter Email" name="email" value="<?php echo e(session('google_user') ? session('google_user')['email'] : old('email')); ?>" required  <?php if(session('google_user')): ?>  readonly <?php endif; ?>>
                                                        <?php if($errors->has('email')): ?>
                                                            <span class="invalid-feedback">
                                                            <strong><?php echo e($errors->first('email')); ?></strong>
                                                        </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <!-- Password Field -->
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label class="form-label">Password</label>
                                                        <input type="password" autocomplete="new-password" class="form-control<?php echo e($errors->has('password') ? ' is-invalid' : ''); ?> password_eye" placeholder="Enter Password" name="password" id="password" required>
                                                        <i class="fa-solid custom_eye_icon fa-eye"></i>
                                                        <i class="fa-solid custom_eye_icon fa-eye-slash"></i>
                                                        <span id="password-error" class="invalid-feedback" style="display:none;">
                                                            <strong>Password must be at least 8 characters, contain one uppercase letter, one number, and one special character.</strong>
                                                        </span>
                                                        <?php if($errors->has('password')): ?>
                                                            <span class="invalid-feedback">
                                                            <strong><?php echo e($errors->first('password')); ?></strong>
                                                        </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label for="" class="form-label">Confirm Password</label>
                                                        <input id="password_confirmation_contractor" type="password" autocomplete="new-password" class="form-control password_eye password_confirmation_contractor" placeholder="Confirm Password" name="password_confirmation" required>
                                                        <i class="fa-solid custom_eye_icon fa-eye"></i>
                                                        <i class="fa-solid custom_eye_icon fa-eye-slash"></i>
                                                        <?php if($errors->has('password_confirmation_contractor')): ?>
                                                            <span class="invalid-feedback">
                                                                <strong><?php echo e($errors->first('password_confirmation_contractor')); ?></strong>
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field country_code">
                                                        <label for="" class="form-label">Phone</label>
                                                        <input id="phone" type="tel" class="form-control phone" placeholder="Enter Phone" name="phone" value="<?php echo e(old('phone')); ?>" required autofocus>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field">
                                                        <label for="" class="form-label">Address</label>
                                                        <input id="contractor_address" type="text" class="form-control" placeholder="Enter Address" name="address" value="<?php echo e(old('address')); ?>" required autofocus>
                                                        <input id="contractor_longitude" type="hidden" class="form-control"  name="longitude" >
                                                        <input id="contractor_latitude" type="hidden" class="form-control"  name="latitude"  >

                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field">
                                                        <label for="" class="form-label">City</label>
                                                        <input id="contractor_city" type="text" class="form-control" placeholder="Enter City" name="city" value="<?php echo e(old('address')); ?>" required autofocus>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field">
                                                        <label for="" class="form-label">State</label>
                                                        <input id="contractor_state" type="text" class="form-control" placeholder="Enter State" name="state" value="<?php echo e(old('state')); ?>" required autofocus>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field">
                                                        <label for="" class="form-label">Zip Code</label>
                                                        <input id="contractor_zip_code" type="text" class="form-control" placeholder="Enter Zip Code" name="zip_code" value="<?php echo e(old('zip_code')); ?>" required autofocus>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field">
                                                        <label for="" class="form-label">Contractor Country</label>
                                                        <input id="contractor_country" type="text" class="form-control" placeholder="Enter Zip Code" name="contractor_country" value="<?php echo e(old('contractor_country')); ?>" required autofocus>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field country_code">
                                                        <label for="" class="form-label">Apt,Suite,Floor</label>
                                                        <input  type="tel" class="form-control phone" placeholder="Enter Apt,Suite,Floor" name="suite_or_floor" value="<?php echo e(old('suite_or_floor')); ?>" autofocus>
                                                    </div>
                                                </div>
                                                <!-- Submit and Continue Button -->
                                                <div class="col-md-12">
                                                    <div class="submit_btn previous_step" >
                                                        <button type="button" class="btn btn_black step_one_btn" id="step_one_btn" onclick="showStep('step2')">Continue
                                                            <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                                        </button>
                                                        <a href="<?php echo e(url('registration_role')); ?>" class="btn btn_black "><div class="next_btn back_btn_stepper"><i class="fa-solid fa-arrow-left"></i></div> Back</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Step 2 -->
                                        <div class="step" id="step2" style="display: none;">
                                            <h1>Step 2: Finalize Your Profile</h1>
                                            <div class="row custom_row">
                                                <div class="col-xl-6 col-lg-12">
                                                    <div class="txt_field">
                                                        <label class="form-label">Date Company was Established</label>
                                                        <div id="" class="date datepicker" data-date-format="mm-dd-yyyy">
                                                            <input class="form-control" type="text" placeholder="DD/MM/YYYY" readonly name="date_company_was_established" value="<?php echo e(old('date_company_was_established')); ?>"/>
                                                            <span class="input-group-addon"><i class="fa-solid fa-calendar"></i></span>
                                                        </div>
                                                        
                                                    </div>
                                                </div>
                                                <!-- Industry Field -->
                                                <div class="col-xl-6 col-lg-12">
                                                    <div class="txt_field">
                                                        <label class="form-label">Categories</label>
                                                        <select class="form-select" name="category_id">
                                                            <?php if(isset($jobCategories) && $jobCategories->count()>0): ?>
                                                                <?php $__currentLoopData = $jobCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jobCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($jobCategory->id); ?>" <?php echo e(old('category_id') == $jobCategory->id ? 'selected' : ''); ?>><?php echo e($jobCategory->name); ?></option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label for="" class="form-label">Please Select States Where You Are Licensed</label>
                                                        <div class="custom_multi_select">
                                                            <select multiple class="form-control custom_multiselect" name="states_license">
                                                                <option></option>
                                                                <option value="1" data-select-attribute="AL">Alabama (AL)</option>
                                                                <option value="2" data-select-attribute="AK">Alaska (AK)</option>
                                                                <option value="3" data-select-attribute="AZ">Arizona (AZ)</option>
                                                                <option value="4" data-select-attribute="AR">Arkansas (AR)</option>
                                                                <option value="5" data-select-attribute="CA">California (CA)</option>
                                                                <option value="6" data-select-attribute="CO">Colorado (CO)</option>
                                                                <option value="7" data-select-attribute="CT">Connecticut (CT)</option>
                                                                <option value="8" data-select-attribute="DE">Delaware (DE)</option>
                                                                <option value="9" data-select-attribute="FL">Florida (FL)</option>
                                                                <option value="10" data-select-attribute="GA">Georgia (GA)</option>
                                                                <option value="11" data-select-attribute="HI">Hawaii (HI)</option>
                                                                <option value="12" data-select-attribute="ID">Idaho (ID)</option>
                                                                <option value="13" data-select-attribute="IL">Illinois (IL)</option>
                                                                <option value="14" data-select-attribute="IN">Indiana (IN)</option>
                                                                <option value="15" data-select-attribute="IA">Iowa (IA)</option>
                                                                <option value="16" data-select-attribute="KS">Kansas (KS)</option>
                                                                <option value="17" data-select-attribute="KY">Kentucky (KY)</option>
                                                                <option value="18" data-select-attribute="LA">Louisiana (LA)</option>
                                                                <option value="19" data-select-attribute="ME">Maine (ME)</option>
                                                                <option value="20" data-select-attribute="MD">Maryland (MD)</option>
                                                                <option value="21" data-select-attribute="MA">Massachusetts (MA)</option>
                                                                <option value="22" data-select-attribute="MI">Michigan (MI)</option>
                                                                <option value="23" data-select-attribute="MN">Minnesota (MN)</option>
                                                                <option value="24" data-select-attribute="MS">Mississippi (MS)</option>
                                                                <option value="25" data-select-attribute="MO">Missouri (MO)</option>
                                                                <option value="26" data-select-attribute="MT">Montana (MT)</option>
                                                                <option value="27" data-select-attribute="NE">Nebraska (NE)</option>
                                                                <option value="28" data-select-attribute="NV">Nevada (NV)</option>
                                                                <option value="29" data-select-attribute="NH">New Hampshire (NH)</option>
                                                                <option value="30" data-select-attribute="NJ">New Jersey (NJ)</option>
                                                                <option value="31" data-select-attribute="NM">New Mexico (NM)</option>
                                                                <option value="32" data-select-attribute="NY">New York (NY)</option>
                                                                <option value="33" data-select-attribute="NC">North Carolina (NC)</option>
                                                                <option value="34" data-select-attribute="ND">North Dakota (ND)</option>
                                                                <option value="35" data-select-attribute="OH">Ohio (OH)</option>
                                                                <option value="36" data-select-attribute="OK">Oklahoma (OK)</option>
                                                                <option value="37" data-select-attribute="OR">Oregon (OR)</option>
                                                                <option value="38" data-select-attribute="PA">Pennsylvania (PA)</option>
                                                                <option value="39" data-select-attribute="RI">Rhode Island (RI)</option>
                                                                <option value="40" data-select-attribute="SC">South Carolina (SC)</option>
                                                                <option value="41" data-select-attribute="SD">South Dakota (SD)</option>
                                                                <option value="42" data-select-attribute="TN">Tennessee (TN)</option>
                                                                <option value="43" data-select-attribute="TX">Texas (TX)</option>
                                                                <option value="44" data-select-attribute="UT">Utah (UT)</option>
                                                                <option value="45" data-select-attribute="VT">Vermont (VT)</option>
                                                                <option value="46" data-select-attribute="VA">Virginia (VA)</option>
                                                                <option value="47" data-select-attribute="WA">Washington (WA)</option>
                                                                <option value="48" data-select-attribute="WV">West Virginia (WV)</option>
                                                                <option value="49" data-select-attribute="WI">Wisconsin (WI)</option>
                                                                <option value="50" data-select-attribute="WY">Wyoming (WY)</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <label for="" class="form-label license_label">States License Number</label>
                                                    <div class="append_state_license"></div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label for="" class="form-label">Do they hold an insurance policy?</label>
                                                        <div class="custom_radio_wrapper">
                                                            <div class="custom_radio custom_position">
                                                                <input class="form-check-input" type="radio" name="insurance_policy" value="yes" id="flexCheckChecked1">
                                                                <label for="flexCheckChecked1"><h6>Yes</h6></label>
                                                            </div>
                                                            <div class="custom_radio">
                                                                <input class="form-check-input" type="radio" name="insurance_policy" value="no" id="flexCheckChecked2" checked>
                                                                <label for="flexCheckChecked2"><h6>No</h6></label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6"></div>
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field custom_insurance_policy">
                                                        <label for="" class="form-label">Insurer Name</label>
                                                        <input id="insurance_name" type="text" class="form-control" placeholder="Enter Insurer Name..." name="insurance_name" value="<?php echo e(old('insurance_name')); ?>" required autofocus>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-sm-6">
                                                    <div class="txt_field custom_insurance_policy">
                                                        <label for="" class="form-label">Policy Number</label>
                                                        <input id="policy_number" type="number" class="form-control" placeholder="Enter Policy Number..." name="policy_number" value="<?php echo e(old('insurance_name')); ?>" required autofocus>
                                                    </div>
                                                </div>
                                                <!-- Continue Button -->
                                                <div class="col-md-12">
                                                    <div class="submit_btn previous_step">
                                                        <button type="button" class="btn btn_black" id="step_2_continue" onclick="showStep('step3')">Continue
                                                            <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                                        </button>
                                                        <a href="<?php echo e(url('#!')); ?>" class="btn btn_black " onclick="showStep('step1')"><div class="next_btn back_btn_stepper"><i class="fa-solid fa-arrow-left"></i></div> Back</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="step" id="step3" style="display: none;">
                                            <h1>Step 3: Upload Documents</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-12">
                                                    <div class="state_license state_license_image">
                                                        <h3>State License Document</h3>
                                                        <div id="" class="custom_file_upload">
                                                            <button type="button" class="append_type_file append_btn">
                                                                <i class="fa-solid fa-plus"></i>Add
                                                            </button>
                                                        </div>
                                                            <span class="error-message"></span>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="insurance_policy insurance_policy_image">
                                                        <h3>Insurance policy</h3>
                                                        <div id="" class="custom_file_upload">
                                                            <button type="button" class="append_type_file append_btn">
                                                                <i class="fa-solid fa-plus"></i>Add
                                                            </button>
                                                        </div>
                                                    </div>
                                                        <span class="error-message"></span>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="g-recaptcha" data-sitekey="<?php echo e(env('RECAPTCHA_SITE_KEY')); ?>"></div>
                                                    <span class="text-danger"  id="recaptcha-error"></span>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="submit_btn previous_step">
                                                        <button type="submit" id="sign-up-button" class="btn btn_black disable-on-submit" >Sign Up
                                                            <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                                        </button>
                                                        <a href="<?php echo e(url('#!')); ?>" class="btn btn_black " onclick="showStep('step2')"><div class="next_btn back_btn_stepper"><i class="fa-solid fa-arrow-left"></i></div> Back</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

    <script src="<?php echo e(asset('plugins/components/dropify/dist/js/dropify.min.js')); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>


    <script>


        $(document).ready(function() {
            // Initialize Select2 for custom multiselect
            $('.custom_multiselect').select2({
                placeholder: "Select An Option",
                allowClear: true
            });

            // Initialize Datepicker
            $(".datepicker").datepicker({
                autoclose: true,
                todayHighlight: true,
                todayBtn: "linked",
            }).datepicker('update', new Date());

            $(document).keydown(function(e) {
//                console.log(showStep);
                if (e.key === "Enter" || e.keyCode === 13) {  // If ESC key is pressed
                    e.preventDefault();
                    $(".step:visible .submit_btn button.btn").trigger("click");
                }
            });

            $(".custom_insurance_policy").hide();
            $(".custom_insurance_policy input").prop('required', false);
            $(".custom_radio_wrapper .custom_radio input[type='radio']").change(function () {
                var selectedValue = $('input[name="insurance_policy"]:checked').val();
                if (selectedValue === "yes") {
                    $(".custom_insurance_policy").show();
                    $(".custom_insurance_policy input").prop('required', true);
                } else  {
                    $(".custom_insurance_policy").hide();
                    $(".custom_insurance_policy input").prop('required', false);
                };
            });
            $(document).on("click", '.append_btn', function() {
                const $parent = $(this).closest('.custom_file_upload');
                let parentClass = $(this).closest('div').parent().attr('class') || '';
                let sectionName = parentClass.split(' ')[0]; // Take the first class name
                if ($parent.find('.file-input').length) {
                    const errorMessage = $('<span class="file_size_error" style="color: red;">You can only upload one file.</span>');
                    $parent.append(errorMessage);
                    return;
                }
                $(this).closest('.custom_file_upload').append(`
            <div class="append_type_wrapper">
                <div class="append_type_file">
                    <input type="file" class="file-input" name="file[input][]" required/>
                    <input type="hidden" name="file[section][]" value="${sectionName}"/>
                    <a class="image_icon" href="#!"><i class="fa-solid fa-image"></i></a>
<!--                    <span class="error-message file_size_error" >File size must not exceeds 5MB</span>-->
                    <button class="close-btn append_img_div_remove"><i class="fa-solid fa-close"></i></button>
                </div>
            </div>`
                );
            });

            // Handle click on close button to remove the uploaded file input div
            $(document).on("click", ".append_type_wrapper .append_img_div_remove", function() {
                $(this).closest(".append_type_wrapper").remove();

            });

            // Handle file input change to show image preview
            $(document).on("change", ".append_type_wrapper .append_type_file input[type='file']", function(event) {
                const file = $(this)[0].files[0];
                const fileType = file.type;
                const $parentDiv = $(this).closest('.append_type_file');
                console.log("file selected" , file)
                if (file) {
                    const reader = new FileReader();
                    const {size} = file;
                    if (size > 5 * 1024 * 1024) {
                        // alert('file size exceeds 5MB');
                        const errorMessage = $('<span class="file_size_error" style="color: red;">File size exceeds 5MB!</span>');
                        $parentDiv.append(errorMessage); // Append the error message to the parent div
                        $(this).val('');
                        return;
                    }else{
                        $parentDiv.find('.file_size_error').remove();
                    }
                    // Check file type
                    if (fileType.startsWith('image/')) {
                        // Handle image file
                        const imageURL = URL.createObjectURL(file);
                        const $parentDiv = $(this).closest(".append_type_file");
                        // Remove existing image preview before appending a new one
                        $parentDiv.find(".image_structure").remove();
                        // Append image preview
                        $parentDiv.append('<div class="image_structure"><img src='+ imageURL +' class="image_preview" /></div>');
                    }else if (fileType == 'application/pdf') {
                        const pdfURL = URL.createObjectURL(file);
                        const $parentDiv = $(this).closest(".append_type_file");
                        // Remove existing image preview before appending a new one
                        $parentDiv.find(".image_structure").remove();
                        $parentDiv.append('<div class="image_structure">' +
                            '<div class="pdf_img"><img src="<?php echo e(asset('website')); ?>/assets/images/pdf_img.png" alt="PDF" class="pdf_control"></div>' +
                            '<a href='+ pdfURL +' target="_blank" class="preview_pdf">View PDF</a></div>');
                    } else if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                        // Handle DOCX file
                        const docxURL = URL.createObjectURL(file);
                        const $parentDiv = $(this).closest(".append_type_file");
                        // Remove existing image preview before appending a new one
                        $parentDiv.find(".image_structure").remove();
                        $parentDiv.append('<div class="image_structure">' +
                            '<div class="docx_img"><img src="<?php echo e(asset('website')); ?>/assets/images/docx_img.png" alt="DOCX" class="docx_control"></div>' +
                            '<a href='+ docxURL +' target="_blank" class="preview_docx">View DOCX</a></div>');
                    }
                }
            });
        });






    </script>

    <script>
        $(function() {
            $('.dropify').dropify();
        });
    </script>
    <script>
        
        
        
        
        

        
        
        
        
        
        
        
        
        

        var contractorInput = document.getElementById("contractor_address");
        var clientInput = document.getElementById("address_client");
            // initGoogleMaps()
        // if(contractorInput) {
        //     initGoogleMaps()
        // }else{
        //     initGoogleMaps()
        // }


        // function initGoogleMaps() {
        function initAutocomplete() {
            if(contractorInput){
             // google.maps.event.addDomListener(window, 'load', function () {
                var places = new google.maps.places.Autocomplete(document.getElementById('contractor_address'));
                google.maps.event.addListener(places, 'place_changed', function () {
                    var place = places.getPlace();
                    // Extract latitude and longitude
                    var latitude = place.geometry.location.lat();
                    var longitude = place.geometry.location.lng();
                    // Assign latitude and longitude to hidden input fields
                    document.getElementById('contractor_latitude').value = latitude;
                    document.getElementById('contractor_longitude').value = longitude;
                    // Extract address components
                    var addressComponents = place.address_components;
                    let city = '';
                    let state = '';
                    let postcode = '';
                    let country = '';
                    addressComponents.forEach((component) => {
                        const types = component.types;
                        if (types.includes('locality')) {
                            city = component.long_name; // City
                        }

                        if (types.includes('administrative_area_level_1')) {
                            state = component.long_name; // State
                        }

                        if (types.includes('postal_code')) {
                            postcode = component.long_name; // Zip Code
                        }

                        if (types.includes('country')) {
                            country = component.long_name; // Country
                        }
                    });

                    // Assign extracted values to their respective fields
                    document.getElementById('contractor_city').value = city || '';
                    document.getElementById('contractor_state').value = state || '';
                    document.getElementById('contractor_zip_code').value = postcode || '';
                    document.getElementById('contractor_country').value = country || '';

                });
            // });

            }

            if(clientInput){
                // google.maps.event.addDomListener(window, 'load', function () {
                    var places = new google.maps.places.Autocomplete(document.getElementById('address_client'));
                    google.maps.event.addListener(places, 'place_changed', function () {
                        var place = places.getPlace();

                        // Get latitude and longitude
                        var latitude = place.geometry.location.lat();
                        var longitude = place.geometry.location.lng();

                        // Assign latitude and longitude to hidden fields
                        if (document.getElementById('latitude')) {
                            document.getElementById('latitude').value = latitude;
                        }
                        if (document.getElementById('longitude')) {
                            document.getElementById('longitude').value = longitude;
                        }

                        // Initialize Geocoder to fetch detailed address components
                        var geocoder = new google.maps.Geocoder();
                        var latlng = new google.maps.LatLng(latitude, longitude);

                        geocoder.geocode({'latLng': latlng}, function (results, status) {
                            if (status == google.maps.GeocoderStatus.OK) {
                                if (results[0]) {
                                    var addressComponents = results[0].address_components;
                                    var city = '';
                                    var state = '';
                                    var postcode = '';
                                    var country = '';
                                    var subpremise = ''; // Apt, Suite, Floor

                                    // Loop through address components to extract data
                                    addressComponents.forEach((component) => {
                                        const types = component.types;

                                        if (types.includes('locality')) {
                                            city = component.long_name; // City
                                        }

                                        if (types.includes('administrative_area_level_1')) {
                                            state = component.long_name; // State
                                        }

                                        if (types.includes('postal_code')) {
                                            postcode = component.long_name; // Zip Code
                                        }

                                        if (types.includes('country')) {
                                            country = component.long_name; // Country
                                        }

                                        if (types.includes('subpremise')) {
                                            subpremise = component.long_name; // Apt, Suite, Floor
                                        }
                                    });

                                    // Assign extracted values to respective fields
                                    if (document.getElementById('state')) {
                                        document.getElementById('state').value = state || '';
                                    }
                                    if (document.getElementById('city')) {
                                        document.getElementById('city').value = city || '';
                                    }
                                    if (document.getElementById('zip_code')) {
                                        document.getElementById('zip_code').value = postcode || '';
                                    }
                                    if (document.getElementById('country')) {
                                        document.getElementById('country').value = country || '';
                                    }
                                    if (document.getElementsByName('suite_or_floor')[0]) {
                                        document.getElementsByName('suite_or_floor')[0].value = subpremise || '';
                                    }
                                }
                            }
                        });
                    });
                // });
            }

        }
// ---------------------------------------------------GoogleApi End---------------------------------------------------



$(document).ready(function(){
    $('#client_email').on('keyup blur', function() {
        var email = $(this).val();
        var emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        var invalidDomainPattern = /@(.*null.*|.*\.null)$/;

        // Check if the email is valid or if it contains a "null" domain
        if (invalidDomainPattern.test(email) || !emailPattern.test(email)) {
            $(this).addClass('is-invalid'); // Add invalid class to the input
            // Only add invalid feedback message if it's not already there
            if ($(this).next('.invalid-feedback').length === 0) {
                $(this).after('<span class="invalid-feedback" style="color:red; display:block;">Invalid email format.</span>');
            }
            $('.btn_sign_up').prop('disabled', true); // Disable the Sign Up button
        } else {
            $(this).removeClass('is-invalid'); // Remove invalid class
            $(this).next('.invalid-feedback').remove(); // Remove the error message
            $('.btn_sign_up').prop('disabled', false); // Enable the Sign Up button
        }
    });
});

        $('form').on('submit', function (e) {
            var password = $('#password').val();
            var passwordConfirmation = $('.password_confirmation').val();
            $('.error-message').remove();
            if (password !== passwordConfirmation) {
                e.preventDefault();
                // $('.password_confirmation').after('<span class="error-message" style="color:red;">Passwords do not match</span>');
                $('.password_confirmation').after('<span class="error-message" style="color:red;"></span>');
            }
        });

        // $('.password_confirmation').on('input', function() {
        //     var password = $('#password').val();
        //     var passwordConfirmation = $('.password_confirmation').val();
        //     $('.password_confirmation').next('.error-message').remove();
        //     if (password === passwordConfirmation) {
        //         $('.password_confirmation').after('.error-message').remove();
        //     }
        // });

        function showStep(stepId) {

            console.log(stepId);

            var isValid = true;
            $('.error-message').remove();
            $('.step:visible').find('input[required], select[required], textarea[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('error');
                    $(this).after('<span class="error-message" style="color:red;">This field is required</span>');
                } else {
                    $(this).removeClass('error');
                }
            });

            // var password = $('input[name="password"]').val().trim();
            // var passwordConfirmation = $('#password_confirmation_contractor').val().trim();
            //
            // if (password && passwordConfirmation && password !== passwordConfirmation) {
            //     isValid = false;
            //     $('#password_confirmation_contractor').addClass('error');
            //     $('#password_confirmation_contractor').after('<span class="error-message" style="color:red;"></span>');
            //     $('#password_confirmation_contractor').after('<span class="error-message" style="color:red;">Passwords do not match</span>');
            // }

            if (isValid) {
                document.querySelectorAll('.step').forEach(function(step) {
                    step.style.display = 'none';
                });
                document.getElementById(stepId).style.display = 'block';
            }
        }

        $('.step input[required], .step select[required], .step textarea[required]').on('input change', function() {
            if ($(this).val()) {
                $(this).removeClass('error');
                $(this).next('.error-message').remove();
            }
        });

        // $('#password_confirmation_contractor').on('input', function() {
        //     var password = $('input[name="password"]').val().trim();
        //     var passwordConfirmation = $(this).val().trim();
        //     if (password === passwordConfirmation) {
        //         $(this).removeClass('error');
        //         $(this).next('.error-message').remove();
        //     } else {
        //         $(this).addClass('error');
        //         if (!$(this).next('.error-message').length) {
        //             $(this).after('<span class="error-message" style="color:red;">Passwords do not match</span>');
        //         }
        //     }
        // });


        $(document).ready(function () {
            $("#sign-up-button").on("click", function (e) {
                let isValid = true;
                $(".error-message").remove();
                let stateLicenseInput = $(".state_license .custom_file_upload").find("input[type='file']");
                if (stateLicenseInput.length === 0 || stateLicenseInput[0].files.length === 0) {
                    $(".state_license").append('<span class="error-message" style="color: red;">State License document is required.</span>');
                    isValid = false;
                }
                let insurancePolicyInput = $(".insurance_policy .custom_file_upload").find("input[type='file']");
                if (insurancePolicyInput.length === 0 || insurancePolicyInput[0].files.length === 0) {
                    $(".insurance_policy").append('<span class="error-message" style="color: red;">Insurance Policy document is required.</span>');
                    isValid = false;
                }
                if (!isValid) {
                    e.preventDefault();
                }
            });
            $(document).on("change", ".custom_file_upload input[type='file']", function () {
                $(this).closest(".col-md-12").find(".error-message").remove();
            });
        });



        $(document).ready(function () {
            $('#step_2_continue').prop('disabled', true);
            // Function to check if any state license number input is filled
            function checkStateLicenseInput() {
                let anyFilled = false;
                // Loop through all state license number inputs
                $('.state_license_input').each(function () {
                    if (String($(this).val()).trim() !== "") {
                        anyFilled = true;
                        return false;
                    }
                });
                return anyFilled;
            }
            function checkStatesSelected() {
                return $('.custom_multi_select').val() && $('.custom_multi_select').val().length > 0;
            }
            function toggleContinueButton() {
                if (checkStatesSelected() || checkStateLicenseInput()) {
                    $('#step_2_continue').prop('disabled', false);
                } else {
                    $('#step_2_continue').prop('disabled', true);
                }
            }
            // Append State License Number Based on Select Option
            $('.custom_multi_select').on('change', function () {
                $('.append_state_license').empty();
                $(this).find('option:selected').each(function () {
                    var stateType = $(this).data('select-attribute');
                    $('.append_state_license').append('<div class="custom_add_license">' +
                        '<div class="state_key_initial"><span class="data_attribute_key">' + stateType + '</span></div>' +
                        '<div class="txt_field"><input type="number" class="form-control state_license_input" placeholder="Enter States License Number..." name="states_license_number" required autofocus> </div>' +
                        '</div>');
                });
                toggleContinueButton();
            });
            $(document).on('input', '.state_license_input', function () {
                toggleContinueButton();
            });
            toggleContinueButton();
            $('#step_2_continue').on('click', function () {
                if (checkStatesSelected() || checkStateLicenseInput()) {
                    showStep('step3');
                    $('#step_2_continue').prop('disabled', true);
                    $('.continue_error').hide();
                } else {
                    $('.continue_error')
                        .text("Please select a state or enter a license number.")
                        .show();
                }
            });
        });

        $(document).ready(function() {
            $('#email').on('keyup blur', function() {
                var email = $(this).val();
                var emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
                var invalidDomainPattern = /@(.*null.*|.*\.null)$/;
                if (invalidDomainPattern.test(email) || !emailPattern.test(email)) {
                    $(this).addClass('is-invalid'); // Add invalid class
                    if ($(this).next('.invalid-feedback').length === 0) {
                        $(this).after('<span class="invalid-feedback" style="color:red; display:block;">Invalid email format.</span>');
                    }
                    $('#step_one_btn').prop('disabled', true);
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                    $('#step_one_btn').prop('disabled', false);
                }
            });

        });
        $(document).ready(function() {
            // Custom password validation method
            $.validator.addMethod("strongPassword", function(value, element) {
                return this.optional(element) || /[A-Z]/.test(value) && /\d/.test(value) && /[!@#$%^&*()_+={}\[\]|\\;:'",<>\./?]/.test(value);
            }, "Password must be at least 8 characters, contain one uppercase letter, one number, and one special character.");

            // Custom confirm password validation method
            $.validator.addMethod("passwordsMatch", function(value, element) {
                return value === $('#password').val();
            }, "Passwords do not match.");

            $.validator.addMethod("recaptchaValid", function () {
                return grecaptcha.getResponse().length !== 0;
            }, "Please verify that you are not a robot.");

            $.validator.addMethod("validPhone", function (value, element) {
                return this.optional(element) || /^[0-9]{7,15}$/.test(value);
            }, "Please enter a valid phone number (7–15 digits)");


            $('#loginform').validate({
                ignore: [],
                rules: {

                    password: {
                        required: true,
                        strongPassword: true,
                        minlength: 8
                    },
                    password_confirmation: {
                        required: true,
                        passwordsMatch: true
                    },
                    "g-recaptcha-response": {
                        recaptchaValid: true
                    },
                    phone:{
                        required: true,
                        validPhone: true
                    },
                },
                messages: {
                    password: {
                        required: "Please enter your password.",
                        minlength: "Your password must be at least 8 characters long."
                    },
                    password_confirmation: {
                        required: "Please confirm your password.",
                        passwordsMatch: "The confirmation password must match the password."
                    },
                    "g-recaptcha-response": {
                        recaptchaValid: "Please verify that you are not a robot."
                    },
                    phone: {
                        required: "Please enter your phone number.",
                        validPhone: "Please enter a valid phone number (7–15 digits)."
                    }
                },
                errorPlacement: function(error, element) {
                    if (element.attr("name") == "g-recaptcha-response") {
                        error.appendTo("#recaptcha-error");
                    } else {
                        error.insertAfter(element);
                    }
                    error.insertAfter(element);
                },
                submitHandler: function(form) {
                    $(form).find('.disable-on-submit').prop('disabled', true).addClass('disabled');
                    $(form).find('.disable-on-submit').text('Submitting...');
                    form.submit();
                },
                invalidHandler: function(event, validator) {
                    $(event.target).find('.disable-on-submit').prop('disabled', false).removeClass('disabled');
                    // $('#step_one_btn').prop('disabled', true);
                }
            });
            $('#password, #password_confirmation').on('keyup', function() {
                if ($('#password').val() === $('#password_confirmation').val()) {
                    // If passwords match, trigger re-validation to remove the error
                    $('#loginform').valid();
                }
            });

            $('#login_cont_form').validate({
                ignore: [],
                rules: {
                    password: {
                        required: true,
                        strongPassword: true,
                        minlength: 8
                    },
                    password_confirmation: {
                        required: true,
                        passwordsMatch: true,
                        equalTo: "#password"
                    },
                    "g-recaptcha-response": {
                        recaptchaValid: "Please verify that you are not a robot."
                    },
                    phone: {
                        required: true,
                        validPhone: true
                    }
                },
                messages: {
                    password: {
                        required: "Please enter your password.",
                        minlength: "Your password must be at least 8 characters long."
                    },
                    password_confirmation: {
                        required: "Please confirm your password.",
                        passwordsMatch: "The confirmation password must match the password."
                    },
                    "g-recaptcha-response": {
                        recaptchaValid: "Please verify that you are not a robot."
                    },
                    phone: {
                        required: "Please enter your phone number.",
                        validPhone: "Please enter a valid phone number (7–15 digits)."
                    }
                },
                errorPlacement: function(error, element) {
                    if (element.attr("name") == "g-recaptcha-response") {
                        error.appendTo("#recaptcha-error");
                    } else {
                        error.insertAfter(element);
                    }
                    error.insertAfter(element);
                },
                submitHandler: function(form) {
                    $(form).find('.disable-on-submit').prop('disabled', true).addClass('disabled');
                    $(form).find('.disable-on-submit').text('Submitting...');
                    form.submit(); // submit the form
                },
                invalidHandler: function(event, validator) {
                    $(event.target).find('.disable-on-submit').prop('disabled', false).removeClass('disabled');
                    $('.step_one_btn').prop('disabled', true);
                }
            });
            // Listen for keyup on both password fields to check if they match
            $('#password, #password_confirmation_contractor').on('keyup', function () {
                // Check if passwords match
                if ($('#password').val() === $('#password_confirmation_contractor').val()) {
                    // If passwords match, remove the error message
                    $('#password_confirmation_contractor').removeClass('is-invalid');
                    $('#password_confirmation_contractor').next('.invalid-feedback').hide(); // Hide the error message
                } else {
                    // If passwords do not match, show the error message
                    $('#password_confirmation_contractor').addClass('is-invalid');
                    $('#password_confirmation_contractor').next('.invalid-feedback').show(); // Show the error message
                }
            });


            // On input in the password confirmation field, check for matching password


            // // Handle password visibility toggle
            // $('.password_eye').on('click', function() {
            //     var passwordField = $(this).prev('input');
            //     var type = passwordField.attr('type') === 'password' ? 'text' : 'password';
            //     passwordField.attr('type', type);
            //     $(this).toggleClass('fa-eye fa-eye-slash');
            // });
        });

        $(document).ready(function () {
            $(".g-recaptcha").submit(function (event) {
                var recaptcha = grecaptcha.getResponse();
                if (recaptcha.length === 0) {
                    event.preventDefault();
                    $("#recaptcha-error").text("Please verify that you are not a robot.");
                    return false;
                } else {
                    $("#recaptcha-error").text("");
                }
            });
        });

    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/auth/register.blade.php ENDPATH**/ ?>