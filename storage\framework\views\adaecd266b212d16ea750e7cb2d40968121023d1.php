<?php $__env->startPush('css'); ?>
    <style>
        .info-box .info-count {
            margin-top: 0px !important;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <?php if(auth()->user()->hasRole('admin')): ?>
            <h2 id="">Hello, Admin </h2>
        <?php elseif(auth()->user()->hasRole('user')): ?>
            <h2 id="">Hello, <?php echo e(auth()->user()->first_name ??''); ?></h2>
        <?php else: ?>
            <h2 id="">Hello, <?php echo e(auth()->user()->name ??''); ?></h2>
       <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin')): ?>
        <div class="row m-0">

            <div class="col-md-3 col-sm-6 info-box">
                <div class="media">
                    <div class="media-left">
                        <span class="icoleaf bg-primary text-white"><i
                                    class="mdi mdi-checkbox-marked-circle-outline"></i></span>
                    </div>
                    <div class="media-body">
                        <h3 class="info-count text-blue">154</h3>
                        <p class="info-text font-12">Bookings</p>
                        <span class="hr-line"></span>
                        <p class="info-ot font-15">Target<span class="label label-rounded label-success">300</span></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 info-box">
                <div class="media">
                    <div class="media-left">
                        <span class="icoleaf bg-primary text-white"><i class="mdi mdi-comment-text-outline"></i></span>
                    </div>
                    <div class="media-body">
                        <h3 class="info-count text-blue">68</h3>
                        <p class="info-text font-12">Complaints</p>
                        <span class="hr-line"></span>
                        <p class="info-ot font-15">Total Pending<span
                                    class="label label-rounded label-danger">154</span></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 info-box">
                <div class="media">
                    <div class="media-left">
                        <span class="icoleaf bg-primary text-white"><i class="mdi mdi-coin"></i></span>
                    </div>
                    <div class="media-body">
                        <h3 class="info-count text-blue">&#36;9475</h3>
                        <p class="info-text font-12">Earning</p>
                        <span class="hr-line"></span>
                        <p class="info-ot font-15">March : <span class="text-blue font-semibold">&#36;514578</span></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 info-box b-r-0">
                <div class="media">
                    <div class="media-left p-r-5">
                        <div id="earning" class="e" data-percent="60">
                            <div id="pending" class="p" data-percent="55"></div>
                            <div id="booking" class="b" data-percent="50"></div>
                        </div>
                    </div>
                    <div class="media-body">
                        <h2 class="text-blue font-22 m-t-0">Report</h2>
                        <ul class="p-0 m-b-20">
                            <li><i class="fa fa-circle m-r-5 text-primary"></i>60% Earnings</li>
                            <li><i class="fa fa-circle m-r-5 text-primary"></i>55% Pending</li>
                            <li><i class="fa fa-circle m-r-5 text-info"></i>50% Bookings</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8 col-sm-12">
                    <div class="white-box stat-widget">
                        <div class="row">
                            <div class="col-md-3 col-sm-3">
                                <h4 class="box-title">Statistics</h4>
                            </div>
                            <div class="col-md-9 col-sm-9">
                                <select class="custom-select">
                                    <option selected value="0">Feb 04 - Mar 03</option>
                                    <option value="1">Mar 04 - Apr 03</option>
                                    <option value="2">Apr 04 - May 03</option>
                                    <option value="3">May 04 - Jun 03</option>
                                </select>
                                <ul class="list-inline">
                                    <li>
                                        <h6 class="font-15"><i class="fa fa-circle m-r-5 text-success"></i>New Sales
                                        </h6>
                                    </li>
                                    <li>
                                        <h6 class="font-15"><i class="fa fa-circle m-r-5 text-primary"></i>Existing
                                            Sales</h6>
                                    </li>
                                </ul>
                            </div>
                            <div class="stat chart-pos"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="white-box">
                        <h4 class="box-title">Task Progress</h4>
                        <div class="task-widget t-a-c">
                            <div class="task-chart" id="sparklinedashdb"></div>
                            <div class="task-content font-16 t-a-c">
                                <div class="col-sm-6 b-r">
                                    Urgent Tasks
                                    <h1 class="text-primary">05 <span class="font-16 text-muted">Tasks</span></h1>
                                </div>
                                <div class="col-sm-6">
                                    Normal Tasks
                                    <h1 class="text-primary">03 <span class="font-16 text-muted">Tasks</span></h1>
                                </div>
                            </div>
                            <div class="task-assign font-16">
                                Assigned To
                                <ul class="list-inline">
                                    <li class="p-l-0">
                                        <img src="<?php echo e(asset('plugins/images/users/1.png')); ?>" alt="user"
                                             data-toggle="tooltip"
                                             data-placement="top" title="" data-original-title="Steave">
                                    </li>
                                    <li>
                                        <img src="<?php echo e(asset('plugins/images/users/2.png')); ?>" alt="user"
                                             data-toggle="tooltip"
                                             data-placement="top" title="" data-original-title="Steave">
                                    </li>
                                    <li>
                                        <img src="<?php echo e(asset('plugins/images/users/3.png')); ?>" alt="user"
                                             data-toggle="tooltip"
                                             data-placement="top" title="" data-original-title="Steave">
                                    </li>
                                    <li class="p-r-0">
                                        <a href="javascript:void(0);" class="btn btn-success font-16">3+</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 col-sm-12">
                    <div class="white-box bg-primary color-box">
                        <h1 class="text-white font-light">&#36;6547 <span class="font-14">Revenue</span></h1>
                        <div class="ct-revenue chart-pos"></div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="white-box bg-success color-box">
                        <h1 class="text-white font-light m-b-0">5247</h1>
                        <span class="hr-line"></span>
                        <p class="cb-text">current visits</p>
                        <h6 class="text-white font-semibold">+25% <span class="font-light">Last Week</span></h6>
                        <div class="chart">
                            <div class="ct-visit chart-pos"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="white-box bg-danger color-box">
                        <h1 class="text-white font-light m-b-0">25%</h1>
                        <span class="hr-line"></span>
                        <p class="cb-text">Finished Tasks</p>
                        <h6 class="text-white font-semibold">+15% <span class="font-light">Last Week</span></h6>
                        <div class="chart">
                            <input class="knob" data-min="0" data-max="100" data-bgColor="#f86b4a"
                                   data-fgColor="#ffffff" data-displayInput=false data-width="96" data-height="96"
                                   data-thickness=".1" value="25" readonly>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="white-box user-table">
                        <div class="row">
                            <div class="col-sm-6">
                                <h4 class="box-title">Table Format/User Data</h4>
                            </div>
                            <div class="col-sm-6">
                                <ul class="list-inline">
                                    <li>
                                        <a href="javascript:void(0);" class="btn btn-default btn-outline font-16"><i
                                                    class="fa fa-trash" aria-hidden="true"></i></a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" class="btn btn-default btn-outline font-16"><i
                                                    class="fa fa-commenting" aria-hidden="true"></i></a>
                                    </li>
                                </ul>
                                <select class="custom-select">
                                    <option selected>Sort by</option>
                                    <option value="1">Name</option>
                                    <option value="2">Location</option>
                                    <option value="3">Type</option>
                                    <option value="4">Role</option>
                                    <option value="5">Action</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="checkbox checkbox-info">
                                            <input id="c1" type="checkbox">
                                            <label for="c1"></label>
                                        </div>
                                    </th>
                                    <th>Name</th>
                                    <th>Location</th>
                                    <th>Type</th>
                                    <th>Role</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>
                                        <div class="checkbox checkbox-info">
                                            <input id="c2" type="checkbox">
                                            <label for="c2"></label>
                                        </div>
                                    </td>
                                    <td><a href="javascript:void(0);" class="text-link">Daniel Kristeen</a></td>
                                    <td>Texas, US</td>
                                    <td>Posts 564</td>
                                    <td><span class="label label-success">Admin</span></td>
                                    <td>
                                        <select class="custom-select">
                                            <option value="1">Modulator</option>
                                            <option value="2">Admin</option>
                                            <option value="3">Staff</option>
                                            <option value="4">User</option>
                                            <option value="5">General</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="checkbox checkbox-info">
                                            <input id="c3" type="checkbox">
                                            <label for="c3"></label>
                                        </div>
                                    </td>
                                    <td><a href="javascript:void(0);" class="text-link">Hanna Gover</a></td>
                                    <td>Los Angeles, US</td>
                                    <td>Posts 451</td>
                                    <td><span class="label label-info">Staff</span></td>
                                    <td>
                                        <select class="custom-select">
                                            <option value="1">Modulator</option>
                                            <option value="2">Admin</option>
                                            <option value="3">Staff</option>
                                            <option value="4">User</option>
                                            <option value="5">General</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="checkbox checkbox-info">
                                            <input id="c4" type="checkbox">
                                            <label for="c4"></label>
                                        </div>
                                    </td>
                                    <td><a href="javascript:void(0);" class="text-link">Jeffery Brown</a></td>
                                    <td>Houston, US</td>
                                    <td>Posts 978</td>
                                    <td><span class="label label-danger">User</span></td>
                                    <td>
                                        <select class="custom-select">
                                            <option value="1">Modulator</option>
                                            <option value="2">Admin</option>
                                            <option value="3">Staff</option>
                                            <option value="4">User</option>
                                            <option value="5">General</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="checkbox checkbox-info">
                                            <input id="c5" type="checkbox">
                                            <label for="c5"></label>
                                        </div>
                                    </td>
                                    <td><a href="javascript:void(0);" class="text-link">Elliot Dugteren</a></td>
                                    <td>San Antonio, US</td>
                                    <td>Posts 34</td>
                                    <td><span class="label label-warning">General</span></td>
                                    <td>
                                        <select class="custom-select">
                                            <option value="1">Modulator</option>
                                            <option value="2">Admin</option>
                                            <option value="3">Staff</option>
                                            <option value="4">User</option>
                                            <option value="5">General</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="checkbox checkbox-info">
                                            <input id="c6" type="checkbox">
                                            <label for="c6"></label>
                                        </div>
                                    </td>
                                    <td><a href="javascript:void(0);" class="text-link">Sergio Milardovich</a></td>
                                    <td>Jacksonville, US</td>
                                    <td>Posts 31</td>
                                    <td><span class="label label-primary">Partial</span></td>
                                    <td>
                                        <select class="custom-select">
                                            <option value="1">Modulator</option>
                                            <option value="2">Admin</option>
                                            <option value="3">Staff</option>
                                            <option value="4">User</option>
                                            <option value="5">General</option>
                                        </select>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <ul class="pagination">
                            <li class="disabled"><a href="#">1</a></li>
                            <li class="active"><a href="#">2</a></li>
                            <li><a href="#">3</a></li>
                            <li><a href="#">4</a></li>
                            <li><a href="#">5</a></li>
                        </ul>
                        <a href="javascript:void(0);" class="btn btn-success pull-right m-t-10 font-20">+</a>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="white-box">
                        <div class="task-widget2">
                            <div class="task-image">
                                <img src="<?php echo e(asset('plugins/images/task.jpg')); ?>" alt="task" class="img-responsive">
                                <div class="task-image-overlay"></div>
                                <div class="task-detail">
                                    <h2 class="font-light text-white m-b-0">07 April</h2>
                                    <h4 class="font-normal text-white m-t-5">Your tasks for today</h4>
                                </div>
                                <div class="task-add-btn">
                                    <a href="javascript:void(0);" class="btn btn-success">+</a>
                                </div>
                            </div>
                            <div class="task-total">
                                <p class="font-16 m-b-0"><strong>5</strong> Tasks for <a href="javascript:void(0);"
                                                                                         class="text-link">Jon Doe</a>
                                </p>
                            </div>
                            <div class="task-list">
                                <ul class="list-group">
                                    <li class="list-group-item bl-info">
                                        <div class="checkbox checkbox-success">
                                            <input id="c7" type="checkbox">
                                            <label for="c7">
                                                <span class="font-16">Create invoice for customers and email each customers.</span>
                                            </label>
                                            <h6 class="p-l-30 font-bold">05:00 PM</h6>
                                        </div>
                                    </li>
                                    <li class="list-group-item bl-warning">
                                        <div class="checkbox checkbox-success">
                                            <input id="c8" type="checkbox" checked>
                                            <label for="c8">
                                                <span class="font-16">Send payment of <strong>&#36;500 invoised</strong> on 23 May to <a
                                                            href="javascript:void(0);"
                                                            class="text-link">Daniel Kristeen</a> via paypal.</span>
                                            </label>
                                            <h6 class="p-l-30 font-bold">03:00 PM</h6>
                                        </div>
                                    </li>
                                    <li class="list-group-item bl-danger">
                                        <div class="checkbox checkbox-success">
                                            <input id="c9" type="checkbox">
                                            <label for="c9">
                                                <span class="font-16">It is a long established fact that a reader will be distracted by the readable.</span>
                                            </label>
                                            <h6 class="p-l-30 font-bold">04:45 PM</h6>
                                        </div>
                                    </li>
                                    <li class="list-group-item bl-success">
                                        <div class="checkbox checkbox-success">
                                            <input id="c10" type="checkbox">
                                            <label for="c10">
                                                <span class="font-16">It is a long established fact that a reader will be distracted by the readable.</span>
                                            </label>
                                            <h6 class="p-l-30 font-bold">05:30 PM</h6>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="task-loadmore">
                                <a href="javascript:void(0);" class="btn btn-default btn-outline btn-rounded">Load
                                    More</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="white-box chat-widget">
                        <a href="javascript:void(0);" class="pull-right"><i class="icon-settings"></i></a>
                        <h4 class="box-title">Chat</h4>
                        <ul class="chat-list slimscroll" style="overflow: hidden;" tabindex="5005">
                            <li>
                                <div class="chat-image"><img alt="male"
                                                             src="<?php echo e(asset('plugins/images/users/hanna.jpg')); ?>"></div>
                                <div class="chat-body">
                                    <div class="chat-text">
                                        <p><span class="font-semibold">Hanna Gover</span> Hey Daniel, This is just a
                                            sample chat. </p>
                                    </div>
                                    <span>2 Min ago</span>
                                </div>
                            </li>
                            <li class="odd">
                                <div class="chat-body">
                                    <div class="chat-text">
                                        <p> buddy </p>
                                    </div>
                                    <span>2 Min ago</span>
                                </div>
                            </li>
                            <li>
                                <div class="chat-image"><img alt="male"
                                                             src="<?php echo e(asset('plugins/images/users/hanna.jpg')); ?>"></div>
                                <div class="chat-body">
                                    <div class="chat-text">
                                        <p><span class="font-semibold">Hanna Gover</span> Bye now. </p>
                                    </div>
                                    <span>1 Min ago</span>
                                </div>
                            </li>
                            <li class="odd">
                                <div class="chat-body">
                                    <div class="chat-text">
                                        <p> We have been busy all the day to make your website proposal and finally came
                                            with the super excited offer. </p>
                                    </div>
                                    <span>5 Sec ago</span>
                                </div>
                            </li>
                        </ul>
                        <div class="chat-send">
                            <input type="text" class="form-control" placeholder="Write your message">
                            <i class="fa fa-camera"></i>
                        </div>
                    </div>
                </div>
            </div>
            <!-- ===== Right-Sidebar ===== -->
                
            <!-- ===== Right-Sidebar-End ===== -->
        </div>
    <?php elseif(auth()->user()->hasRole('user')): ?>
        <section class="projects_wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="ewallet_sales">
                            <div class="custom_sales">
                                <h3>Sales Statistics</h3>
                            </div>
                            <div class="statistics_card">
                                <div class="row">
                                    <div class="col-md-3 col-sm-6 custom_stats_column">
                                        <div class="custom_card">
                                            <h5>Total Sales</h5>
                                            <div class="total_sales_graph">
                                                <div class="chart_img">
                                                    <svg width="0" height="0">
                                                        <defs>
                                                            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                                                <stop offset="0%" style="stop-color:rgba(34, 128, 194, 0.25); stop-opacity:1" />
                                                                <stop offset="100%" style="stop-color:rgba(34, 128, 194, 0); stop-opacity:1" />
                                                            </linearGradient>
                                                        </defs>
                                                    </svg>
                                                    <span class="peity-line1" data-width="100%" data-height="60">6,2,8,4,3,8,1,3,6,5,9,2,8,1,4,8,9,8,2,1</span>
                                                </div>
                                            </div>
                                            <h3>$<?php echo e(number_format($totalSales ?? 0, 2)); ?></h3>

                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6 custom_stats_column">
                                        <div class="custom_card">
                                            <h5>Open Projects</h5>
                                            <div class="chart_img">
                                                <svg width="0" height="0">
                                                    <defs>
                                                        <linearGradient id="lineGradient2" x1="0%" y1="0%" x2="0%" y2="100%">
                                                            <stop offset="0%" style="stop-color:rgba(119, 185, 42, 0.25); stop-opacity:1" />
                                                            <stop offset="100%" style="stop-color:rgba(119, 185, 42, 0); stop-opacity:1" />
                                                        </linearGradient>
                                                    </defs>
                                                </svg>
                                                <span class="peity-line2" data-width="100%" data-height="60">1,2,8,9,8,4,1,8,2,9,5,6,3,1,8,4,8,2,6</span>
                                            </div>
                                            <h3><?php echo e($openProjectPrice ?? '0'); ?></h3>

                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6 custom_stats_column">
                                        <div class="custom_card">
                                            <h5>Closed Projects</h5>
                                            <div class="chart_img">
                                                <svg width="0" height="0">
                                                    <defs>
                                                        <linearGradient id="lineGradient3" x1="0%" y1="0%" x2="0%" y2="100%">
                                                            <stop offset="0%" style="stop-color:rgba(228, 2, 85, 0.25); stop-opacity:1" />
                                                            <stop offset="100%" style="stop-color:rgba(228, 2, 85, 0); stop-opacity:1" />
                                                        </linearGradient>
                                                    </defs>
                                                </svg>
                                                <span class="peity-line3" data-width="100%" data-height="60">6,2,8,4,3,8,1,3,6,5,9,2,8,1,4,8,9,8,2,1</span>
                                            </div>
                                            <h3><?php echo e($closedProjectPrice ?? '0'); ?></h3>

                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6 custom_stats_column">
                                        <div class="custom_card">
                                            <h5>Gross Profit</h5>
                                            <div class="chart_img">
                                                <svg width="0" height="0">
                                                    <defs>
                                                        <linearGradient id="lineGradient4" x1="0%" y1="0%" x2="0%" y2="100%">
                                                            <stop offset="0%" style="stop-color:rgba(246, 166, 0, 0.25); stop-opacity:1" />
                                                            <stop offset="100%" style="stop-color:rgba(246, 166, 0, 0); stop-opacity:1" />
                                                        </linearGradient>
                                                    </defs>
                                                </svg>
                                                <span class="peity-line4" data-width="100%" data-height="60">6,2,8,4,3,8,1,3,6,5,9,2,8,1,4,8,9,8,2,1</span>
                                            </div>
                                            <h3>$<?php echo e($adminComm??0); ?></h3>

                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="active_sales">
                                            <h5>Active Contractors</h5>
                                            <div class="active_ratio">
                                                <h3><?php echo e($totalContractors ?? 0); ?></h3>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="active_sales">
                                            <h5>Active Buyers</h5>
                                            <div class="active_ratio">
                                                <h3><?php echo e($totalBuyer ?? 0); ?></h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-8 col-md-6">
                        <div class="custom_projects_tabs admin_tbl scrollable_tbl">
                            <h3>Top Service Providers</h3>
                            <div class="table-responsive">
                                <div class="custom_table">
                                    <table id="" class="table table-striped datatable without_pagination_tbl">
                                        <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Project Title / Project Number</th>
                                            <th>Revenue Generated</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $topStaff; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $seller): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($seller->job->assignSeller->name ?? '---'); ?></td>
                                                    <td><?php echo e($seller->job->assignSeller->email ?? '---'); ?></td>
                                                    <td><?php echo e($seller->job->project_title . '/' . $seller->job->project_number); ?> </td>
                                                    <td>$<?php echo e(number_format($seller->total_amount, 2)); ?></td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="overall_sells">
                            <div class="white-box">
                                <h3 class="overall_graph_title">Overall Sales</h3>
                                <div class="overall_sale_chart">
                                    <canvas id="line_chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="custom_projects_tabs scrollable_tbl">
                            <h3>Live Projects</h3>
                            <div class="table-responsive">
                                <div class="custom_table">
                                    <table id="" class="table table-striped datatable without_pagination_tbl">
                                        <thead>
                                        <tr>
                                            <th>Client</th>
                                            <th>Service Provider</th>
                                            <th>Project Number</th>
                                            <th>Project Title</th>
                                            <th>Assigned Staff</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $jobs->where('status','on_going'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td> <?php echo e($job->user->name??'---'); ?> </td>
                                                    <td> <?php echo e($job->assignSeller->name??'---'); ?> </td>
                                                    <td> <?php echo e($job->project_number??'---'); ?> </td>
                                                    <td> <?php echo e($job->project_title??'---'); ?> </td>
                                                    <td> <?php echo e($job->jobAssignStaff->name ??'---'); ?> </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php elseif(auth()->user()->hasRole('staff')): ?>
        <section class="staff_homepage">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12 col-sm-12 col-xs-12">
                        <div class="ewallet_sales">
                            <div class="custom_sales">
                                <h3>Stats</h3>
                            </div>
                            <div class="statistics_card">
                                <div class="row">
                                    <div class="col-md-3 col-sm-6 custom_stats_column">
                                        <div class="custom_card">
                                            <h5>Scheduled Visits</h5>
                                            <h3><?php echo e($scheduledVisits?? 0); ?></h3>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6 custom_stats_column">
                                        <div class="custom_card">
                                            <h5>Open Disputes</h5>
                                            <h3><?php echo e($openDisputed ?? 0); ?></h3>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6 custom_stats_column">
                                        <div class="custom_card">
                                            <h5>Closed Disputes</h5>
                                            <h3><?php echo e($closedDisputed ?? 0); ?></h3>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6 custom_stats_column">
                                        <div class="custom_card">
                                            <h5>Total Projects Pending</h5>
                                            <h3><?php echo e($todayProjectsPending ?? 0); ?></h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12">
                        <div class="custom_projects_tabs scrollable_tbl">
                            <h3>Upcoming Site Visits</h3>
                            <div class="table-responsive">
                                <div class="custom_table">
                                    <table id="" class="table table-striped datatable without_pagination_tbl">
                                        <thead>
                                            <tr>
                                                <th>Project Number / Project Title</th>
                                                <th>Buyer / Email</th>
                                                <th>Service Provider / Email</th>
                                                <th>Location</th>
                                                <th>Visit Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $UpcomingSiteVisits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $siteVisit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td> <?php echo e($siteVisit->project_number ?? '---'); ?> / <?php echo e($siteVisit->project_title ?? '---'); ?> </td>
                                                    <td> <?php echo e($siteVisit->user->name ?? ''); ?> / <?php echo e($siteVisit->user->email ?? ''); ?> </td>
                                                    <td> <?php echo e($siteVisit->getSeller->name ?? ''); ?> / <?php echo e($siteVisit->getSeller->email ?? ''); ?> </td>
                                                    <td> <?php echo e($siteVisit->state ?? ''); ?> / <?php echo e($siteVisit->city ?? ''); ?> </td>
                                                    <td> <?php echo e($siteVisit->visit_date ?? ''); ?> </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12">
                        <div class="custom_projects_tabs scrollable_tbl">
                            <h3>Live Projects</h3>
                            <div class="table-responsive">
                                <div class="custom_table">
                                    <table id="" class="table table-striped datatable without_pagination_tbl">
                                        <thead>
                                            <tr>
                                                <th>Project Number / Project Title</th>
                                                <th>Buyer / Email</th>
                                                <th>Service Provider / Email</th>
                                                <th>Location</th>
                                                <th>Visit Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        <?php $__currentLoopData = $liveSiteVisits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $siteVisit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td> <?php echo e($siteVisit->project_number ?? '---'); ?> / <?php echo e($siteVisit->project_title ?? '---'); ?> </td>
                                                <td> <?php echo e($siteVisit->user->name ?? ''); ?> / <?php echo e($siteVisit->user->email ?? ''); ?> </td>
                                                <td> <?php echo e($siteVisit->getSeller->name ?? ''); ?> / <?php echo e($siteVisit->getSeller->email ?? ''); ?> </td>
                                                <td> <?php echo e($siteVisit->state ?? ''); ?> / <?php echo e($siteVisit->city ?? ''); ?> </td>
                                                <td> <?php echo e($siteVisit->visit_date ?? ''); ?> </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('js/db1.js')); ?>"></script>
<script>
    $(function() {
        var tblWithoutPagination = $('.without_pagination_tbl'). DataTable({
            "bLengthChange": false,
            "paging": false,
            "info": false,
        });
        $(document).on("input", '.custom_search_box', function () {
            var searchValue = $(this).val();
            tblWithoutPagination.search(searchValue).draw();
        });
    });
</script>
    <script src="<?php echo e(asset('plugins/components/peity/jquery.peity.min.js')); ?>"></script>
    <script src="<?php echo e(asset('plugins/components/peity/jquery.peity.init.js')); ?>"></script>
    <script src="https://cdn2.hubspot.net/hubfs/476360/Chart.js"></script>
    <script>
        function initializePeityCharts() {
            $('.peity-line1').each(function () {
                $(this).peity("line", {
                    fill: "url(#lineGradient)",
                    width: $(this).data('width') || 100,
                    height: $(this).data('height') || 30
                });
            });

            $('.peity-line2').each(function () {
                $(this).peity("line", {
                    stroke: "#77B92A",
                    fill: "url(#lineGradient2)",
                    width: $(this).data('width') || 100,
                    height: $(this).data('height') || 30
                });
            });

            $('.peity-line3').each(function () {
                $(this).peity("line", {
                    stroke: "#E40255",
                    fill: "url(#lineGradient3)",
                    width: $(this).data('width') || 100,
                    height: $(this).data('height') || 30
                });
            });
            $('.peity-line4').each(function () {
                $(this).peity("line", {
                    stroke: "#F6A600",
                    fill: "url(#lineGradient4)",
                    width: $(this).data('width') || 100,
                    height: $(this).data('height') || 30
                });
            });
        }
        // Initialize on page load
        initializePeityCharts();

        // Reinitialize on window resize
        $(window).on("resize", function () {
            initializePeityCharts();
        });

    </script>

    <script>
        // PHP variable to JavaScript
        var amounts = <?php echo json_encode($amounts); ?>;

        // Canvas and Context
        var canvas = document.getElementById('line_chart');
        var ctx = canvas.getContext("2d");

        // Make Canvas Responsive
        canvas.parentElement.style.position = "relative";
        canvas.parentElement.style.width = "100%";
        canvas.parentElement.style.height = "100%";

        // Gradient Configuration (spanning across width)
        var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
        gradient.addColorStop(0, '#2280C2');
        gradient.addColorStop(0.50, '#77B92A');
        gradient.addColorStop(0.80, '#E40255');
        gradient.addColorStop(1, '#F6A600');

        // Function to Adjust Font Size Based on Screen Size
        function responsiveFontSize() {
            return window.innerWidth < 768 ? 10 : 12; // Small font on mobile, normal on desktop
        }

        // Chart Configuration
        var myChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                datasets: [{
                    borderColor: gradient,
                    pointRadius: window.innerWidth < 768 ? 2 : 3, // Smaller points on mobile
                    tension: 0.4,
                    fill: false,
                    borderWidth: 2,
                    data: amounts,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                tooltips: {
                    enabled: true,
                    backgroundColor: "#FFDCD2",
                    titleFontColor: "#4a4a4a",
                    bodyFontColor: "#4a4a4a",
                    titleFontStyle: 'bold',
                    padding: 10,
                    cornerRadius: 10,
                    callbacks: {
                        label: function(tooltipItem) {
                                return '$' + ' ' + tooltipItem.yLabel;
                        }
                    }
                },
                legend: { display: false },
                scales: {
                    yAxes: [{
                        ticks: {
                            fontColor: "rgba(0,0,0,0.7)",
                            fontSize: responsiveFontSize(),
                            min: 0,
                            max: Math.ceil(Math.max(...amounts) / 3000) * 3000,
                            stepSize: 3000,
                            callback: function(value) {
                                return value.toLocaleString() + ' $';
                            },
                        },
                        gridLines: {
                            drawTicks: false,
                            display: true,
                            drawBorder: false,
                        },
                    }],
                    xAxes: [{
                        ticks: {
                            fontColor: "rgba(0,0,0,0.7)",
                            fontSize: responsiveFontSize(),
                            autoSkip: true,
                            maxTicksLimit: window.innerWidth < 768 ? 6 : 12, // Reduce labels on mobile
                            maxRotation: 0,
                            minRotation: 0
                        },
                        gridLines: {
                            display: false,
                            drawBorder: false
                        },
                    }]
                },
            },
        });

        // Resize Chart on Window Resize
        window.addEventListener("resize", function () {
            myChart.options.scales.xAxes[0].ticks.fontSize = responsiveFontSize();
            myChart.options.scales.yAxes[0].ticks.fontSize = responsiveFontSize();
            myChart.options.scales.xAxes[0].ticks.maxTicksLimit = window.innerWidth < 768 ? 6 : 12;
            myChart.options.elements.point.radius = window.innerWidth < 768 ? 2 : 3;
            myChart.update();
        });



        
            

            
            
            

            
            
            
            
            
            

            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            

            
            

    </script>





<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/index.blade.php ENDPATH**/ ?>