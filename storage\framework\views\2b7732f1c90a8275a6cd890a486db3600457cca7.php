<?php $__env->startPush("css"); ?>
    <style>
        header,footer{display: none;}
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<section class="login_register">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-7 col-md-6 custom_column_padding">
                <div class="custom_banner">
                    <video autoplay muted loop playsinline webkit-playsinline>
                        <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                    </video>
                    <div class="background_banner">
                        <div class="banner_content custom_login_title">
                            <a href="<?php echo e(url('home')); ?>">
                                <div class="site_logo">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png">
                                </div>
                            </a>
                            <div class="site_title">
                                <h1>Tackle any home improvement project, effortlessly</h1>
                            </div>
                            <div class="site_key_parameter">
                                <a href="javascript:void(0)"><i class="fa-regular fa-circle-check"></i>Free consultation</a>
                                <a href="javascript:void(0)"><i class="fa-solid fa-award"></i>Satisfaction Guaranteed</a>
                                <a href="javascript:void(0)"><img src="<?php echo e(asset('website')); ?>/assets/images/banner_sheild.png">Protected Payments</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5 col-md-6 custom_column_padding">
                <div class="custom_scrollbar">
                    <div class="login_box">
                        <form class="form-horizontal form-material" action="<?php echo e(route('register')); ?>" method="get">
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <h1>Select Role</h1>
                                    <h6>Choose your role from the options below to proceed with the application. Your role determines your access level and available features.</h6>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field custom_registration_role">
                                        <div class="custom_radio">
                                            <input class="form-check-input" type="radio" name="role_id" value="client" id="clientRole" checked>
                                            <label for="clientRole"><span><i class="fa-solid fa-users"></i></span><h4>Client</h4></label>
                                        </div>
                                        <div class="custom_radio">
                                            <input class="form-check-input" type="radio" name="role_id" value="contractor" id="contractorRole">
                                            <label for="contractorRole"><span><i class="fa-solid fa-user-gear"></i></span><h4>Contractor</h4></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="submit_btn previous_step">
                                        <button class="btn btn_black" type="submit">Continue
                                            <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                        </button>
                                        <a href="<?php echo e(url('home')); ?>" class="btn btn_black "><div class="next_btn back_btn_stepper"><i class="fa-solid fa-arrow-left"></i></div> Back</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush("js"); ?>
<script>


    
    
    
    
    
    
    
    
    

</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/login/registration_role.blade.php ENDPATH**/ ?>