<tbody>
<?php $__currentLoopData = $milestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td class="get_last_iteration"><?php echo e($loop->iteration??$key); ?></td>
        <td>
            <div class="milestone_icon">
                <span><i class="fa-solid fa-flag"></i></span>
            </div>
        </td>
        <td>
            <div class="milestone_card">
                <h5> <?php echo e($milestone->title??'---'); ?>  </h5>
                <p>$ <?php echo e($milestone->amount??'---'); ?> </p>
                <span class="due_date"><i class="fa-regular fa-calendar"></i><?php echo e($milestone->date??'---'); ?></span>
            </div>
        </td>
        <td>
            <div class="dropdown">
                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa-solid fa-ellipsis"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton4">
                    <li><a class="dropdown-item edit_milestone" data-id="<?php echo e($milestone->id); ?>"  ><i class="fa-solid fa-pen-to-square"></i> Edit</a></li>
                    <li><a class="dropdown-item delete_milestone" data-id="<?php echo e($milestone->id); ?>" ><i class="fa-solid fa-trash"></i> Delete</a></li>
                </ul>
            </div>
        </td>

    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</tbody>
<?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/ajax/milestone_table_view.blade.php ENDPATH**/ ?>