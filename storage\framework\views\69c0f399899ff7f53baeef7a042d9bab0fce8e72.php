<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">Projects</h2>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<section class="projects_wrapper pagination_scroll_tbl">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="custom_projects_tabs scrollable_tbl">
                    <div class="cms_tabs">
                        <ul class="nav projects nav-pills">

                            <li class="active"><a href="#on_going" data-toggle="tab" aria-expanded="false">On-Going</a></li>
                            <li><a href="#pending" data-toggle="tab" aria-expanded="true">Pending</a></li>
                            <li><a href="#completed" data-toggle="tab" aria-expanded="true">Completed</a></li>
                        </ul>
                    </div>
                    <div class="custom_search_filter custom_flex">
                        <div class="txt_field custom_search">
                            <input type="text" placeholder="Search" class="custom_search_box">
                        </div>
                        
                            
                                
                                    
                                        
                                    
                                        
                                            
                                        
                                        
                                            
                                                
                                            
                                        
                                    
                                
                            
                        
                    </div>
                    <div class="tab-content custom_tab_content">












































                        <div class="tab-pane active" id="on_going" >
                            <div class="table-responsive">
                                <div class="custom_table">
                                    <table id="" class="table table-striped myTable datatable">
                                        <thead>
                                        <tr>
                                            <th>Project Name</th>
                                            <th>User Name</th>
                                            <th>Project Number</th>
                                            <th>category, sub category</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $jobs->where('status','on_going'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><a href="<?php echo e(url('projects_ongoing_view')); ?>/<?php echo e($job->id); ?>"><?php echo e($job->project_title??'----'); ?></a></td>
                                                    <td><?php echo e($job->user->name??'----'); ?></td>
                                                    <td>#<?php echo e($job->project_number??'----'); ?></td>
                                                    <td><?php echo e($job->category->name??'----'); ?>,<?php echo e($job->subCategory->name??'----'); ?></td>
                                                    <td><div class="dropdown">
                                                            <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                                <i class="fa-solid fa-ellipsis"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                                <li><a class="dropdown-item" href="<?php echo e(url('projects_ongoing_view')); ?>/<?php echo e($job->id); ?>"><i class="fa-solid fa-eye"></i>View</a></li>


                                                            </ul>

                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane" id="pending" >
                            <div class="table-responsive">
                                <div class="custom_table">
                                    <table id="" class="table table-striped myTable datatable">
                                        <thead>
                                        <tr>
                                            <th>Project Name</th>
                                            <th>User Name</th>
                                            <th>Project Number</th>
                                            <th>category, sub category</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(in_array($job->status, ['posted', 'pending'])): ?>
                                                <tr>
                                                <td><a href="<?php echo e(url('projects_pending')); ?>/<?php echo e($job->id); ?>"><?php echo e($job->project_title??'----'); ?></a></td>
                                                <td><?php echo e($job->user->name??'----'); ?></td>
                                                <td>#<?php echo e($job->project_number??'----'); ?></td>
                                                <td><?php echo e($job->category->name??'----'); ?>,<?php echo e($job->subCategory->name??'----'); ?></td>
                                                <td><div class="dropdown">
                                                        <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                            <li><a class="dropdown-item" href="<?php echo e(url('projects_pending')); ?>/<?php echo e($job->id); ?>"><i class="fa-solid fa-eye"></i>View</a></li>


                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                          <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane" id="completed" >
                            <div class="table-responsive">
                                <div class="custom_table">
                                    <table id="" class="table table-striped myTable datatable">
                                        <thead>
                                        <tr>
                                            <th>Project Name</th>
                                            <th>User Name</th>
                                            <th>Project Number</th>
                                            <th>category, sub category</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $jobs->where('status','completed'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><a href="<?php echo e(url('projects_ongoing_view')); ?>/<?php echo e($job->id); ?>"><?php echo e($job->project_title??'----'); ?></a></td>
                                                    <td><?php echo e($job->user->name??'----'); ?></td>
                                                    <td>#<?php echo e($job->project_number??'----'); ?></td>
                                                    <td><?php echo e($job->category->name??'----'); ?>,<?php echo e($job->subCategory->name??'----'); ?></td>
                                                    <td><div class="dropdown">
                                                            <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                                <i class="fa-solid fa-ellipsis"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                                <li><a class="dropdown-item" href="<?php echo e(url('projects_ongoing_view')); ?>/<?php echo e($job->id); ?>"><i class="fa-solid fa-eye"></i>View</a></li>


                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script>
    //        Filter Functionality
//    $(document).ready(function () {
//        $(".filter_btn input[type='number']").on("change", function () {
//            let selectedNumber = $(this).val();
//            if (!selectedNumber) {
//                $(".myTable tbody tr").show();
//                return;
//            }
//
//            $(".myTable tbody tr").each(function () {
//                let cellValue = $(this).find("td:nth-child(3)").text().trim();
//
//                $(this).toggle(cellValue === selectedNumber);
//            });
//        });
//    });
</script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/admin/projects.blade.php ENDPATH**/ ?>