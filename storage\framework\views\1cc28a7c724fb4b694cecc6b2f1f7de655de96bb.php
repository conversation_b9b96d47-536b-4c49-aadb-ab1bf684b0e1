
<?php $__env->startPush("css"); ?>
    <link rel="stylesheet" href="<?php echo e(asset('plugins/components/dropify/dist/css/dropify.min.css')); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="posted_view_page">
        <div class="container">
            <div class="row custom_row">
                <?php if(auth()->user()->hasRole('buyer')): ?>

                    <div class="col-md-4">
                                <div class="listing_section">
                            <p style="color: #000;font-size:15px">Fixed Bid</p>

                                    <?php if(isset($job->jobOffers) && !empty($job->jobOffers) && $job->jobOffers->count() > 0): ?>
                                        <?php $__currentLoopData = $job->jobOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="post_list first_post fixed_bid_color">
                                                <div class="company_name">

                                                    <h2><?php echo e($offer->getStaffDetail->profile->company_name ?? '---'); ?></h2>
                                                </div>

                                                <div class="dflex">
                                                    <div class="profile_name_inline">
                                                        <div class="post_profile">
                                                            <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($offer?->getStaffDetail?->profile->pic??''); ?>" alt="">
                                                        </div>
                                                        <h5><?php echo e($offer->getStaffDetail->name ?? '---'); ?></h5>
                                                    </div>
                                                    <h6><?php echo e($offer->created_at->format('d-m-Y H:i')); ?></h6>
                                                </div>
                                                <div class="custom_justify">
                                                    <div>

                                                        <?php if($offer->range_or_fixed == 'no'): ?>

                                                            <h5>Total Bid:  $ <?php echo e($offer->getJobDetail->jobMilestone->sum('amount') ?? '0'); ?>  </h5>
                                                        <?php else: ?>

                                                            <h5>Total Bid:  $ <?php echo e($offer->getJobDetail->jobMilestone->sum('amount') ?? '0'); ?>  </h5>
                                                        <?php endif; ?>
                                                <h5>Labor Expense: $<?php echo e($offer->labour_expense ?? '0'); ?></h5>
                                                <h5>Materials  Expense: $<?php echo e($offer->material_expense ?? '0'); ?></h5>
                                                    </div>
                                                    <?php if($job->status == 'on_going'): ?>
                                                    <?php else: ?>
                                                        <a data-id="<?php echo e($offer->id); ?>" class="btn btn_black btn_has_icon post_detail_view post_detail_view_fixed" >Details <div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <div class="post_list first_post staff_first_post">
                                            <h6>No Fixed Bid Offer Found</h6>
                                        </div>
                                    <?php endif; ?>
                            <div class="estimate_bid_section">
                                <p >Estimate Bid</p>
                            </div>
                                    <?php if(isset($job->estimateOffers) && !empty($job->estimateOffers) && $job->estimateOffers->count() > 0): ?>
                                        <?php $__currentLoopData = $job->estimateOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $estimateOffer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="post_list first_post estimate_bid_color">
                                                <div class="company_name">
                                                    <h2><?php echo e($estimateOffer->getSellerDetail->profile->company_name ?? '---'); ?></h2>
                                                </div>
                                                <div class="dflex">
                                                    <div class="profile_name_inline">
                                                        <div class="post_profile">
                                                            <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($estimateOffer?->getSellerDetail?->profile->pic??''); ?>" alt="">
                                                        </div>
                                                        <h5><?php echo e($estimateOffer->getSellerDetail->name ?? '---'); ?></h5>
                                                    </div>
                                                    <h6><?php echo e($estimateOffer->created_at->format('d-m-Y H:i')); ?></h6>
                                                </div>
                                                <div class="custom_justify">
                                                    <div>

                                                        <h5>Labor Expense:    $<?php echo e($estimateOffer->labour_expense ?? '0'); ?></h5>
                                                        <?php if($estimateOffer->range_or_fixed == 'yes'): ?>
                                                        <h5>Materials Expense: $<?php echo e($estimateOffer->min_amount ?? '0'); ?> --- $<?php echo e($estimateOffer->max_amount ?? '0'); ?></h5>
                                                            <?php else: ?>
                                                        <h5>Materials Expense: $<?php echo e($estimateOffer->amount ?? '0'); ?></h5>
                                                        <?php endif; ?>

                                                            <?php
                                                                $labour = (float) ($estimateOffer->labour_expense ?? 0);
                                                                $priceDisplay = ($estimateOffer->min_amount && $estimateOffer->max_amount)
                                                                    ? '$' . number_format($labour + $estimateOffer->min_amount, 2) . ' --- $' . number_format($labour + $estimateOffer->max_amount, 2)
                                                                    : '$' . number_format($labour + ($estimateOffer->amount ?? 0));
                                                            ?>



                                                        <?php if($estimateOffer->range_or_fixed == 'no'): ?>

                                                            <h5>Total Bid Amount:  <?php echo e($priceDisplay); ?></h5>
                                                        <?php else: ?>
                                                            <h5>Total Bid Amount: <?php echo e($priceDisplay); ?></h5>

                                                        <?php endif; ?>
                                                    </div>
                                                    <?php if($job->status == 'on_going'): ?>
                                                    <?php else: ?>
                                                        <a data-id="<?php echo e($estimateOffer->id); ?>" class="btn btn_black btn_has_icon  post_detail_view post_detail_view_estimated">Details <div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <div class="post_list first_post staff_first_post">
                                            <h6>No Estimate Bid Offer Found</h6>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>


                <?php endif; ?>
                <div class="col-md-8">
                    <div class="row custom_rowGap">
                        <div class="col-md-12">
                            <div class="posted_view_jobs">
                                <div class="custom_categories">
                                    <h3><?php echo e($job->project_number.'/'.ucwords($job->project_title)??'----'); ?></h3>
                                </div>
                                <div class="custom_categories">
                                    <span> <?php echo e($job->category->name ?? '----'); ?> / <?php echo e($job->subCategory->name ?? '----'); ?> </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <h5>Posted On: <span><?php echo e($job->created_at->format('d-m-Y')); ?></span></h5>
                        </div>
                        <div class="col-md-12">
                            <h5>Visit Date: <span><?php echo e($job->visit_date); ?>,  <?php echo e($job->visit_time_from??''); ?> -- <?php echo e($job->visit_time_to??''); ?></span></h5>
                        </div>
                        <div class="col-md-12">
                            <h5>Budget:</h5>
                            <h3>$<?php echo e($job->project_budget_min??'0'); ?> - $<?php echo e($job->project_budget_max??'0'); ?></h3>
                        </div>
                        <?php if(!empty($job->jobQuestionAnswer)): ?>
                            <?php $__currentLoopData = $job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-12">
                                    <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                        <?php if(isset($element['value']) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $element['value'])): ?>
                                            <a href="<?php echo e(asset('website')); ?>/<?php echo e($element['value']); ?>" data-fancybox="gallery">
                                                <div class="questionnaire_img">
                                                    <img src="<?php echo e(asset('website')); ?>/<?php echo e($element['value']); ?>"  style="max-width: 100%;">
                                                </div>
                                            </a>
                                        <?php else: ?>
                                            <h6><?php echo e($element['value'] ?? '----'); ?></h6>
                                        <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                        <div class="col-md-12">
                            <h5>Photos</h5>
                           <?php if(!empty($job->jobFiles)): ?>
                                <div class="row custom_row">
                                    <?php $__currentLoopData = $job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $fileUrl = $file->file ?? '---';
                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                        ?>

                                        <div class="col-md-3 col-sm-4 col-6">
                                            <?php if($isImage): ?>
                                                <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                <div class="project_photos questionnaire_img">
                                                        <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="" loading="lazy">
                                                    </div>
                                                </a>
                                            <?php elseif($isVideo): ?>
                                                <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery" data-type="video">
                                                    <div class="project_photos questionnaire_img">
                                                        <video controls preload="metadata" class="video-player">
                                                            <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                            Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                        </video>
                                                </div>
                                            </a>
                                            <?php else: ?>
                                                <div class="project_photos questionnaire_img">
                                                    <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-4"></div>
                                </div>
                           <?php endif; ?>
                        </div>
                        <?php if(isset($job->jobAssignStaffMeasurements) && !$job->jobAssignStaffMeasurements->isEmpty()): ?>
                            <div class="col-md-12">
                                <div class="project_detail">
                                    <div class="custom_flex">
                                        <h3>Further Details</h3>
                                    </div>
                                    <div class="project_scope">
                                        <h5>Measurements : </h5>
                                        <?php $__empty_1 = true; $__currentLoopData = $job->jobAssignStaffMeasurements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $measurement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <h6>Name:<?php echo e($measurement->name??'-'); ?>, Number:<?php echo e($measurement->number??'-'); ?>, Unit:<?php echo e($measurement->unit??'-'); ?></h6>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                            <h6>No Measurements Found</h6>
                                        <?php endif; ?>
                                        <h5>Specifications</h5>
                                        <h6><?php echo $job->staff_specifications ?? 'N/A'; ?></h6>
                                    </div>
                                </div>
                            </div>

                            <?php if(isset($job->jobAssignStaffDocuments) && !empty($job->jobAssignStaffDocuments)): ?>
                                <div class="col-md-12">
                                    <div class="project_detail">
                                        <div class="">
                                            <h5> Media</h5>
                                        </div>
                                        <div class="media_download">
                                            <?php $__currentLoopData = $job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    $fileUrl = $file->image ?? '---';
                                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                ?>
                                                <?php if($isImage): ?>
                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                        <div class="custom_images">
                                                            <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="">
                                                        </div>
                                                    </a>
                                                <?php elseif($isVideo): ?>
                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                        <div class="custom_images staff_video">
                                                            <div class="downloadable_video">
                                                                <video controls preload="metadata" class="video-player">
                                                                    <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                    Your browser does not support this video format.
                                                                </video>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php else: ?>
                                                    <div class="custom_images">
                                                        <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="modal fade post_detail_view" id="post_detail_view" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
    </div>

    <div class="modal fade post_detail_view post_detail_view_estimated" id="post_detail_view_estimated" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
<script>
    $(document).ready(function() {
        $('[data-fancybox="gallery"]').fancybox({
            protect: false,
            clickOutside: false,
            closeExisting: false,
        });
    });
</script>
<script>

    $(document).on('click', '.post_detail_view_fixed', function () {
        var id = $(this).attr('data-id');
        $.ajax({
            url: "<?php echo e(url('view_seller_bid_modal_ajax')); ?>/" + id,
            success: function (data) {
                $('#post_detail_view').html(data);
                $('#post_detail_view').modal('show');
            }//ends success
        });
    });

    $(document).on('click', '.post_detail_view_estimated', function () {
        var id = $(this).attr('data-id');
        $.ajax({
            url: "<?php echo e(url('view_seller_estimated_modal_ajax')); ?>/" + id,
            success: function (data) {
                $('#post_detail_view_estimated').html(data);
                $('#post_detail_view_estimated').modal('show');
            }//ends success
        });
    });

</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mrdoall-git\resources\views/website/buyer/posted_view.blade.php ENDPATH**/ ?>