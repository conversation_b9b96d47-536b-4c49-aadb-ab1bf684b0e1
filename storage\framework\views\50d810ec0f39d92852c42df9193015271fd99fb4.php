<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">Projects</h2>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('backbtn_nav'); ?>
    <a class="navbar_backBtn" href="<?php echo e(url('projects')); ?>">
        <i class="fa-solid fa-chevron-left"></i>
    </a>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>

    <section class="projects_wrapper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_projects_tabs">
                        <div class="cms_tabs">
                            <ul class="nav projects nav-pills">
                                <li class="active"><a href="#overview" data-toggle="tab" aria-expanded="false">Overview</a></li>
                                <li class=""><a href="#milestones" data-toggle="tab" aria-expanded="false">Milestones</a></li>
                            </ul>
                        </div>
                        <div class="tab-content custom_tab_content">
                            <div class="tab-pane active" id="overview" >
                                <div class="ongoing_overview custom_label">
                                    <div class="row custom_row">
                                        <div class="col-md-12">
                                            <div class="client_projects custom_flex">
                                                <div class="custom_name">
                                                    <h3><?php echo e($job->user->name??'---'); ?></h3>
                                                    <div class="rating_star">
                                                        <input type="checkbox" id="review1" value="1" readonly checked/>
                                                        <label for="review1"></label>
                                                        <input type="checkbox" id="review2" value="2" readonly checked/>
                                                        <label for="review2"></label>
                                                        <input type="checkbox" id="review3" value="3" readonly checked/>
                                                        <label for="review3"></label>
                                                        <input type="checkbox" id="review4" value="4" readonly checked/>
                                                        <label for="review4"></label>
                                                        <input type="checkbox" id="review5" value="5" readonly/>
                                                        <label for="review5"></label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="posting_date">
                                                <label>Project Title:<span><?php echo e($job->project_title??'---'); ?></span></label>
                                                <label>Project Number:<span><?php echo e($job->project_number??'---'); ?></span></label>
                                                <label>Email:<span><?php echo e($job->user->email??'---'); ?></span></label>
                                                <label>Posted On:<span><?php echo e($job->created_at->format('d-m-y')??'---'); ?></span></label>
                                            </div>
                                            <div class="schedule_btn">
                                                <a href="#!" class="btn btn_blue">
                                                    <label>Scheduled Visit:<span> <?php echo e($job->visit_date); ?>, TIME <?php echo e($job->visit_time_from??'--'); ?> -- <?php echo e($job->visit_time_to??'--'); ?> </span></label>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="project_detail">
                                                <h5>Budget:</h5>
                                                <h3>$<?php echo e($job->project_budget_min??'0'); ?> - $<?php echo e($job->project_budget_max??'0'); ?></h3>
                                                <?php if(!empty($job->jobQuestionAnswer)): ?>
                                                    <?php $__currentLoopData = $job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="project_scope">
                                                            <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                                            <?php
                                                                $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                                            ?>
                                                            <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $value)): ?>
                                                                <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="">
                                                                    <div class="questionnaire_img">
                                                                        <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="width: 100%;">
                                                                    </div>
                                                                </a>
                                                            <?php else: ?>
                                                                <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <div class="project_media">
                                                <?php if(!empty($job->jobFiles)): ?>
                                                    <div class="custom_flex">
                                                        <h5>Media</h5>
                                                        <a href="<?php echo e(route('download_all_document')); ?>/<?php echo e($job->id); ?>" class="btn btn_black">Download<i class="fa-solid fa-download"></i> </a>
                                                    </div>
                                                    <div class="media_download">
                                                        <?php $__currentLoopData = $job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php
                                                                $fileUrl = $file->file ?? '---';
                                                                $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                            ?>
                                                            <?php if($isImage): ?>
                                                                <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                    <div class="custom_images">
                                                                        <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid">
                                                                    </div>
                                                                </a>
                                                            <?php elseif($isVideo): ?>
                                                                <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                    <div class="custom_images">
                                                                        <video controls preload="metadata" class="img-fluid video-player">
                                                                            <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                            Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                                        </video>
                                                                    </div>
                                                                </a>
                                                            <?php else: ?>
                                                                <div class="custom_images">
                                                                    <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                </div>
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                            <?php if(isset($job->jobAssignStaffMeasurements) && !$job->jobAssignStaffMeasurements->isEmpty()): ?>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <div class="project_detail">
                                                <div class="custom_flex">
                                                    <h3>Staff Further Details</h3>

                                                </div>
                                                <div class="project_scope">
                                                        <h5>Measurements : </h5>
                                                        <?php $__currentLoopData = $job->jobAssignStaffMeasurements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $measurement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <h6>Name:<?php echo e($measurement->name??'-'); ?>, Number:<?php echo e($measurement->number??'-'); ?>, Unit:<?php echo e($measurement->unit??'-'); ?></h6>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <h5>Specifications</h5>
                                                        <h6><?php echo $job->staff_specifications ?? '----'; ?></h6>
                                                </div>
                                            </div>
                                        </div>
                                            <?php if(isset($job->jobAssignStaffDocuments) && !empty($job->jobAssignStaffDocuments)): ?>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <div class="project_media">
                                                        <div class="custom_flex">
                                                            <h5>Media</h5>

                                                        </div>
                                                        <div class="media_download">
                                                            <?php $__currentLoopData = $job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <?php
                                                                    $fileUrl = $file->image ?? '---';
                                                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                ?>
                                                                <?php if($isImage): ?>
                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                        <div class="custom_images">
                                                                            <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="">
                                                                        </div>
                                                                    </a>
                                                                <?php elseif($isVideo): ?>
                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                        <div class="custom_images staff_video">
                                                                            <div class="downloadable_video">
                                                                                <video controls preload="metadata" class="video-player">
                                                                                    <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                    Your browser does not support this video format.
                                                                                </video>
                                                                            </div>
                                                                            <a href="<?php echo e($fileUrl); ?>" class="download_video" download>Download the video</a>
                                                                        </div>
                                                                    </a>
                                                                <?php else: ?>
                                                                    <div class="custom_images">
                                                                        <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                </div>
                                                                <?php endif; ?>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane" id="milestones" >
                                <div class="ongoing_milestone">
                                    <div class="row custom_row">
                                        <div class="col-md-4 col-sm-4 col-xs-6">
                                            <div class="milestone_card milestone_border">
                                                <h3>$  <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->sum('amount') ?? '0'); ?> <?php else: ?> 00 <?php endif; ?></h3>
                                                <h5>In Escrow</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-6">
                                            <div class="milestone_card milestone_border">
                                                <h3>$ <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','released')->sum('amount') ?? '0'); ?> <?php else: ?> 00 <?php endif; ?> </h3>
                                                <h5>Milestones Paid: <span><?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','released')->count()??'0'); ?> <?php else: ?> 00 <?php endif; ?></span></h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <div class="milestone_card milestone_border">
                                                <h3>$ <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','!=','released')->sum('amount')); ?> <?php else: ?> 00 <?php endif; ?></h3>
                                                <h5>Milestones Remaining: <span> <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','!=','released')->count() ?? '0'); ?> <?php else: ?> 00 <?php endif; ?> </span></h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="custom_timeline">
                                    <h3>Milestone Timeline</h3>
                                </div>
                                <div class="project_ongoing">
                                    <div class="row custom_row">
                                        <?php if(isset($milestones) && !empty($milestones)): ?>
                                            <?php $__currentLoopData = $milestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="col-md-12 custom_column_milestone">
                                                    <div class="project_milestone">
                                                        <span class="project_id"><?php echo e($loop->iteration??$key); ?></span>
                                                        <div class="milestone_icon">
                                                            <span><i class="fa-solid fa-flag"></i></span>
                                                        </div>
                                                        <div class="milestone_card">
                                                            <h5><?php echo e($milestone->title??'---'); ?></h5>
                                                            <h6>$ <?php echo e($milestone->amount??'---'); ?></h6>
                                                            <span class="due_date"><i class="fa-regular fa-calendar"></i>Due <?php echo e($milestone->date??'---'); ?> / <?php echo e($milestone->requested??'---'); ?></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
    <script>
        $(document).ready(function() {
            $('[data-fancybox="gallery"]').fancybox({
                protect: false,
                clickOutside: false,
                closeExisting: false,
            });
        });
    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/admin/projects_ongoing_view.blade.php ENDPATH**/ ?>