<?php $__env->startPush('css'); ?>


<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">Staff Management</h2>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <section class="projects_wrapper pagination_scroll_tbl">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_projects_tabs scrollable_tbl">
                        <div class="custom_search_filter custom_flex">
                            <div class="txt_field custom_search">
                                <input type="text" placeholder="Search" class="custom_search_box">
                            </div>
                            <div class="filter_btn">
                                <a href="#!" class="btn btn_grey" data-toggle="modal" data-target="#addStaff">Add Staff<i class="fa-solid fa-users"></i> </a>
                                
                                    
                            </div>
                        </div>
                        <div class="table-responsive">
                            <div class="custom_table">
                                <table id="" class="table table-striped myTable datatable">
                                    <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Role</th>
                                        <th>Email</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $staff; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><a href="<?php echo e(url('admin_staff_view_management')); ?>/<?php echo e($user->id); ?>"><?php echo e($user->name??'---'); ?></a></td>
                                            <td><?php echo e($user->roles()->pluck('name')->implode(', ')); ?></td>
                                            <td><?php echo e($user->email ?? '---'); ?></td>
                                            <td><div class="dropdown custom_dropdown">
                                                    <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                        <li><a class="dropdown-item" href="<?php echo e(url('admin_staff_view_management')); ?>/<?php echo e($user->id); ?>"><i class="fa-solid fa-eye"></i>View</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal fade assign_staff" id="addStaff" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content append_input_wrapper">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalLabel1">Add Staff</h4>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('store_staff')); ?>" method="post">
                        <?php echo csrf_field(); ?>
                        <div class="txt_field ">
                            <label for="" class="form-label">Email</label>
                            <input type="email" class="form-control staff_email" id="" placeholder="enter staff email ">
                            <a href="#!" class="btn btn_transparent add_email"><i class="fa-solid fa-plus"></i></a>
                        </div>
                        <div class="email"></div>
                        <div class="email_error text-danger" style="display: none;">Please add at least one email.</div>
                        <div class="add_btn">
                            <button type="submit" class="btn btn_black" id="btnSubmit">Submit<i class="fa-solid fa-plus"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>

        $(document).ready(function (){
            $("form").on("submit", function (e) {

                var emailTags = $(this).find('.email .posted_email');
                var emailError = $(this).find('.email_error');
                var $btn = $(this).find('#btnSubmit');
                if (emailTags.length === 0) {
                    emailTags.addClass("invalid");
                    emailError.show();
                    e.preventDefault();
                } else {

                    emailTags.removeClass("invalid");
                    emailError.hide();
                    $btn.prop('disabled', true).html('Submitting.. <span><i class="fa-solid fa-spinner fa-spin"></i></span>');
                }
            });


            var addedEmails = [];
            $(".add_email").click(function (){
                var emailInput = $(this).closest(".append_input_wrapper").find('.staff_email');
                var email = emailInput.val().trim();
                // Validate email format
                var emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (email !== "" && emailPattern.test(email) && !addedEmails.includes(email)) {
                    addedEmails.push(email);
                    $(this).closest(".append_input_wrapper").find(".email").append(
                        '<div class="posted_email">' +
                        '<p>' + email + '<a href="#!" class="remove_email"><i class="fa fa-solid fa-close"></i></a></p>' +
                        '</div>'
                    );
                    $(this).closest("form").append(
                        '<input type="hidden" name="emails[]" value="' + email + '" class="hidden_email_' + email.replace(/[@.]/g, '_') + '">'
                    );

                    emailInput.val("");
                } else {
                    alert("Please enter a valid and unique email address.");
                }
        });

            $(document).on("click", ".remove_email", function () {
                var email = $(this).closest(".posted_email").text().trim();
                addedEmails = addedEmails.filter(function(e) { return e !== email; });
                $(".hidden_email_" + email.replace(/[@.]/g, '_')).remove();
            $(this).closest(".posted_email").remove();
        });
        });


    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/admin/admin_staff_management.blade.php ENDPATH**/ ?>