<?php $__env->startPush("css"); ?>
    <link rel="stylesheet" href="<?php echo e(asset('plugins/components/dropify/dist/css/dropify.min.css')); ?>">
<style>
    .error{
        color:red !important;
    }
</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="post_project_section edit_profile">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="edit_buyer_profile post_project_content">
                        <form action="<?php echo e(route('user_profile_update',$user->id)); ?>" method="post" enctype="multipart/form-data" id="updateProfileForm">
                            <div class="row">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>
                                <div class="col-md-12">
                                    <div class="edit_btn">
                                        <button type="button" id="" class="btn btn_black change_pass">Change Password<i class="fa-solid fa-eye-slash"></i></button>
                                    </div>
                                </div>
                                <div class="col-m-12">
                                    <div class="edit_profile_logo">
                                        <div class="profile_picture">
                                            <input type="file" id="" class="dropify myinput" name="pic_file" data-default-file="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($user->profile->pic??''); ?>"/>
                                            
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="single_field">
                                        <h5 class="mb_20">About</h5>
                                        <div class="txt_field">
                                            <textarea class="form-control" name="about" rows="4" placeholder="Enter Your About" class="form-control"><?php echo e($user->profile->bio??''); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <h5>Personal Information</h5>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="single_field">
                                        <div class="txt_field">
                                            <label>First Name <span class="text-danger">*</span></label>
                                            <input  type="text" class="form-control" id="first_name" name="first_name" value="<?php echo e($user->first_name??''); ?>" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="single_field">
                                        <div class="txt_field">
                                            <label>Last Name</label>
                                            <input  type="text" class="form-control" name="last_name" value="<?php echo e($user->last_name??''); ?>" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="single_field">
                                        <div class="txt_field">
                                            <label>Address</label>
                                            <input type="text" id="address" name="address" class="form-control" value="<?php echo e($user->profile->address ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="single_field">
                                        <div class="txt_field">
                                            <label>Date of Birth</label>
                                            <input  type="date" class="form-control" name="dob" id="dob" value="<?php echo e($user->profile->dob??''); ?>" max="<?php echo e(now()->format('Y-m-d')); ?>">
                                        </div>
                                    </div>
                                    <p class="text-danger mt-4" id="dob-error">
                                    </p>
                                </div>
                                <div class="col-md-12">
                                    <h5>Contact Information</h5>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="single_field">
                                        <div class="txt_field">
                                            <label>Email</label>
                                            <input  type="email" readonly class="form-control" value="<?php echo e($user->email??''); ?>" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="single_field">
                                        <div class="txt_field">
                                            <label>Phone Number</label>
                                            <input  type="number" name="phone" id="phone" class="form-control" value="<?php echo e($user->profile->phone??''); ?>" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="post_btn">
                                        <button type="button" class="btn btn_transparent">Cancel</button>
                                        <button type="Submit" class="btn btn_black btn_has_icon" >Save Changes <div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="change_password_wrapper custom_projects_tabs">
                        <form action="<?php echo e(route('change_user_password')); ?>" method="post" enctype="multipart/form-data" id="account_detail">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="currentPassword" class="form-label input_labels">Current Password</label>
                                        <input type="password" name="password" class="form-control input_fields borderRadius" id="currentpassword" aria-describedby="emailHelp">
                                        <?php if($errors->has('password')): ?>
                                            <span class="invalid-feedback">
                                                <strong><?php echo e($errors->first('password')); ?></strong>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="txt_field">
                                        <label for="newPassword" class="form-label input_labels">New Password</label>
                                        <div class="input_group_box">
                                            <input type="password" name="new_password" class="form-control input_fields borderRadius" id="newpassword" aria-describedby="emailHelp">
                                            <div class="input_icon">
                                                <a href="javascript:void(0);" class="password_btn2"><i class="fa fa-eye-slash" aria-hidden="true"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="txt_field">
                                        <label for="confirmPassword" class="form-label input_labels">Confirm Password</label>
                                        <div class="input_group_box">
                                            <input type="password" name="password_confirmation" class="form-control input_fields borderRadius" id="password_confirmation" aria-describedby="emailHelp">
                                            <div class="input_icon">
                                                <a href="javascript:void(0);" class="password_btn3"><i class="fa fa-eye-slash" aria-hidden="true"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6"></div>
                            </div>
                            <div class="change_password_btn">
                                <button type="submit" class="btn btn_black submit_btn">Change Password</button>
                                <button type="button" class="btn btn_black back_prev">Back</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('js/jasny-bootstrap.js')); ?>"></script>
    <script src="<?php echo e(asset('plugins/components/dropify/dist/js/dropify.min.js')); ?>"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>


    <script>
        $(function() {
            $('.dropify').dropify();
            $(".dropify-wrapper .dropify-clear").html("");
        });

        
            
                
                
                    
                    
                    
                        
                        
                    
                    
                        
                            
                        
                            
                        
                    
                    
                        
                    
                
            
        
//        $(document).ready(function() {
//            // On form submit
//            $('#updateProfileForm').on('submit', function(event) {
//                // Get the values of the password fields
//                var password = $('#password').val();
//                var confirmPassword = $('#confirm_password').val();
//                var dob = $('#dob').val(); // Get the value of the DOB field
//
//                // Get today's date in YYYY-MM-DD format
//                var today = new Date().toISOString().split('T')[0];
//
//                // Clear previous error messages
//                $('#password-error').hide();
//                $('#dob-error').hide();
//
//                // Check if the passwords match
//                if (password !== confirmPassword) {
//                    // Prevent form submission
//                    event.preventDefault();
//                    document.getElementById('password-error').innerHTML = "Confirm Password not Matched";
//                    // Show error message
//                    $('#password-error').show();
//                } else {
//                    // Hide error message if passwords match
//                    $('#password-error').hide();
//                }
//
//                // Validate DOB (should not be greater than today's date)
//                if (dob > today) {
//                    // Prevent form submission
//                    event.preventDefault();
//                    document.getElementById('dob-error').innerHTML = "Date of birth should not be greater than todays date";
//                    // Show error message for DOB
//                    $('#dob-error').show();
//                }
//            });
//        });

        $("#updateProfileForm").validate({
            rules: {
                first_name: {
                    required: true,
                },
                email: {
                    required: true,
                },

            },messages:{
                name: "Name field is required",
                email: "Email field is required",
            }
        });
        function initAutocomplete() {
            // Get the address input element
            var input = document.getElementById('address');

            // Create the autocomplete object and link it to the address input field
            var autocomplete = new google.maps.places.Autocomplete(input);

            // Optionally, set the types of places to be searched (for example, 'geocode' for general addresses)
            autocomplete.setTypes(['geocode']);
        }

        // Ensure the function is triggered after the DOM has fully loaded
        window.onload = function() {
            // Load the Google Maps API script dynamically after the page has loaded
            var script = document.createElement('script');
            script.src = "https://maps.googleapis.com/maps/api/js?key=<?php echo e(env('googleAPI')); ?>&libraries=places&callback=initAutocomplete";
            script.async = true;
            script.defer = true;
            document.body.appendChild(script);
        };

    </script>
    <script>
        document.getElementById('edit-icon').addEventListener('click', function() {
            document.getElementById('input-file-now').click();
        });
    </script>
<script>
    jQuery('#account_detail').validate({
        rules: {
            password:{
                required: true,
                remote: {
                    type: 'post',
                    url: "<?php echo e(route('check_password')); ?>",
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>",'password': function () { return $('#currentpassword').val(); }
                    },
                    dataType: 'json'
                }
            },
            new_password: {
                required: true,
                minlength: 8,
            },
            password_confirmation: {
                required: true,
                minlength: 8,
                equalTo: "#newpassword",
            },
        },
        messages: {
            password: "Password must be at least 8 characters.",
            password_confirmation: "Confirm password is incorrect.",
            password: {
                required: "Please enter your current password.",
                remote: "Current Password is Invalid"
            }
        },
        submitHandler: function (form) {
            return true;
        }
    });


    $(function(){
        $('.password_btn2').click(function(){
            if($('.password_btn2 i').hasClass('fa-eye-slash')){
                $('.password_btn2 i').removeClass('fa-eye-slash');
                $('.password_btn2 i').addClass('fa-eye');
                $('#newpassword').attr('type','text');
            }else{
                $('.password_btn2 i').removeClass('fa-eye');
                $('.password_btn2 i').addClass('fa-eye-slash');
                $('#newpassword').attr('type','password');
            }
        });
    });
    $(function(){
        $('.password_btn3').click(function(){
            if($('.password_btn3 i').hasClass('fa-eye-slash')){
                $('.password_btn3 i').removeClass('fa-eye-slash');
                $('.password_btn3 i').addClass('fa-eye');
                $('#password_confirmation').attr('type','text');
            }else{
                $('.password_btn3 i').removeClass('fa-eye');
                $('.password_btn3 i').addClass('fa-eye-slash');
                $('#password_confirmation').attr('type','password');
            }
        });
    });
</script>
<script>
    $(document).ready(function () {
        $(".edit_profile .change_password_wrapper").hide();
        $(".edit_profile .edit_buyer_profile .edit_btn .change_pass").on("click", function () {
            $(".edit_profile .edit_buyer_profile ").hide();
            $(".edit_profile .change_password_wrapper").show();
        });
        $(".edit_profile .change_password_btn .back_prev").on("click", function () {
            $(".edit_profile .change_password_wrapper ").hide();
            $(".edit_profile .edit_buyer_profile").show();
        });
    })
</script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/buyer/edit_profile.blade.php ENDPATH**/ ?>