<div class="col-md-6 col-sm-6">
    <div class="txt_field">
        <label for="project_title">Project Title</label>
        <input type="text" class="form-control" id="project_title" name="project_title" placeholder="" required>
    </div>
</div>
<?php $__currentLoopData = $job_question; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if($element->question_type == 'file'): ?>






<div class="col-md-6 col-sm-6">
    <div class="txt_field">
        <label><?php echo e($element->question); ?></label>
        <input type="file" class="form-control file_input vehicle_field" id="vehicle_field_<?php echo e($element->id); ?>" accept="image/*" required>
        <input type="hidden" class="form-control file_name_display mt-2" name="question[<?php echo e($element->id); ?>][val]"  placeholder="Uploaded filename will appear here" readonly> <!-- Display uploaded filename -->
        <span class="file_size_error" style="color: red;"></span>
    </div>
</div>

    <?php elseif($element->question_type == 'input'): ?>
        <div class="col-md-6 col-sm-6">
            <div class="txt_field">
                <label for="vehicle_field_<?php echo e($element->id); ?>"><?php echo e($element->question); ?> </label>
                <input type="text" class="form-control vehicle_field" id="vehicle_field_<?php echo e($element->id); ?>" name="question[<?php echo e($element->id); ?>][val]" placeholder="" required>
            </div>
        </div>
    <?php elseif($element->question_type == 'text'): ?>
        <div class="col-md-6 col-sm-6">
            <div class="txt_field">
                <label for="vehicle_field_<?php echo e($element->id); ?>"><?php echo e($element->question); ?> </label>
                <textarea class="vehicle_field" name="question[<?php echo e($element->id); ?>][val]" id="vehicle_field_<?php echo e($element->id); ?>" rows="4" cols="50" required></textarea>
            </div>
        </div>
    <?php elseif($element->question_type == 'select'): ?>
        <div class="col-md-6 col-sm-6">
            <div class="txt_field">
                <label for="vehicle_field_<?php echo e($element->id); ?>"><?php echo e($element->question); ?> </label>
                <select id="vehicle_field_<?php echo e($element->id); ?>" name="question[<?php echo e($element->id); ?>][val]" class="form-control question_type_select" required>
                    <option value="" selected="selected"></option>
                            <?php $__currentLoopData = json_decode($element->options); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($option ==  'Unsure/undecided'): ?>

                                <?php else: ?>
                                    <option value="<?php echo e($option); ?>" ><?php echo e($option); ?></option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($element->other_input == 1): ?>
                                <option data-type="other_input" value="other" >Other – Please specify</option>
                            <?php endif; ?>
                            <?php if($element->image_input == 1): ?>
                                <option data-type="image_input" value="other" >Unsure (Please attach a picture if possible)</option>
                            <?php endif; ?>
                </select>
                <div class="question_type_select_div"></div>
            </div>
        </div>
    <?php elseif($element->question_type == 'select_multiple'): ?>
        <div class="col-md-6 col-sm-6">
            <div class="txt_field">
                <label for="vehicle_field_<?php echo e($element->id); ?>"><?php echo e($element->question); ?> </label>
                <div class="custom_multi_select">
                    <select multiple id="vehicle_field_<?php echo e($element->id); ?>" name="question[<?php echo e($element->id); ?>][val]" class="form-control vehicle_field custom_multiselect" required>
                        
                        <?php $__currentLoopData = json_decode($element->options); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($option); ?>" ><?php echo e($option); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
        </div>
    <?php elseif($element->question_type == 'checkbox'): ?>
        <div class="col-md-12">
            <div class="txt_field">
               <?php $__currentLoopData = json_decode($element->options); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <input type="checkbox" class="vehicle_field" value="Yes" name="question[<?php echo e($element->id); ?>][val]" id="vehicle_field_<?php echo e($index); ?>_<?php echo e($element->id); ?>" <?php echo e($loop->first ? 'checked' : ''); ?> required>
                <label for="vehicle_field_<?php echo e($index); ?>_<?php echo e($element->id); ?>"><?php echo e($element->question); ?> </label>
               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php elseif($element->question_type == 'radio'): ?>
        <div class="col-md-12">
            <div class="txt_field">
              <?php $__currentLoopData = json_decode($element->options); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <input type="checkbox" class="vehicle_field" value="Yes" name="question[<?php echo e($element->id); ?>][val]" id="vehicle_field_<?php echo e($index); ?>_<?php echo e($element->id); ?>" <?php echo e($loop->first ? 'checked' : ''); ?> required>
                <label for="vehicle_field_<?php echo e($index); ?>_<?php echo e($element->id); ?>"><?php echo e($element->question); ?> </label>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


<?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/ajax/get_sub_category_question.blade.php ENDPATH**/ ?>