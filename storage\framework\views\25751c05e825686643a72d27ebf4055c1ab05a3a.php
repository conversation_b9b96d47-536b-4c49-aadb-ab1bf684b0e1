<?php $__env->startPush('css'); ?>
    <link href="<?php echo e(asset('plugins/components/datatables/jquery.dataTables.min.css')); ?>" rel="stylesheet" type="text/css"/>
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet"
          type="text/css"/>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">Contact</h2>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <section class="contact_admin projects_wrapper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_projects_tabs scrollable_tbl">
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('add-'.str_slug('Contact'))): ?>
                            <a class="btn btn-success pull-right" href="<?php echo e(url('/contact/contact/create')); ?>"><i
                                        class="icon-plus"></i> Add <?php echo e(preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Contact')); ?></a>
                        <?php endif; ?>
                        
                        
                        <div class="table-responsive">
                            <div class="custom_table admin_contact_tbl">
                                <table class="table" id="myTable">
                                    <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>First Name</th>
                                        <th>Last Name</th>
                                        <th>Email</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $contact; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($loop->iteration??$item->id); ?></td>
                                            <td><?php echo e($item->first_name); ?></td><td><?php echo e($item->last_name); ?></td><td><?php echo e($item->email); ?></td>
                                            <td>
                                                <div class="contact_action_btn">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-'.str_slug('Contact'))): ?>
                                                        <a href="<?php echo e(url('/contact/contact/' . $item->id)); ?>"
                                                           title="View <?php echo e(preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Contact')); ?>">
                                                            <button class="btn btn_blue">
                                                                <i class="fa-solid fa-eye" aria-hidden="true"></i> View
                                                            </button>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit-'.str_slug('Contact'))): ?>
                                                        <a href="<?php echo e(url('/contact/contact/' . $item->id . '/edit')); ?>"
                                                           title="Edit <?php echo e(preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Contact')); ?>">
                                                            <button class="btn btn_grey">
                                                                <i class="fa-solid fa-pencil-square-o" aria-hidden="true"></i> Edit
                                                            </button>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete-'.str_slug('Contact'))): ?>
                                                        <form method="POST" action="<?php echo e(url('/contact/contact/' . $item->id)); ?>" accept-charset="UTF-8" style="display:inline">
                                                            <?php echo e(method_field('DELETE')); ?>

                                                            <?php echo e(csrf_field()); ?>

                                                            <button type="button" class="btn btn_red"
                                                                    onclick="showDeleteConfirmation(this)">
                                                                <i class="fa-solid fa-trash-o" aria-hidden="true"></i> Delete
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination-wrapper"> <?php echo $contact->appends(['search' => Request::get('search')])->render(); ?> </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('plugins/components/toast-master/js/jquery.toast.js')); ?>"></script>

    <script src="<?php echo e(asset('plugins/components/datatables/jquery.dataTables.min.js')); ?>"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>
        $(document).ready(function () {

            <?php if(\Session::has('message')): ?>
            $.toast({
                heading: 'Success!',
                position: 'top-center',
                text: '<?php echo e(session()->get('message')); ?>',
                loaderBg: '#ff6849',
                icon: 'success',
                hideAfter: 3000,
                stack: 6
            });
            <?php endif; ?>
        })

        $(function () {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });

        });

        $(document).ready(function () {
            $(".custom_table.admin_contact_tbl .dataTables_filter input").attr("placeholder","Search");
        })
    </script>

    <script>
        window.onload = function() {
            function showDeleteConfirmation(button) {
                event.preventDefault();
                Swal.fire({
                    title: 'Confirm Delete',
                    text: 'Are you sure you want to delete this item?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Delete',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        button.closest('form').submit();
                    }
                });
            }

            window.showDeleteConfirmation = showDeleteConfirmation;
        };
    </script>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/contact/contact/index.blade.php ENDPATH**/ ?>