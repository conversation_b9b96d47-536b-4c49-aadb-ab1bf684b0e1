<?php $__env->startPush("css"); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="my_projects_page service_all_projects">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h3 class="projects_title">My Projects</h3>
                </div>
                <div class="col-md-12">
                    <div class="table_box table_padding">
                        <div class="heading_part">
                            <ul class="nav nav-pills " id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active tab_button" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">All</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link tab_button" id="pills-ongoing-tab" data-bs-toggle="pill" data-bs-target="#pills-ongoing" type="button" role="tab" aria-controls="pills-ongoing" aria-selected="false">On-Going</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link tab_button" id="pills-completed-tab" data-bs-toggle="pill" data-bs-target="#pills-completed" type="button" role="tab" aria-controls="pills-completed" aria-selected="false">Completed</button>
                                </li>
                            </ul>
                        </div>
                        <div class="custom_search_filter ">
                            <div class="txt_field custom_search">
                                <input type="text" placeholder="Search" class="custom_search_box">
                            </div>
                        </div>
                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0" >
                                <div class="table-responsive">
                                    <table  class="table myTable datatable">
                                        <thead>
                                            <tr>
                                                <th>Client Name</th>
                                                <th>Location</th>
                                                <th>Project Title</th>
                                                <th>Email</th>
                                                <th>Date</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <a href="<?php echo e(url('service_completed_projects')); ?>/<?php echo e($project->id); ?>" class="table_flex">
                                                        <div class="img_container">
                                                            <?php if(!empty($project->user->profile)): ?>
                                                            <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($project->user->profile->pic); ?>" alt="">
                                                            <?php else: ?>
                                                            <img src="<?php echo e(asset('website')); ?>/assets/images/inside_logo.png" alt="">
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php echo e($project->user->name??'---'); ?>

                                                    </a>
                                                </td>
                                                <td><?php echo e($project->user->name??'---'); ?></td>
                                                <td><?php echo e($project->project_number ?? '---'); ?> /  <?php echo e($project->project_title ?? '---'); ?></td>
                                                <td><?php echo e($project->user->email??'---'); ?></td>
                                                <td><?php echo e($project->created_at->format('d-m-y')??'---'); ?></td>
                                                <td>
                                                    <span class="<?php echo e($project->status == 'completed' ? 'success' : ($project->status == 'on_going' ? 'ongoing' : 'pending')); ?>">
                                                        <?php echo e(isset($project->status) ? str_replace('_', ' ', $project->status) : '----'); ?>

                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                            <li><a class="dropdown-item" href="<?php echo e(url('service_completed_projects')); ?>/<?php echo e($project->id); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pills-ongoing" role="tabpanel" aria-labelledby="pills-ongoing -tab" tabindex="0">
                                <div class="table-responsive">
                                    <table  class="table myTable datatable">
                                        <thead>
                                        <tr>
                                            <th>Client Name</th>
                                            <th>Location</th>
                                            <th>Project Title</th>
                                            <th>Email </th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php $__currentLoopData = $projects->where('status','on_going'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <a href="<?php echo e(url('service_completed_projects')); ?>/<?php echo e($project->id); ?>" class="table_flex">
                                                        <div class="img_container">
                                                            <?php if(!empty($project->user->profile)): ?>
                                                                <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($project->user->profile->pic); ?>" alt="">
                                                            <?php else: ?>
                                                            <img src="<?php echo e(asset('website')); ?>/assets/images/inside_logo.png" alt="">
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php echo e($project->user->name??'---'); ?>

                                                    </a>
                                                </td>
                                                <td><?php echo e($project->user->name??'---'); ?></td>
                                                <td><?php echo e($project->project_number ?? '---'); ?> /  <?php echo e($project->project_title ?? '---'); ?></td>
                                                <td><?php echo e($project->user->email??'---'); ?></td>
                                                <td><?php echo e($project->created_at->format('d-m-y')??'---'); ?></td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                            <li><a class="dropdown-item" href="<?php echo e(url('service_completed_projects')); ?>/<?php echo e($project->id); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pills-completed" role="tabpanel" aria-labelledby="pills-completed-tab" tabindex="0">
                                <div class="table-responsive">
                                    <table  class="table myTable datatable">
                                        <thead>
                                        <tr>
                                            <th>Client Name</th>
                                            <th>Location</th>
                                            <th>Project Title</th>
                                            <th>Email </th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php $__currentLoopData = $projects->where('status','completed'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <a href="<?php echo e(url('service_completed_projects')); ?>/<?php echo e($project->id); ?>" class="table_flex">
                                                        <div class="img_container">
                                                            <?php if(!empty($project->user->profile)): ?>
                                                                <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($project->user->profile->pic); ?>" alt="">
                                                            <?php else: ?>
                                                            <img src="<?php echo e(asset('website')); ?>/assets/images/inside_logo.png" alt="">
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php echo e($project->user->name??'---'); ?>

                                                    </a>
                                                </td>
                                                <td><?php echo e($project->user->name??'---'); ?></td>
                                                <td><?php echo e($project->project_number ?? '---'); ?> /  <?php echo e($project->project_title ?? '---'); ?></td>
                                                <td><?php echo e($project->user->email??'---'); ?></td>
                                                <td><?php echo e($project->created_at->format('d-m-y')??'---'); ?></td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                            <li><a class="dropdown-item" href="<?php echo e(url('service_completed_projects')); ?>/<?php echo e($project->id); ?>" ><i class="fa-regular fa-eye"></i> View</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(function() {
            var dataTable = $('.myTable'). DataTable({
                "searching": true,
                "bLengthChange": false,
                "paging": true,
                "info": true,
            });
            $(document).on("input", '.custom_search_box', function () {
                var searchValue = $(this).val();
                dataTable.search(searchValue).draw();
            });
        });

    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/serviceProvider/all_projects.blade.php ENDPATH**/ ?>