<?php $__env->startPush("css"); ?>
    <style>
        header,footer{display: none;}
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <section id="wrapper" class="login_register">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-7 col-md-6 custom_column_padding">
                    <div class="custom_banner">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                            <div class="banner_content custom_login_title">
                                <a href="<?php echo e(url('home')); ?>">
                                    <div class="site_logo">
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png">
                                    </div>
                                </a>
                                <div class="site_title">
                                    <h1>Tackle any home improvement project, effortlessly</h1>
                                </div>
                                <div class="site_key_parameter">
                                    <a href="javascript:void(0)"><i class="fa-regular fa-circle-check"></i>Free consultation</a>
                                    <a href="javascript:void(0)"><i class="fa-solid fa-award"></i>Satisfaction Guaranteed</a>
                                    <a href="javascript:void(0)"><img src="<?php echo e(asset('website')); ?>/assets/images/banner_sheild.png">Protected Payments</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5 col-md-6 custom_column_padding">
                    <div class="login_box">
                        <div class="login_back_btn">
                            <a href="<?php echo e(route('login')); ?>" class="prev_btn"><i class="fa-solid fa-chevron-left"></i></a>
                        </div>
                        <form class="form-horizontal form-material" method="POST" action="<?php echo e(route('password.email')); ?>">
                            <?php echo csrf_field(); ?>
                            <?php if(session('status')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('status')); ?>

                                </div>
                            <?php endif; ?>
                            <div class="authentication_text">
                                <h1><?php echo e(__('Forgot Password')); ?></h1>
                                <h6>Enter your email for the verification process, we will send reset password to your email.</h6>
                            </div>
                            <div class="txt_field">
                                <label for="" class="form-label">Email</label>
                                <input id="email" placeholder="Email" type="email" class="form-control<?php echo e($errors->has('email') ? ' is-invalid' : ''); ?>" name="email" value="<?php echo e(old('email')); ?>" required>
                                <?php if($errors->has('email')): ?>
                                    <span class="invalid-feedback">
                                        <strong><?php echo e($errors->first('email')); ?></strong>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="submit_btn">
                                <button class="btn btn_black" type="submit">Reset <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>





                
                    
                        
                            
                        
                        
                            
                                
                                    
                                
                                
                                    
                                
                                
                                    
                                    
                                    
                                
                            
                        
                    
                


                        
                            
                        







































<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/auth/passwords/email.blade.php ENDPATH**/ ?>