<?php $__env->startPush("css"); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/chat.css')); ?>" />
    <script src="https://code.jquery.com/jquery-1.11.1.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="project_overview">
        <div class="container">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="ongoing_milestone">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <h3> <?php echo e($job->project_title ?? '---'); ?> </h3>
                            </div>
                            <div class="custom_milestone_column">
                                <div class="milestone_card">
                                    <h5><?php echo e(str_replace('_', ' ', $job->status ?? '----')); ?></h5>
                                    <h5>Status</h5>
                                </div>




                                <div class="milestone_card">
                                    <h3>$  <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->sum('amount') ?? '0'); ?> <?php else: ?> 00 <?php endif; ?></h3>
                                    <h5>In Escrow</h5>
                                </div>
                                <div class="milestone_card">
                                    <h3>$ <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','released')->sum('amount') ?? '0'); ?> <?php else: ?> 00 <?php endif; ?> </h3>
                                    <h5>Milestones Paid: <span><?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','released')->count()??'0'); ?> <?php else: ?> 00 <?php endif; ?></span></h5>
                                </div>
                                <div class="milestone_card">
                                    <h3>$ <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','!=','released')->sum('amount')); ?> <?php else: ?> 00 <?php endif; ?></h3>
                                    <h5>Milestones Remaining: <span> <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','!=','released')->count() ?? '0'); ?> <?php else: ?> 00 <?php endif; ?> </span></h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="projects_scope">
                        <div class="view_profile">
                            <div class="project_title">
                                <div class="project_logo">
                                    <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($job->assignSeller->profile->pic??''); ?>">
                                </div>
                                <h5><?php echo e($job->assignSeller->name??''); ?></h5>
                            </div>
                            <div class="view_btn">
                                <a href="<?php echo e(url('buyer_chat')); ?>" class="btn btn_black"><i class="fa-solid fa-envelope"></i></a>
                                <a href="<?php echo e(url('service_profile')); ?>/<?php echo e($job?->assignSeller->id??0); ?>" class="btn btn_black">View Profile</a>
                            </div>
                        </div>
                        <div class="project_desc">
                            <h5>Description</h5>
                            <h6><?php echo e($job->assignSeller->profile->bio??''); ?></h6>
                            <div class="custom_display_flex">
                            <a href="<?php echo e(url('posted_view')); ?>/<?php echo e($job->id); ?>" class="btn btn_blue"> View Original Project Listing <span><i class="fa-solid fa-arrow-right"></i> </span></a>
                                <?php if(isset($milestones) && $milestones->isNotEmpty() && $milestones->count() === $milestones->where('requested', 'released')->count()): ?>
                                    <?php if($job->status != 'completed'): ?>
                                    <a href="<?php echo e(url('update_status_job')); ?>/<?php echo e($job->id); ?>/completed" class="btn btn_blue">
                                        Close Project <span><i class="fa-solid fa-arrow-right"></i> </span>
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                                <?php if($job->status == 'completed'): ?>
                                    <?php if($job->getJobReview->where('sender_id',auth()->user()->id)->count() == 0): ?>
                                        <a href="#!" data-bs-toggle="modal" data-bs-target="#service_rating" class="btn btn_dark_green">Leave Feedback<span><i class="fa-solid fa-arrow-right"></i> </span></a>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="scope_desc">
                            <h5>Review & Rating</h5>
                                <?php $__empty_1 = true; $__currentLoopData = $job->getJobReview->where('sender_id', $job->assign_seller_id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="seller_rating">
                                    <?php
                                        $stars = $review->stars ?? 0; // Get the star rating, default to 0 if not set
                                    ?>
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <span class="fa fa-star <?php echo e($i <= $stars ? 'checked' : ''); ?>"></span>
                                    <?php endfor; ?>
                                    <h6><?php echo e($stars); ?>.0 Stars</h6>
                                </div>
                                <h6><?php echo e($review->review??'0'); ?></h6>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <h6>No Review Found</h6>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="project_ongoing custom_chats project_overview">
                        <div class="custom_timeline">
                            <ul class="nav nav-pills " id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active tab_button" id="pills-milestone-tab" data-bs-toggle="pill" data-bs-target="#pills-milestone" type="button" role="tab" aria-controls="pills-milestone" aria-selected="true">Milestone</button>
                                </li>





                                <li class="nav-item <?php echo e($dispute != 0 ? '' : 'd-none'); ?>" id="dispute-tab-wrapper" role="presentation">

                                    <button class="nav-link tab_button" id="pills-dispute-tab"
                                            data-bs-toggle="pill"
                                            data-bs-target="#pills-dispute"
                                            type="button"
                                            role="tab"
                                            aria-controls="pills-dispute"
                                            aria-selected="false">
                                        Dispute
                                    </button>
                                </li>

                            </ul>
                        </div>
                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-milestone" role="tabpanel" aria-labelledby="pills-milestone-tab" tabindex="0" >
                                <div class="custom_timeline">
                                    <h3>Milestone</h3>
                                    <div class="custom_btn">
                                        <div class="dispute_btn">

                                                
                                            <button type="button" class="btn btn_red" data-bs-toggle="modal" data-bs-target="#dispute_milestone">Dispute<span><i class="fa-solid fa-arrow-right"></i> </span></button>

                                        </div>
                                        
                                            
                                        
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table myTable datatable">
                                        <tbody>
                                        <?php if(isset($milestones) && !empty($milestones)): ?>
                                            <?php $__currentLoopData = $milestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($loop->iteration ?? $key); ?></td>
                                                    <td>
                                                        <div class="milestone_icon">
                                                            <span><i class="fa-solid fa-flag"></i></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="milestone_card">
                                                            <h5><?php echo e($milestone->title??'---'); ?></h5>
                                                            <p>$<?php echo e($milestone->amount??'0'); ?></p>
                                                            <span class="due_date"><i class="fa-regular fa-calendar"></i><?php echo e($milestone->date??'---'); ?></span>
                                                        </div>
                                                    </td>

                                                    <?php if($milestone->requested == 'requested' || $milestone->requested == 'released'): ?>
                                                    <td>
                                                        <div class="release_btn">
                                                            <a href="#!" class="btn btn_pink view_requested_modal" data-id="<?php echo e($milestone->id); ?>">view
                                                                <span><img src="<?php echo e(asset('website')); ?>/assets/images/bell-on.png"></span>
                                                            </a>
                                                        </div>
                                                    </td>
                                                        <td>
                                                            <div class="release_btn">
                                                                <a href="#!" class="btn  <?php if($milestone->requested == 'requested'): ?>btn_pink  request_btn_doc <?php else: ?> btn_green <?php endif; ?>" data-id="<?php echo e($milestone->id); ?>"><?php if($milestone->requested == 'released'): ?> Released <?php else: ?> Release <?php endif; ?>
                                                                    <?php if($milestone->requested == 'requested'): ?> <span><img src="<?php echo e(asset('website')); ?>/assets/images/bell-on.png"></span>
                                                                    <?php else: ?>
                                                                        <i class="fa-solid fa-circle-check"></i>
                                                                    <?php endif; ?>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    <?php else: ?>
                                                        <td>
                                                            <div class="release_btn">
                                                                <a href="#!" class="btn btn_blue">Release
                                                                    <span><img src="<?php echo e(asset('website')); ?>/assets/images/bell-on.png"></span>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    <?php endif; ?>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pills-dispute" role="tabpanel" aria-labelledby="pills-dispute -tab" tabindex="0">
                                <div class="row" id="dispute_milestone_chat_div">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    

    <div class="modal fade service_rating feedbackService" id="service_rating" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="custom_img">
                        <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png">
                    </div>
                    <div class="seller_name">
                        <h3>Client :  <?php echo e($job->user->name); ?></h3>
                        <div class="seller_rating">
                            <span class="fa fa-star <?php if($job->user->ratingSum >= 1): ?> checked <?php endif; ?>"></span>
                            <span class="fa fa-star <?php if($job->user->ratingSum >= 2): ?> checked <?php endif; ?>"></span>
                            <span class="fa fa-star <?php if($job->user->ratingSum >= 3): ?> checked <?php endif; ?>"></span>
                            <span class="fa fa-star <?php if($job->user->ratingSum >= 4): ?> checked <?php endif; ?>"></span>
                            <span class="fa fa-star <?php if($job->user->ratingSum >= 5): ?> checked <?php endif; ?>"></span>
                            <h6><?php echo e($job->user->ratingSum); ?> Stars</h6>
                        </div>
                    </div>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('store_review_rating')); ?>" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="row custom_row">
                            <div class="col-md-6 col-sm-6">
                                <div class="txt_field">
                                    <label>Location:</label>
                                    <span><?php echo e($job->user->profile->country); ?> <?php echo e($job->user->profile->city); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="txt_field">
                                    <label>Budget:</label>
                                    <span>$<?php echo e($job->project_budget_min); ?> - $<?php echo e($job->project_budget_max); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Job Type:</label>
                                    <span><?php echo e($job->category->name); ?> - <?php echo e($job->subCategory->name); ?></span>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="custom_rating_star">
                                    <h6>Rate Me</h6>
                                    <div class="txt_field">
                                        <div class="rating_star wrapper">
                                            <input type="radio" name="rating" id="st1" value="5" />
                                            <label for="st1"></label>
                                            <input type="radio" name="rating" id="st2" value="4" />
                                            <label for="st2"></label>
                                            <input type="radio" name="rating" id="st3" value="3" />
                                            <label for="st3"></label>
                                            <input type="radio" name="rating" id="st4" value="2" />
                                            <label for="st4"></label>
                                            <input type="radio" name="rating" id="st5" value="1" />
                                            <label for="st5"></label>
                                            <input type="hidden" name="user_id" value="<?php echo e($job->assign_seller_id); ?>">
                                            <input type="hidden" name="job_id" value="<?php echo e($job->id); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Enter Review</label>
                                    <textarea class="form-control" rows="4" name="review" placeholder="Type Here"></textarea>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    
    <div class="modal fade create_milestone " id="create_milestone" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Create Milestone</h3>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="new_milestone">
                                    <span>05</span>
                                    <h6>Milestone Details</h6>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Title</label>
                                    <input type="text" placeholder="Title Here" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Milestone Amount</label>
                                    <input type="text" placeholder="0000" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Due Date</label>
                                    <input type="date" placeholder="DD/MM/YYYY" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade dispute_milestone disputeModalMilestone" id="dispute_milestone" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Dispute Milestone</h3>
                </div>
                <div class="modal-body">
                    <form id="disputeMilestoneForm">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Dispute Title</label>
                                    <input type="text" placeholder="Enter Dispute Title" id="dispute_title" class="form-control" required>
                                    <div class="invalid-feedback">Dispute title is required.</div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Disputed Milestone</label>
                                    <select class="form-control form-select" id="disputed_milestone_id" required>

                                        <?php if(isset($milestones) && !empty($milestones)): ?>

                                            <?php $__currentLoopData = $milestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                                <option value="<?php echo e($milestone->id); ?>"><?php echo e($milestone->title); ?> -  <?php echo e($milestone->amount); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            <input type="hidden" name="job_id" id="job_id" value="<?php echo e($job->id); ?>">
                            <input type="hidden" name="job_offer_id" id="job_offer_id" value="<?php echo e($job->jobOffersPaid->id??''); ?>">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Enter Message</label>
                                    <textarea placeholder="Enter Comments" id="enter_message" rows="5" class="form-control" required></textarea>
                                    <div class="invalid-feedback">Message is required.</div>
                                </div>
                            </div>

















                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="button" id="dispute_milestone_button" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade view_milestone_requested" id="view_milestone_requested" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">

    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script src="https://js.pusher.com/4.1/pusher.min.js"></script>
<script src="<?php echo e(asset('js/chat.js')); ?>"></script>
<script src="https://malsup.github.io/jquery.form.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.25/dist/sweetalert2.min.js"></script>
<script>


    window.onload = function()
    {
        const urlParams = new URLSearchParams(window.location.search);
        const openDisputeModal = urlParams.get('opendisputeModalMilestone');

        if (openDisputeModal === 'true') {
            $('.disputeModalMilestone').modal('show');
        }
    };

    window.onload = function() {
        const urlParams = new URLSearchParams(window.location.search);
        const openfeedbackService = urlParams.get('openfeedbackService');

        console.log('openfeedbackService:', openfeedbackService);

        if (openfeedbackService === 'true') {
            $('.feedbackService').modal('show');
        }
    };


    $(document).ready(function () {

        $('.send_msg button').attr('disabled','disabled');
        $('.send_msg input[type="text"]').keyup(function(){
            if($(this).val().length > 0){
                $('.send_msg button').removeAttr('disabled');
            }
            else if($(this).val().length == 0){
                $('.send_msg button').attr('disabled','disabled');
            }
        });


        function scrollChatToBottom() {
            const $chatMessages = $('.custom_chats .users_chats .chat_messages');
            if ($chatMessages.length > 0) {
                $chatMessages.animate({
                    scrollTop: $chatMessages[0].scrollHeight
                }, 500); // Reduced animation time for better UX
            }
        }

        const chatArea = document.getElementById('chat-area');
        if (chatArea) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes.length) {
                        scrollChatToBottom();
                    }
                });
            });

            observer.observe(chatArea, {
                childList: true,
                subtree: true
            });
        }

        $(document).on("click", ".custom_chats .users_chats .send_message", function () {
            const $chatMessages = $('.custom_chats .users_chats .chat_messages');
            if ($chatMessages.length > 0) {
                $chatMessages.animate({
                    scrollTop: $chatMessages[0].scrollHeight
                }, 2000);
            }
        });

        $(".searchbar_input .custom_search_box").on("keyup", function () {
            var searchValue = $(this).val().toLowerCase();

            $(".custom_chats .all_users_chats .user_profile_msg").each(function () {
                var titleText = $(this).find(".user_msg h6").text().toLowerCase();

                if (titleText.includes(searchValue)) {
                    $(this).closest('.custom_chats .all_users_chats li').show();
                } else {
                    $(this).closest('.custom_chats .all_users_chats li').hide();
                }
            });
        });
        $(".searchbar_input .custom_search_box").on("input", function () {
            if ($(this).val() === "") {
                $(".custom_chats .all_users_chats li").show();
            }
        });

        $(".custom_chats .all_users_chats li:first-child a.chat-box-toggle").trigger('click');

        $(document).on("click", ".custom_chats .all_users_chats a.chat-box-toggle", function () {
            $(".custom_chats .users_chats .chat_messages").animate({
                scrollTop: $('.custom_chats .users_chats .chat_messages').get(0).scrollHeight
            }, 2000);
        });

        $(window).on('load', function(){
            $(".custom_chats .users_chats .chat_messages").animate({
                scrollTop: $('.custom_chats .users_chats .chat_messages').get(0).scrollHeight
            }, 2000);
        });
    });
    $(document).on('click', '.view_requested_modal', function() {
        var view_requested_id = $(this).attr('data-id');
        $.ajax({
            url: '<?php echo e(url('view_milestone_requested_doc')); ?>/' + view_requested_id, //Replace with your desired URL
            success: function(response) {
                $('#view_milestone_requested').html(response);
                $('#view_milestone_requested').modal('show');
            },
        });
    });


    $(document).on('click', '#dispute_milestone_button', function() {
        let dispute_title  = $('#dispute_title').val();
        let disputed_milestone_id = $('#disputed_milestone_id').val();
        let job_id         = $('#job_id').val();
        let job_offer_id   = $('#job_offer_id').val();
        let enter_message  = $('#enter_message').val();
        let outcome        = 'continue';
        const $button = $(this);
        const $title = $('#dispute_title');
        const $message = $('#enter_message');
        let isValid = true;
        $('.form-control').removeClass('is-invalid');
        if ($title.val().trim().length < 3) {
            $title.addClass('is-invalid');
            isValid = false;
        }
        if ($message.val().trim().length < 3) {
            $message.addClass('is-invalid');
            isValid = false;
        }
        if (!isValid) return;
        $button.prop('disabled', true).html('Processing... <span><i class="fa-solid fa-spinner fa-spin"></i></span>');
        // let outcome        = $('input[name="outcome"]:checked').val();

        if (!dispute_title || !disputed_milestone_id || !job_id || !job_offer_id || !enter_message) {

            Swal.fire({
                title: "Error",
                text: "All fields are required!",
                icon: "error",
            });
            return;

        }

        $.ajax({
            url: '<?php echo e(url('dispute_milestone')); ?>',
            method: 'GET', // or POST depending on your requirement
            data: {
                dispute_title: dispute_title,
                disputed_milestone_id: disputed_milestone_id,
                job_id: job_id,
                job_offer_id: job_offer_id,
                enter_message: enter_message,
                outcome: outcome
            },
            success: function(response) {
                Swal.fire({
                    title: response.title,
                    text: response.message,
                    icon: response.type,
                }).then(() => {
                    // window.location.reload();
                    $('#dispute_milestone').modal('hide');
                    $('#disputeMilestoneForm')[0].reset();
                    $('#dispute-tab-wrapper').removeClass('d-none');
                    const disputeTab = new bootstrap.Tab(document.querySelector('#pills-dispute-tab'));
                    disputeTab.show();
                });
                        dispute_milestone_chat(response.id)
            },
            complete: function () {

                $button.prop('disabled', false).html('Submit <span><i class="fa-solid fa-arrow-right"></i></span>');
            }
        });
    });
    $(document).on('click', '.request_btn_doc', function() {
        var view_requested_id = $(this).attr('data-id');
        var status = 'released';
        Swal.fire({
            title: 'Are you sure?',
            text: 'Do you want to submit this request?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, submit it!',
            cancelButtonText: 'No, cancel!',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while we release your request.',
                    icon: 'info',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                $.ajax({
                    url: '<?php echo e(url('milestone_updated_release_status')); ?>/' + view_requested_id + '/' + status,
                    method: 'GET', // or POST depending on your requirement
                    success: function(response) {
                        Swal.fire({
                            title: response.title,
                            text: response.message,
                            icon: response.type,
                        }).then(() => {
                            window.location.reload();
                        });
                    },
                });
            } else {
                // If user cancels, show a message
                Swal.fire(
                    'Cancelled',
                    'Your request was not submitted.',
                    'info'
                );
            }
        });
    });
    dispute_milestone_chat()
    function dispute_milestone_chat(id=0){

        job_id = $('#job_id').val()
        $.ajax({
            url: '<?php echo e(url('dispute_milestone_chat')); ?>',
            method: 'GET', // or POST depending on your requirement
                    data:{job_id:job_id,id:id},
            success: function(response) {
               $('#dispute_milestone_chat_div').html(response)
                // $('#dispute_milestone').modal('hide');
                // $('#disputeMilestoneForm')[0].reset();
                // const disputeTab = new bootstrap.Tab(document.querySelector('#pills-dispute-tab'));
                // disputeTab.show();
            },
        });
    }

</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/buyer/my_projects_ongoing_view.blade.php ENDPATH**/ ?>