<nav class="navbar navbar-default custom_navbar navbar-static-top m-b-0">
    <div class="navbar-header">
        <a class="navbar-toggle font-20 hidden-sm hidden-md hidden-lg " href="javascript:void(0)" data-toggle="collapse"
           data-target=".navbar-collapse">
            <i class="fa fa-bars"></i>
        </a>
        <?php if(auth()->user()->hasRole('admin')): ?>
            <div class="top-left-part">
                <a class="logo" href="<?php echo e(url('/')); ?>">
                    <b>
                        <img src="<?php echo e(asset('')); ?><?php echo e(App\CommonSetting::first()->dashboard_logo??''); ?>" alt="home" style="height: 30px" />
                    </b>
                    <span>
                        <img src="<?php echo e(asset('')); ?><?php echo e(App\CommonSetting::first()->dashboard_logo_text??''); ?>" alt="homepage" class="dark-logo" style="height: 50px;width: 73px" />
                    </span>
                </a>
            </div>
        <?php endif; ?>
        <ul class="nav navbar-top-links navbar-left hidden-xs">
            <?php if(session()->get('theme-layout') != 'fix-header'): ?>
                <li class="sidebar-toggle">
                    <a href="javascript:void(0)" class="sidebartoggler font-20 waves-effect waves-light"><i class="icon-arrow-left-circle"></i></a>
                </li>
            <?php endif; ?>


            
            
            
            
            
            
        </ul>
        <div class="navbar_top">
            <div class="custom_nav_heading">
                <?php echo $__env->yieldContent('backbtn_nav'); ?>
            <div class="navbar_heading">
                <?php echo $__env->yieldContent('navbar-title'); ?>
            </div>
            </div>

            <?php if(auth()->user()->hasRole('admin')): ?>
                <ul class="nav navbar-top-links navbar-right pull-right">
                    <li class="dropdown">
                        <a class="dropdown-toggle waves-effect waves-light font-20" data-toggle="dropdown"
                           href="javascript:void(0);">
                            <i class="icon-speech"></i>
                            <span class="badge badge-xs badge-danger">6</span>
                        </a>
                        <ul class="dropdown-menu mailbox animated bounceInDown">
                            <li>
                                <div class="drop-title">You have 4 new messages</div>
                            </li>
                            <li>
                                <div class="message-center">
                                    <a href="javascript:void(0);">
                                        <div class="user-img">
                                            <img src="<?php echo e(asset('plugins/images/users/1.jpg')); ?>" alt="user" class="img-circle">
                                            <span class="profile-status online pull-right"></span>
                                        </div>
                                        <div class="mail-contnet">
                                            <h5>Pavan kumar</h5>
                                            <span class="mail-desc">Just see the my admin!</span>
                                            <span class="time">9:30 AM</span>
                                        </div>
                                    </a>
                                    <a href="javascript:void(0);">
                                        <div class="user-img">
                                            <img src="<?php echo e(asset('plugins/images/users/2.jpg')); ?>" alt="user" class="img-circle">
                                            <span class="profile-status busy pull-right"></span>
                                        </div>
                                        <div class="mail-contnet">
                                            <h5>Sonu Nigam</h5>
                                            <span class="mail-desc">I've sung a song! See you at</span>
                                            <span class="time">9:10 AM</span>
                                        </div>
                                    </a>
                                    <a href="javascript:void(0);">
                                        <div class="user-img">
                                            <img src="<?php echo e(asset('plugins/images/users/3.jpg')); ?>" alt="user"
                                                 class="img-circle"><span class="profile-status away pull-right"></span>
                                        </div>
                                        <div class="mail-contnet">
                                            <h5>Arijit Sinh</h5>
                                            <span class="mail-desc">I am a singer!</span>
                                            <span class="time">9:08 AM</span>
                                        </div>
                                    </a>
                                    <a href="javascript:void(0);">
                                        <div class="user-img">
                                            <img src="<?php echo e(asset('plugins/images/users/4.jpg')); ?>" alt="user" class="img-circle">
                                            <span class="profile-status offline pull-right"></span>
                                        </div>
                                        <div class="mail-contnet">
                                            <h5>Pavan kumar</h5>
                                            <span class="mail-desc">Just see the my admin!</span>
                                            <span class="time">9:02 AM</span>
                                        </div>
                                    </a>
                                </div>
                            </li>
                            <li>
                                <a class="text-center" href="javascript:void(0);">
                                    <strong>See all notifications</strong>
                                    <i class="fa fa-angle-right"></i>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <a class="dropdown-toggle waves-effect waves-light font-20" data-toggle="dropdown"
                           href="javascript:void(0);">
                            <i class="icon-calender"></i>
                            <span class="badge badge-xs badge-danger">3</span>
                        </a>
                        <ul class="dropdown-menu dropdown-tasks animated slideInUp">
                            <li>
                                <a href="javascript:void(0);">
                                    <div>
                                        <p>
                                            <strong>Task 1</strong>
                                            <span class="pull-right text-muted">40% Complete</span>
                                        </p>
                                        <div class="progress progress-striped active">
                                            <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="40"
                                                 aria-valuemin="0" aria-valuemax="100" style="width: 40%">
                                                <span class="sr-only">40% Complete (success)</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="javascript:void(0);">
                                    <div>
                                        <p>
                                            <strong>Task 2</strong>
                                            <span class="pull-right text-muted">20% Complete</span>
                                        </p>
                                        <div class="progress progress-striped active">
                                            <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="20"
                                                 aria-valuemin="0" aria-valuemax="100" style="width: 20%">
                                                <span class="sr-only">20% Complete</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="javascript:void(0);">
                                    <div>
                                        <p>
                                            <strong>Task 3</strong>
                                            <span class="pull-right text-muted">60% Complete</span>
                                        </p>
                                        <div class="progress progress-striped active">
                                            <div class="progress-bar progress-bar-warning" role="progressbar" aria-valuenow="60"
                                                 aria-valuemin="0" aria-valuemax="100" style="width: 60%">
                                                <span class="sr-only">60% Complete (warning)</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="javascript:void(0);">
                                    <div>
                                        <p>
                                            <strong>Task 4</strong>
                                            <span class="pull-right text-muted">80% Complete</span>
                                        </p>
                                        <div class="progress progress-striped active">
                                            <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="80"
                                                 aria-valuemin="0" aria-valuemax="100" style="width: 80%">
                                                <span class="sr-only">80% Complete (danger)</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a class="text-center" href="javascript:void(0);">
                                    <strong>See All Tasks</strong>
                                    <i class="fa fa-angle-right"></i>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="right-side-toggle">
                        <a class="right-side-toggler waves-effect waves-light b-r-0 font-20" href="javascript:void(0)">
                            <i class="icon-settings"></i>
                        </a>
                    </li>
                </ul>
            <?php else: ?>
                <ul class="nav navbar-top-links navbar-right pull-right">
                    <li class="dropdown">
                        <a class="dropdown-toggle waves-effect waves-light font-20 custom_notification" data-toggle="dropdown"
                           href="javascript:void(0);">
                            <i class="fa-solid fa-bell bell_icon"></i>
                            <span class="badge badge-xs badge-danger">1</span>
                        </a>
                        <ul class="dropdown-menu mailbox animated bounceInDown">
                            <div class="notification_top">
                                <h4>Notifications</h4>
                                <a href="<?php echo e(url('notifications')); ?>"><h6>View All</h6></a>
                            </div>
                            <li>
                                <div class="message-center">
                                    <a href="#!">
                                        <div class="navbar_notification ">
                                            <div class="user_profile">
                                                <div class="profile_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/user_img.png">
                                                </div>
                                                <div>
                                                    <h6>John Doe</h6>
                                                    <p>It is a long established fact that a reader will be distracted</p>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <span><i class="fa-solid fa-circle"></i></span>
                                                <h5> 00 min ago</h5>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#!">
                                        <div class="navbar_notification ">
                                            <div class="user_profile">
                                                <div class="profile_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/user_img.png">
                                                </div>
                                                <div>
                                                    <h6>John Doe</h6>
                                                    <p>It is a long established fact that a reader will be distracted</p>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <span><i class="fa-solid fa-circle"></i></span>
                                                <h5> 00 min ago</h5>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#!">
                                        <div class="navbar_notification ">
                                            <div class="user_profile">
                                                <div class="profile_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/user_img.png">
                                                </div>
                                                <div>
                                                    <h6>John Doe</h6>
                                                    <p>It is a long established fact that a reader will be distracted</p>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <span><i class="fa-solid fa-circle"></i></span>
                                                <h5> 00 min ago</h5>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#!">
                                        <div class="navbar_notification ">
                                            <div class="user_profile">
                                                <div class="profile_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/user_img.png">
                                                </div>
                                                <div>
                                                    <h6>John Doe</h6>
                                                    <p>It is a long established fact that a reader will be distracted</p>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <span><i class="fa-solid fa-circle"></i></span>
                                                <h5> 00 min ago</h5>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#!">
                                        <div class="navbar_notification ">
                                            <div class="user_profile">
                                                <div class="profile_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/user_img.png">
                                                </div>
                                                <div>
                                                    <h6>John Doe</h6>
                                                    <p>It is a long established fact that a reader will be distracted</p>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <span><i class="fa-solid fa-circle"></i></span>
                                                <h5> 00 min ago</h5>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </li>
                    <li class="dropdown profile_setting_dropdown">
                        <a href="#!" class="dropdown-toggle" data-toggle="dropdown">
                            <div class="user_img">
                                <?php if(auth()->user()->profile->pic == null): ?>
                                    <img src="<?php echo e(asset('/website/assets/images/user_image.png')); ?>" class="img-circle" alt="user" />
                                <?php else: ?>
                                    <div class="profile_box">
                                        <img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" class="img-circle"
                                             alt="user-img" />
                                    </div>

                                    <div class="username">
                                        <?php if(auth()->user()->hasRole('admin')): ?>
                                           Admin
                                        <?php elseif(auth()->user()->hasRole('user')): ?>
                                         <?php echo e(auth()->user()->first_name ??''); ?>

                                        <?php else: ?>
                                           <?php echo e(auth()->user()->name ??''); ?>

                                        <?php endif; ?>
                                        
                                    </div>
                                    
                                <?php endif; ?>
                            </div>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="" href="<?php echo e(url('account-settings')); ?>" aria-expanded="false"><i class="fa-solid fa-gear"></i>Account Settings</a></li>
                            <li><a class="" href="<?php echo e(url('logout')); ?>" aria-expanded="false"><i class="fa-solid fa-arrow-left"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            <?php endif; ?>
        </div>
    </div>
</nav>
<?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/layouts/partials/navbar.blade.php ENDPATH**/ ?>