<?php $__env->startPush("css"); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/chat.css')); ?>" />
    <script src="https://code.jquery.com/jquery-1.11.1.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
   <section class="project_overview seller_dispute_chat">
        <div class="container">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="ongoing_milestone">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <h3> <?php echo e($job->project_title ?? '---'); ?> </h3>
                            </div>
                            <div class="custom_milestone_column">
                                <div class="milestone_card">
                                    <h5><?php echo e(str_replace('_', ' ', $job->status ?? '----')); ?></h5>
                                    <h5> Status </h5>
                                </div>
                                <div class="milestone_card">
                                    <h3>$  <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->sum('amount') ?? '0'); ?> <?php else: ?> 00 <?php endif; ?></h3>
                                    <h5>In Escrow</h5>
                                </div>
                                <div class="milestone_card">
                                    <h3>$ <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','released')->sum('amount') ?? '0'); ?> <?php else: ?> 00 <?php endif; ?> </h3>
                                    <h5>Milestones Paid: <span><?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','released')->count()??'0'); ?> <?php else: ?> 00 <?php endif; ?></span></h5>
                                </div>
                                <div class="milestone_card">
                                    <h3>$ <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','!=','released')->sum('amount')); ?> <?php else: ?> 00 <?php endif; ?></h3>
                                    <h5>Milestones Remaining: <span> <?php if(isset($milestones) && !empty($milestones)): ?> <?php echo e($milestones->where('requested','!=','released')->count() ?? '0'); ?> <?php else: ?> 00 <?php endif; ?> </span></h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="projects_scope">
                        <div class="view_profile">
                            <div class="project_title">
                                <div class="project_logo">
                                    <img src="<?php echo e(asset('storage/uploads/users/' . $job?->user?->profile->pic)); ?>">
                                </div>
                                <h5><?php echo e($job->user->name ?? '---'); ?></h5>
                            </div>
                            <div class="view_btn">
                                <a href="<?php echo e(url('service_provider_chat')); ?>" class="btn btn_black"><i class="fa-solid fa-envelope"></i></a>
                                <a href="<?php echo e(url('service_profile')); ?>/<?php echo e($job?->assignSeller->id??0); ?>" class="btn btn_black">View Profile</a>
                            </div>
                        </div>

















                        
                            
                        
                        <div class="project_desc">

                            <div class="custom_display_flex">
                                <a href="<?php echo e(url('posted_view')); ?>/<?php echo e($job->id); ?>" class="btn btn_blue">View Original Project Listing <span><i class="fa-solid fa-arrow-right"></i> </span></a>
                                <?php if($job->status == 'completed'): ?>
                                      <?php if($job->getJobReview->where('sender_id',auth()->user()->id)->count() == 0): ?>
                                            <a href="#!" data-bs-toggle="modal" data-bs-target="#service_rating" class="btn btn_dark_green">Leave Feedback<span><i class="fa-solid fa-arrow-right"></i> </span></a>
                                      <?php endif; ?>
                                <?php endif; ?>
                                <div class="view_contract"><a href="<?php echo e(url('project_contract')); ?>/<?php echo e($jobOffer->id); ?>/<?php echo e($job->id); ?>" class="btn btn_yellow" >View Contract <span><i class="fa-solid fa-arrow-right"></i> </span></a></div>
                            </div>
                        </div>
                        <div class="scope_desc">
                            <h5>Review & Rating</h5>
                            <?php $__empty_1 = true; $__currentLoopData = $job->getJobReview->where('sender_id', $job->user_id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="seller_rating">
                                    <?php
                                        $stars = $review->stars ?? 0; // Get the star rating, default to 0 if not set
                                    ?>
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <span class="fa fa-star <?php echo e($i <= $stars ? 'checked' : ''); ?>"></span>
                                        <?php endfor; ?>
                                    <h6><?php echo e($stars); ?>.0 Stars</h6>
                                </div>
                                <h6><?php echo e($review->review??'0'); ?></h6>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <h6>No Review Found</h6>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="project_ongoing custom_chats project_overview">
                        <div class="custom_timeline">
                            <ul class="nav nav-pills " id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active tab_button" id="pills-milestone-tab" data-bs-toggle="pill" data-bs-target="#pills-milestone" type="button" role="tab" aria-controls="pills-milestone" aria-selected="true">Milestone</button>
                                </li>

                                
                                
                                


                                <li class="nav-item <?php echo e((isset($dispute) && $dispute != 0) ? '' : 'd-none'); ?>" id="dispute-tab-wrapper" role="presentation">

                                    <button class="nav-link tab_button" id="pills-dispute-tab"
                                            data-bs-toggle="pill"
                                            data-bs-target="#pills-dispute"
                                            type="button"
                                            role="tab"
                                            aria-controls="pills-dispute"
                                            aria-selected="false">
                                        Dispute
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-milestone" role="tabpanel" aria-labelledby="pills-milestone-tab" tabindex="0" >
                                <div class="custom_timeline">
                                    <h3>Milestone</h3>
                                    <div class="custom_btn">
                                        <div class="dispute_btn">
                                            <button type="button" class="btn btn_red" data-bs-toggle="modal" data-bs-target="#dispute_milestone">Dispute<span><i class="fa-solid fa-arrow-right"></i> </span></button>
                                        </div>
                                </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table myTable datatable">
                                        <tbody>

                                        <?php if(isset($milestones) && !empty($milestones)): ?>
                                            <?php $__currentLoopData = $milestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($loop->iteration ?? $key); ?></td>
                                                    <td>
                                                        <div class="milestone_icon">
                                                            <span><i class="fa-solid fa-flag"></i></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="milestone_card">
                                                            <h5> <?php echo e($milestone->title??'---'); ?> </h5>
                                                            <p> $<?php echo e($milestone->amount??'0'); ?> </p>
                                                            <span class="due_date"><i class="fa-regular fa-calendar"></i>Due <?php echo e($milestone->date??'---'); ?></span>
                                                        </div>
                                                    </td>

                                                            <?php if($milestone->requested != 'released'): ?>
                                                            <td>
                                                                        <div class="release_btn">
                                                                        <a href="#!" class="btn btn_yellow upload_media_modal" data-id="<?php echo e($milestone->id); ?>" data-key="<?php echo e($loop->iteration ?? $key); ?>" >Upload  <span><i class="fa-solid fa-upload"></i></span></a>
                                                                        </div>
                                                            </td>
                                                            <?php endif; ?>

                                                            <?php if(isset($milestone->jobMilestoneRequestedDoc) && $milestone->jobMilestoneRequestedDoc->count() > 0): ?>
                                                                <td>
                                                                    <div class="release_btn">
                                                                        <a href="#!" class="btn btn_pink view_requested_modal" data-id="<?php echo e($milestone->id); ?>">View<span><img src="<?php echo e(asset('website')); ?>/assets/images/bell-on.png"></span></a>
                                                                    </div>
                                                                </td>
                                                            <?php endif; ?>
                                                            <?php if(isset($milestone->jobMilestoneRequestedDoc) && $milestone->jobMilestoneRequestedDoc->count() > 0): ?>
                                                                <?php if($milestone->requested == 'pending'): ?>
                                                                    <td>
                                                                        <div class="release_btn">
                                                                            <a href="#!" class="btn btn_pink request_btn_doc" data-id="<?php echo e($milestone->id); ?>">Request Release<span><img src="<?php echo e(asset('website')); ?>/assets/images/bell-on.png"></span></a>
                                                                        </div>
                                                                    </td>
                                                                <?php elseif($milestone->requested == 'released'): ?>
                                                                    <td>
                                                                        <div class="milestone_paid">
                                                                            <a href="#!" class="btn btn_green"><i class="fa-solid fa-circle-check"></i>Released</a>
                                                                        </div>
                                                                    </td>
                                                                <?php else: ?>
                                                                    <td>
                                                                        <div class="release_btn">
                                                                            <a  class="btn btn_blue"> Requested <span><img src="<?php echo e(asset('website')); ?>/assets/images/bell-on.png"></span></a>
                                                                        </div>
                                                                    </td>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>

                                        </tbody>
                                    </table>
                                </div>

                            </div>
                            <div class="tab-pane fade" id="pills-dispute" role="tabpanel" aria-labelledby="pills-dispute -tab" tabindex="0">
                               <div class="row" id="dispute_milestone_chat_div">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="modal fade service_rating" id="service_rating" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="custom_img">
                        <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png">
                    </div>
                    <div class="seller_name">
                        <h3>Client :  <?php echo e($job->user->name); ?></h3>
                        <div class="seller_rating">
                            <span class="fa fa-star <?php if($job->user->ratingSum >=1): ?> checked <?php endif; ?>"></span>
                            <span class="fa fa-star <?php if($job->user->ratingSum >=2): ?> checked <?php endif; ?>"></span>
                            <span class="fa fa-star <?php if($job->user->ratingSum >=3): ?> checked <?php endif; ?>"></span>
                            <span class="fa fa-star <?php if($job->user->ratingSum >=4): ?> checked <?php endif; ?>"></span>
                            <span class="fa fa-star <?php if($job->user->ratingSum >=5): ?> checked <?php endif; ?>"></span>
                            <h6><?php echo e($job->user->ratingSum); ?> Stars</h6>
                        </div>
                    </div>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('store_review_rating')); ?>" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="row custom_row">
                            <div class="col-md-6">
                                <div class="txt_field">
                                    <label>Location:</label>
                                    <span><?php echo e($job->user->profile->country); ?> <?php echo e($job->user->profile->city); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="txt_field">
                                    <label>Budget:</label>
                                    <span>$<?php echo e($job->project_budget_min); ?> - $<?php echo e($job->project_budget_max); ?></span>
                                </div>
                            </div>
                               
                            <div class="col-md-6">
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Job Type:</label>
                                    <span><?php echo e($job->category->name); ?> - <?php echo e($job->subCategory->name); ?></span>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="custom_rating_star">
                                    <h6>Rate Me</h6>
                                    <div class="txt_field">
                                        <div class="rating_star wrapper">
                                            <input type="radio" name="rating" id="st1" value="5" />
                                            <label for="st1"></label>
                                            <input type="radio" name="rating" id="st2" value="4" />
                                            <label for="st2"></label>
                                            <input type="radio" name="rating" id="st3" value="3" />
                                            <label for="st3"></label>
                                            <input type="radio" name="rating" id="st4" value="2" />
                                            <label for="st4"></label>
                                            <input type="radio" name="rating" id="st5" value="1" />
                                            <label for="st5"></label>
                                            <input type="hidden" name="user_id" value="<?php echo e($job->user->id); ?>">
                                            <input type="hidden" name="job_id" value="<?php echo e($job->id); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Enter Review</label>
                                    <textarea class="form-control" rows="4" name="review" placeholder="Type Here"></textarea>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade upload_media" id="upload_media" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Upload Media</h3>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('store_milestone_request')); ?>" method="post" id="upload_media_milestone" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="milestone_id" id="milesstone_id" >
                        <div class="row">
                            <div class="col-md-12">
                                <div class="new_milestone">
                                    <span id="miles_stone_key">02</span>
                                    <h6>Milestone Details</h6>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <h5>Add Images/ Videos</h5>
                                <div id="custom_file_upload" class="custom_file_upload">
                                    <div class="append_type_wrapper">
                                        <div class="append_type_file">
                                            <input type="file" class="file-input" name="files[]" accept="image/*" />
                                            <a href="#!">
                                                <i class="fa-solid fa-image"></i>
                                            </a>
                                            <img src="" class="image_preview" alt="Image Preview" style="display: none;" />
                                            <button class="close-btn append_img_div_remove">
                                                <i class="fa-solid fa-close"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="new_custom_div"></div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black uploadBtn" id="uploadBtn">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade view_milestone_requested" id="view_milestone_requested" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">

    </div>

    <div class="modal fade dispute_milestone" id="dispute_milestone" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Dispute Milestone</h3>
                </div>
                <div class="modal-body">
                    <form id="dispute_milestoneForm">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Dispute Title</label>
                                    <input type="text" placeholder="Enter Dispute Title" id="dispute_title" class="form-control">
                                    <div class="invalid-feedback">Dispute title is required.</div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Disputed Milestone</label>
                                    <select class="form-control form-select" id="disputed_milestone_id" required>

                                        <?php if(isset($disputeMilestones) && !empty($disputeMilestones)): ?>
                                            <?php $__currentLoopData = $disputeMilestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($milestone->id); ?>"><?php echo e($milestone->title); ?> -  <?php echo e($milestone->amount); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            <input type="hidden" name="job_id" id="job_id" value="<?php echo e($job->id); ?>">
                            <input type="hidden" name="job_offer_id" id="job_offer_id" value="<?php echo e($job->jobOffersPaid->id); ?>">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Enter Message</label>
                                    <textarea placeholder="Enter Comments" id="enter_message" rows="5" class="form-control"></textarea>
                                    <div class="invalid-feedback">Message is required.</div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="button" id="dispute_milestone_button" class="btn btn_black disputeSubmitBtn">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
   <script src="https://js.pusher.com/4.1/pusher.min.js"></script>
<script src="<?php echo e(asset('js/chat.js')); ?>"></script>
<script src="https://malsup.github.io/jquery.form.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.25/dist/sweetalert2.min.js"></script>

     <script>


        $(document).ready(function() {
            $('#upload_media_milestone').on('submit', function() {
                let $btn = $('#uploadBtn');
                $btn.prop('disabled', true);
                $btn.html('Submitting..<span><i class="fa-solid fa-spinner fa-spin"></i></span>');
            });
        });

        $(document).ready(function () {
            $('.send_msg button').attr('disabled','disabled');
            $('.send_msg input[type="text"]').keyup(function(){
                if($(this).val().length > 0){
                    $('.send_msg button').removeAttr('disabled');
                }
                else if($(this).val().length == 0){
                    $('.send_msg button').attr('disabled','disabled');
                }
            });

            $(".send_msg input[type=file]").click(function () {
                $(".send_msg input[type=file]").css("width", "150px");
            });

            $(".users_chats").hide();
            $('.accept_reject_msg button').click(function() {
                var btnValue = $(this).val();
                if (btnValue === "accepted") {
                    console.log(btnValue);
                    $(this).closest('.chats_section').find('.users_chats').show();
                    $(this).closest('.chats_section').find(".accept_reject_msg").hide();
                }
                else if (btnValue === "rejected") {
                    $(this).closest('.chats_section').find(".accept_reject_msg .new_message p").html("Message Has Been Rejected");
                    $(this).closest('.chats_section').find(".accept_reject_msg .custom_btn").hide();
                    console.log(btnValue);
                };
            });
            $(".searchbar_input .custom_search_box").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $(".all_users_chats.myTable *").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
            $(".send_msg button").click(function () {
                $(".custom_chats .chats_section .chat_messages").animate({
                    scrollTop: $('.custom_chats .chats_section .chat_messages').get(0).scrollHeight
                }, 2000);
            });
        });

    </script>
<script>
    $(document).ready(function (){
        // upload Media file

        $('.append_type_file .append_img_div_remove').hide();
        $(document).on("input",'.append_type_file .file-input',function() {

            if($(this).val() != '')
            {
                $(this).closest('.custom_file_upload').append(`
                    <div class="append_type_wrapper">
                        <div class="append_type_file">
                            <input type="file" class="file-input" name="files[]" accept="image/*" />
                            <a href="#!">
                                <i class="fa-solid fa-image"></i>
                            </a>
                            <img src="" class="image_preview" alt="Image Preview" style="display: none;" />
                            <button class="close-btn append_img_div_remove">
                                <i class="fa-solid fa-close"></i>
                            </button>
                        </div>
                    </div>
                `);
                $(this).closest('.append_type_file').find('.append_img_div_remove').show();
            }

            $('.append_type_file .file-input').each(function () {
                if ($(this).val() === '' && $(this).closest('.append_type_file').find('.image_preview').attr("src").trim() === "") {
                    $(this).closest('.append_type_file').find('.append_img_div_remove').hide();
                }
            });

            $(document).on("click",".append_type_wrapper .append_img_div_remove",function () {
                $(this).closest(".append_type_wrapper").remove();
            });
        });

        $(document).on("change", ".append_type_wrapper .append_type_file input[type='file']", function(event) {
            const fileDiv = $(this).closest('.append_type_file')[0];  // Get the closest div for file input
            const fileInput = fileDiv.querySelector('.file-input');  // Get the file input
            const img = fileDiv.querySelector('img');  // Get the image element
            const plusIcon = fileDiv.querySelector('a>i');  // Get the plus icon

            // Handle file input change
            const file = fileInput.files[0];  // Get the selected file
            if (file) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    img.src = e.target.result;  // Set the image source to the selected file
                    img.style.display = 'block';  // Show the image
                    plusIcon.style.display = 'none';  // Hide the plus icon
                };

                reader.readAsDataURL(file);  // Read the file as a Data URL
            }
        });
    })
    $(document).on('click', '.view_requested_modal', function() {
        var view_requested_id = $(this).attr('data-id');
        $.ajax({
            url: '<?php echo e(url('view_milestone_requested_doc')); ?>/' + view_requested_id, //Replace with your desired URL
            success: function(response) {
                $('#view_milestone_requested').html(response);
                $('#view_milestone_requested').modal('show');
            },
        });
    });
$(document).on('click', '#dispute_milestone_button', function() {
            let dispute_title = $('#dispute_title').val();
            let  disputed_milestone_id = $('#disputed_milestone_id').val();
            let job_id = $('#job_id').val();
            let job_offer_id = $('#job_offer_id').val();
            let enter_message = $('#enter_message').val();
            let outcome        = 'continue';

            const $button = $(this);
            const $title = $('#dispute_title');
            const $message = $('#enter_message');
            let isValid = true;

            // Reset previous validation states
            $('.form-control').removeClass('is-invalid');

            if ($title.val().trim().length < 3) {
                $title.addClass('is-invalid');
                isValid = false;
            }

            if ($message.val().trim().length < 3) {
                $message.addClass('is-invalid');
                isValid = false;
            }

            if (!isValid) return;
            $button.prop('disabled', true).html('Processing... <span><i class="fa-solid fa-spinner fa-spin"></i></span>');
            if (!dispute_title || !disputed_milestone_id || !job_id || !job_offer_id || !enter_message) {

                Swal.fire({
                    title: "Error",
                    text: "All fields are required!",
                    icon: "error",
                });
                return;

            }
    $.ajax({
        url: '<?php echo e(url('dispute_milestone')); ?>',
        method: 'GET', // or POST depending on your requirement
        data: {
            dispute_title: dispute_title,
            disputed_milestone_id: disputed_milestone_id,
            job_id: job_id,
            job_offer_id: job_offer_id,
            enter_message: enter_message,
            outcome: outcome
                    // outcome: "continue",
        },
        success: function(response) {
            Swal.fire({
                title: response.title,
                text: response.message,
                icon: response.type,
            }).then(() => {
                        // window.location.reload();
                        $('#dispute_milestone').modal('hide');
                        $('#dispute_milestoneForm')[0].reset();
                        $('#dispute-tab-wrapper').removeClass('d-none');
                        const disputeTab = new bootstrap.Tab(document.querySelector('#pills-dispute-tab'));
                        disputeTab.show();
            });
        },
                // error: function() {
                //     $btn.prop('disabled', false).html('Submit <span><i class="fa-solid fa-arrow-right"></i></span>');
                //     Swal.fire('Error', 'Something went wrong. Please try again.', 'error');
                // },
        complete: function() {

                    $button.prop('disabled', false).html('Submit <span><i class="fa-solid fa-arrow-right"></i></span>');
        }
    });
});

    $(document).on('click', '.request_btn_doc', function() {
        var view_requested_id = $(this).attr('data-id');
        var status = 'requested';
        Swal.fire({
            title: 'Are you sure?',
            text: 'Do you want to submit this request?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, submit it!',
            cancelButtonText: 'No, cancel!',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Processing...',
                        text: 'Please wait while we submit your request.',
                        icon: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                $.ajax({
                    url: '<?php echo e(url('milestone_updated_release_status')); ?>/' + view_requested_id + '/' + status,
                    method: 'GET', // or POST depending on your requirement
                    success: function(response) {
                        Swal.fire({
                            title: response.title,
                            text: response.message,
                            icon: response.type,
                        }).then(() => {
                            window.location.reload();
                        });
                        },
                });
            } else {
                // If user cancels, show a message
                Swal.fire(
                    'Cancelled',
                    'Your request was not submitted.',
                    'info'
                );
            }
        });
    });


    $(document).on('click','.upload_media_modal',function() {
        var dataKey = $(this).attr('data-key');
        var milesstone_id = $(this).attr('data-id');
        $('#miles_stone_key').text(dataKey);
        $('#milesstone_id').val(milesstone_id);
        $('#upload_media').modal('show');
    });
        dispute_milestone_chat()
    function dispute_milestone_chat(){

                job_id = $('#job_id').val()
                $.ajax({
                    url: '<?php echo e(url('dispute_milestone_chat')); ?>',
                    method: 'GET', // or POST depending on your requirement
                    data:{job_id:job_id},
                    success: function(response) {
                       $('#dispute_milestone_chat_div').html(response)
                    },
                });
    }

    $(document).on('click','#resolve_dispute',function(){
                id = $(this).attr('data-id')
                $.ajax({
                    url: '<?php echo e(url('resolve_dispute')); ?>',
                method: 'GET',
                    data:{id:id},
                    success: function(response) {
                        Swal.fire({
                            title: response.title,
                            text: response.message,
                            icon: response.type,
                        }).then(() => {
                            window.location.reload();
                        });
                    },
                });
    });

</script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/serviceProvider/service_completed_projects.blade.php ENDPATH**/ ?>