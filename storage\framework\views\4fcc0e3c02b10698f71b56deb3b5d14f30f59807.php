<?php $__env->startPush("css"); ?>
<link rel="stylesheet" href="<?php echo e(asset('plugins/components/dropify/dist/css/dropify.min.css')); ?>">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">

<style>
    .error{
        color:red !important;
    }
</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="post_project_section edit_profile">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <form action="<?php echo e(url('profile_update')); ?>" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="post_project_content">
                            <div class="edit_btn">
                                <button type="button" id="" class="btn btn_black change_pass">Change Password<i class="fa-solid fa-eye-slash"></i></button>
                                <button type="button" id="" class="btn btn_black upload_docx">Upload Document<i class="fa-solid fa-file"></i></button>
                            </div>
                            <div class="edit_profile_logo">
                                <div class="profile_picture">
                                    <input type="file" name="pic_file"  class="dropify myinput" data-default-file="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>"/>
                                </div>
                            </div>
                            <div class="single_field">
                                <h5 class="mb_20">Description</h5>
                                <div class="txt_field">
                                    <textarea class="form-control" rows="4" name="bio" placeholder="Enter Your Description"><?php echo $user->profile->bio  ?? '---'; ?></textarea>
                                </div>
                            </div>
                            <div class="personal_information">
                                <div class="row custom_row">
                                    <div class="col-md-12">
                                        <h5>Personal Information</h5>
                                    </div>
                                    <div class="col-md-4 col-sm-4">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="salutation" class="form-label">Salutation</label>
                                                <select name="salutation" id="salutation" class="salutation_select form-select">
                                                    <?php if(isset($salutations) && $salutations->count()>0): ?>
                                                        <?php $__currentLoopData = $salutations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $salutation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($salutation->id); ?>" <?php echo e($user->salutation == $salutation->id ? 'selected' : ''); ?>><?php echo e($salutation->name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label>First Name</label>
                                                <input type="text" class="form-control"  id="" name="first_name" value="<?php echo e($user->first_name ??''); ?>" placeholder="Enter First Name">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label>Last Name</label>
                                                <input type="text" class="form-control" name="last_name" value="<?php echo e($user->last_name ??''); ?>" placeholder="Enter Last Name">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <h5>Contact Information</h5>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label>Email</label>
                                                <input type="email" class="form-control" placeholder="Enter Email" name="email" value="<?php echo e($user->email??''); ?>" required readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Phone</label>
                                                <input id="" type="tel" class="form-control phone" placeholder="Enter Phone" name="phone" value="<?php echo e($user->profile->phone??''); ?>" required autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Address</label>
                                                <label for="" class="form-label">Address</label>
                                                <input id="contractor_address" type="text" class="form-control" placeholder="Enter Address" name="address" value="<?php echo e($user->profile->address??''); ?>" required autofocus>
                                                <input id="contractor_longitude" type="hidden" class="form-control"  name="longitude" >
                                                <input id="contractor_latitude" type="hidden" class="form-control"  name="latitude"  >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="" class="form-label">City</label>
                                                <input id="" type="text" class="form-control" placeholder="Enter City" name="city" value="<?php echo e($user->profile->city??''); ?>" required autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="" class="form-label">State</label>
                                                <input id="" type="text" class="form-control" placeholder="Enter State" name="state" value="<?php echo e($user->profile->state??''); ?>" required autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Zip Code</label>
                                                <input id="" type="text" class="form-control" placeholder="Enter Zip Code" name="zip_code" value="<?php echo e($user->profile->postal??''); ?>" required autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Contractor Country</label>
                                                <input id="" type="text" class="form-control" placeholder="Enter Contractor Country" name="country" value="<?php echo e($user->profile->country??''); ?>" required autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Apt,Suite,Floor</label>
                                                <input id="" type="tel" class="form-control" placeholder="Enter Apt,Suite,Floor" name="suite_or_floor" value="<?php echo e($user->profile->suite_or_floor??''); ?>" autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <h5>Company Information</h5>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label>Company Name</label>
                                                <input id="company_name" type="text" class="form-control" placeholder="Enter Company Name..." name="company_name" value="<?php echo e($user->profile->company_name ?? '--'); ?>" required autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label class="form-label">Date Company Was Established</label>
                                                <div id="" class="date datepicker" data-date-format="mm-dd-yyyy">
                                                    <input class="form-control" type="text" placeholder="DD/MM/YYYY" readonly name="date_company_was_established" value="<?php echo e($user->profile->date_company_was_established??''); ?>"/>
                                                    <span class="input-group-addon"><i class="fa-solid fa-calendar"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 col-sm-12">
                                        <div class="custom_category_flex">
                                            <div class="single_field">
                                                <div class="txt_field">
                                                    <label class="form-label">Categories</label>
                                                    <div class="seller_categories">
                                                        <?php $__currentLoopData = $user->userCategory->where('status','!=','rejected'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="category_type_request">
                                                                <h4><?php echo e($category->category->name ?? '---'); ?></h4>

                                                                <span class="accept_category <?php if($category->status == 'approved'): ?>badge-success <?php else: ?> badge-secondary <?php endif; ?>"> <?php echo e($category->status ?? '---'); ?> </span>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="add_request_btn">
                                                <button type="button" class="btn btn_blue category_button"  data-bs-toggle="modal" data-bs-target="#categoryModal">Add Request</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label for="" class="form-label">Please Select States Where You Are Licensed</label>
                                                <div class="custom_multi_select multi_licence_select">
                                                    <select multiple class="form-control custom_multiselect" name="states_license">
                                                        <option></option>
                                                        <option value="1" data-select-attribute="CA">California(CA)</option>
                                                        <option value="2" data-select-attribute="Co">Colorado(CO)</option>
                                                        <option value="3" data-select-attribute="IN">Indiana(IN)</option>
                                                        <option value="4" data-select-attribute="NY">New York(NY)</option>
                                                        <option value="5" data-select-attribute="NJ">New Jersey(NJ)</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <label for="" class="form-label">States License Number</label>
                                        <div class="append_state_license"></div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label>Do they hold an insurance policy?</label>
                                                <div class="custom_radio_wrapper">
                                                    <div class="custom_radio custom_position">
                                                        <input class="form-check-input" type="radio" name="insurance_policy" value="yes" <?php if($user->profile->insurance_policy == 'yes'): ?> checked <?php endif; ?> id="flexCheckChecked1">
                                                        <label for="flexCheckChecked1"><h6>Yes</h6></label>
                                                    </div>
                                                    <div class="custom_radio">
                                                        <input class="form-check-input" type="radio" name="insurance_policy" value="no" <?php if($user->profile->insurance_policy == 'no'): ?> checked <?php endif; ?> id="flexCheckChecked2">
                                                        <label for="flexCheckChecked2"><h6>No</h6></label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label>Insurance Name</label>
                                                <input id="" type="text" class="form-control" placeholder="Enter Insurer Name..." name="insurance_name" value="<?php echo e($user->profile->insurance_name ?? '--'); ?>" required autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6 col-6">
                                        <div class="single_field">
                                            <div class="txt_field">
                                                <label>Policy Number</label>
                                                <input id="" type="number" class="form-control" placeholder="Enter Policy Number..." name="policy_number" value="<?php echo e($user->profile->policy_number ?? '--'); ?>" required autofocus>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="post_btn">
                                            <button type="button" class="btn btn_transparent " >Cancel</button>
                                            <button type="Submit" class="btn btn_black btn_has_icon" >Save Changes <div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-12">
                    <div class="change_password_wrapper custom_projects_tabs">
                        <form action="<?php echo e(route('change_user_password')); ?>" method="post" enctype="multipart/form-data" id="account_detail">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="currentPassword" class="form-label input_labels">Current Password</label>
                                        <input type="password" name="password" class="form-control input_fields borderRadius" id="currentpassword" aria-describedby="emailHelp">
                                        <?php if($errors->has('password')): ?>
                                            <span class="invalid-feedback">
                                                <strong><?php echo e($errors->first('password')); ?></strong>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="txt_field">
                                        <label for="newPassword" class="form-label input_labels">New Password</label>
                                        <div class="input_group_box">
                                            <input type="password" name="new_password" class="form-control input_fields borderRadius" id="newpassword" aria-describedby="emailHelp">
                                            <div class="input_icon">
                                                <a href="javascript:void(0);" class="password_btn2"><i class="fa fa-eye-slash" aria-hidden="true"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="txt_field">
                                        <label for="confirmPassword" class="form-label input_labels">Confirm Password</label>
                                        <div class="input_group_box">
                                            <input type="password" name="password_confirmation" class="form-control input_fields borderRadius" id="password_confirmation" aria-describedby="emailHelp">
                                            <div class="input_icon">
                                                <a href="javascript:void(0);" class="password_btn3"><i class="fa fa-eye-slash" aria-hidden="true"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6"></div>
                            </div>
                            <div class="change_password_btn">
                                <button type="submit" class="btn btn_black submit_btn">Change Password</button>
                                <button type="button" class="btn btn_black back_prev">Back</button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="custom_projects_tabs upload_documents_wrapper">
                        <form action="<?php echo e(url('upload-license-document')); ?>" method="post" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <div class="row custom_row">
                                <?php $__currentLoopData = $user->getUserAttachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $statusClass = $file->status == '1'
                                            ? 'badge-success'
                                            : ($file->status === '0'
                                                ? 'badge-danger'
                                                : 'badge-primary'); // Default class for null

                                        $statusText = $file->status == '1'
                                            ? 'Approved'
                                            : ($file->status == '0'
                                                ? 'Rejected'
                                                : 'Pending'); // Explicitly handle null
                                    ?>
                                    <?php if($file->section == 'state_license'): ?>
                                        <div class="col-md-12">
                                            <div class="default_doc_preview">
                                                <h3>Change State License Document <span class="<?php echo e($statusClass); ?>"><?php echo e($statusText); ?></span></h3>
                                                <div class="state_license"><!--default_doc_preview-->
                                                    <?php if($file->status == 0): ?>
                                                        <div id="" class="custom_file_upload">
                                                            <button type="button" class="append_type_file append_btn">
                                                                <i class="fa-solid fa-plus"></i>Add
                                                            </button>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="license_document">
                                                        <a href="<?php echo e(asset('website')); ?>//<?php echo e($file->name??''); ?>">
                                                            <div class="doc_image">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/docx_img.png">
                                                            </div>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="col-md-12">
                                            <div class="default_doc_preview">
                                                <h3>Change Insurance Policy <span class="<?php echo e($statusClass); ?>"><?php echo e($statusText); ?></span></h3>
                                                <div class="insurance_policy"><!--default_doc_preview-->
                                                    <?php if($file->status == 0): ?>
                                                        <div id="" class="custom_file_upload">
                                                            <button type="button" class="append_type_file append_btn">
                                                                <i class="fa-solid fa-plus"></i>Add
                                                            </button>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="license_document">
                                                        <a href="<?php echo e(asset('website')); ?>/<?php echo e($file->name??''); ?>">
                                                            <div class="doc_image">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/docx_img.png">
                                                            </div>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <div class="submit_doc">
                                <button type="submit" class="btn btn_black submit_doc_btn">Submit Document</button>
                                <button type="button" class="btn btn_black back_prev">Back</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal fade service_rating category_modal" id="categoryModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Select your Categories</h2>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('user_category_request')); ?>" method="post">
                        <?php echo csrf_field(); ?>
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="single_field">
                                    <div class="txt_field">
                                        <label class="form-label">Categories</label>
                                        <div class="custom_multi_select">
                                            <select class="form-select" name="category_id[]">
                                                <?php if(isset($jobCategories) && $jobCategories->count()>0): ?>
                                                    <?php $__currentLoopData = $jobCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jobCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if(isset($user->userCategory) && in_array($jobCategory->id, $user->userCategory->where('status','approved')->pluck('category_id')->toArray())): ?>
                                                            <?php continue; ?>
                                                        <?php endif; ?>
                                                        <option value="<?php echo e($jobCategory->id); ?>" ><?php echo e($jobCategory->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php endif; ?>
                                            </select>
                                            <div class="select_values"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script src="<?php echo e(asset('js/jasny-bootstrap.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/components/dropify/dist/js/dropify.min.js')); ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

<script>
    $(document).ready(function() {
        $('input[name="email"]').prop('readonly', true);
        $('.phone').on('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
        $(function() {
            $('.dropify').dropify();
            $(".dropify-wrapper .dropify-clear").html("");
        });

        $(document).on("click", '.append_btn', function() {
            let parentClass = $(this).closest('div').parent().attr('class') || '';
            let sectionName = parentClass.split(' ')[0]; // Take the first class name
            if ($(this).closest('.custom_file_upload').find('.append_type_wrapper').length === 0) {
                $(this).closest('.custom_file_upload').append(`
            <div class="append_type_wrapper">
                <div class="append_type_file">
                    <input type="file" class="file-input" name="file[input][]" required/>
                    <input type="hidden" name="file[section][]" value="${sectionName}"/>
                    <a class="image_icon" href="#!"><i class="fa-solid fa-image"></i></a>
                    <button class="close-btn append_img_div_remove"><i class="fa-solid fa-close"></i></button>
                </div>
            </div>`
                );
            }
        });

        // Handle click on close button to remove the uploaded file input div
        $(document).on("click", ".append_type_wrapper .append_img_div_remove", function() {
            $(this).closest(".append_type_wrapper").remove();
        });

        // Handle file input change to show image preview
        $(document).on("change", ".append_type_wrapper .append_type_file input[type='file']", function(event) {
            const file = $(this)[0].files[0];  // Get the selected file
            const fileType = file.type;
            if (file) {
                const reader = new FileReader();
                // Check file type
                if (fileType.startsWith('image/')) {
                    // Handle image file
                    const imageURL = URL.createObjectURL(file);
                    const $parentDiv = $(this).closest(".append_type_file");
                    // Remove existing image preview before appending a new one
                    $parentDiv.find(".image_preview").remove();
                    // Append image preview
                    $parentDiv.append('<div class="image_structure"><img src='+ imageURL +' class="image_preview" /></div>');
                }else if (fileType == 'application/pdf') {
                    const pdfURL = URL.createObjectURL(file);
                    const $parentDiv = $(this).closest(".append_type_file");
                    // Remove existing image preview before appending a new one
                    $parentDiv.find(".image_preview").remove();
                    $parentDiv.append('<div class="image_structure">' +
                        '<div class="pdf_img"><img src="<?php echo e(asset('website')); ?>/assets/images/pdf_img.png" alt="PDF" class="pdf_control"></div>' +
                        '<a href='+ pdfURL +' target="_blank" class="preview_pdf">View PDF</a></div>');
                } else if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                    // Handle DOCX file
                    const docxURL = URL.createObjectURL(file);
                    const $parentDiv = $(this).closest(".append_type_file");
                    // Remove existing image preview before appending a new one
                    $parentDiv.find(".image_preview").remove();
                    $parentDiv.append('<div class="image_structure">' +
                        '<div class="docx_img"><img src="<?php echo e(asset('website')); ?>/assets/images/docx_img.png" alt="DOCX" class="docx_control"></div>' +
                        '<a href='+ docxURL +' target="_blank" class="preview_docx">View DOCX</a></div>');
                }
            }
        });

//         Append State License Number Based on Select Option
        $('.custom_multi_select.multi_licence_select').on('change', function() {
            $('.append_state_license').empty();

            $(this).find('option:selected').each(function() {
                var stateType = $(this).data('select-attribute');
                $('.append_state_license').append('<div class="custom_add_license">' +
                    '<div class="state_key_initial"><span class="data_attribute_key">' + stateType + '</span></div>' +
                    '<div class="txt_field"><input id="" type="number" class="form-control" placeholder="Enter States License Number..." name="" value="" required autofocus> </div>'+
                    '</div>');
            });
        });

        $('.custom_multiselect').select2({
            placeholder: "Select An Option",
            allowClear: true
        });

        // Modal Select 2
//below code is commented because it is not used
//         $('.category_modal .custom_multi_select select').on('change', function() {
//             var selectedOption = $(this).find('option:selected');
//             var stateType = selectedOption.text();
//             var stateValue = selectedOption.val();
//
//             if ($('.select_values .custom_select_values[data-value="' + stateValue + '"]').length === 0) {
//                 $('.select_values').append(`<div class="custom_select_values" data-value="${stateValue}">
//                     <span class="select_attribute_key">${stateType}<i class="fa-solid fa-times remove_item" data-value="${stateValue}"></i></span>
//                     </div>`);
//             }
//         });



        $(document).ready(function() {
            // Handle category selection
            $('.category_modal .custom_multi_select select').on('change', function() {
                var selectedOption = $(this).find('option:selected');
                var stateType = selectedOption.text();
                var stateValue = selectedOption.val();

                // Only add if not already selected
                if ($('.select_values .custom_select_values[data-value="' + stateValue + '"]').length === 0) {
                    $('.select_values').append(`
                <div class="custom_select_values" data-value="${stateValue}">
                    <span class="select_attribute_key">${stateType}
                        <i class="fa-solid fa-times remove_item" data-value="${stateValue}"></i>
                    </span>
                    <input type="hidden" name="category_id[]" value="${stateValue}">
                </div>
            `);
                }

                // Reset the select to its default state
                $(this).val('');
            });

            // Handle removal of selected categories
            $(document).on('click', '.remove_item', function() {
                var valueToRemove = $(this).data('value');
                $('.select_values .custom_select_values[data-value="' + valueToRemove + '"]').remove();
            });

            // Optional: Form validation before submission
            $('#categoryForm').on('submit', function(e) {
                if ($('.select_values .custom_select_values').length === 0) {
                    e.preventDefault();
                    alert('Please select at least one category');
                    return false;
                }
                return true;
            });
        });




        $(document).on('click', '.remove_item', function() {
            var valueToRemove = $(this).data("value");

            $('.select_values .custom_select_values[data-value="' + valueToRemove + '"]').remove();
        });


        // Initialize Datepicker
        $(".datepicker").datepicker({
            autoclose: true,
            todayHighlight: true,
            todayBtn: "linked",
        }).datepicker('update', new Date());
    })

</script>
<script>
    jQuery('#account_detail').validate({
        rules: {
            password:{
                required: true,
                remote: {
                    type: 'post',
                    url: "<?php echo e(route('check_password')); ?>",
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>",'password': function () { return $('#currentpassword').val(); }
                    },
                    dataType: 'json'
                }
            },
            new_password: {
                required: true,
                minlength: 8,
            },
            password_confirmation: {
                required: true,
                minlength: 8,
                equalTo: "#newpassword",
            },
        },
        messages: {
            password: "Password must be at least 8 characters.",
            password_confirmation: "Confirm password is incorrect.",
            password: {
                required: "Please enter your current password.",
                remote: "Current Password is Invalid"
            }
        },
        submitHandler: function (form) {
            return true;
        }
    });


    $(function(){
        $('.password_btn2').click(function(){
            if($('.password_btn2 i').hasClass('fa-eye-slash')){
                $('.password_btn2 i').removeClass('fa-eye-slash');
                $('.password_btn2 i').addClass('fa-eye');
                $('#newpassword').attr('type','text');
            }else{
                $('.password_btn2 i').removeClass('fa-eye');
                $('.password_btn2 i').addClass('fa-eye-slash');
                $('#newpassword').attr('type','password');
            }
        });
    });
    $(function(){
        $('.password_btn3').click(function(){
            if($('.password_btn3 i').hasClass('fa-eye-slash')){
                $('.password_btn3 i').removeClass('fa-eye-slash');
                $('.password_btn3 i').addClass('fa-eye');
                $('#password_confirmation').attr('type','text');
            }else{
                $('.password_btn3 i').removeClass('fa-eye');
                $('.password_btn3 i').addClass('fa-eye-slash');
                $('#password_confirmation').attr('type','password');
            }
        });
    });
</script>
<script>
    $(document).ready(function () {
        $(".edit_profile .change_password_wrapper").hide();
        $(".edit_profile .upload_documents_wrapper").hide();
        $(".edit_profile .post_project_content .edit_btn .change_pass").on("click", function () {
            $(".edit_profile .post_project_content ").hide();
            $(".edit_profile .change_password_wrapper").show();
        });
        $(".edit_profile .post_project_content .edit_btn .upload_docx").on("click", function () {
            $(".edit_profile .post_project_content ").hide();
            $(".edit_profile .upload_documents_wrapper").show();
        });
        $(".edit_profile .back_prev").on("click", function () {
            $(".edit_profile .change_password_wrapper ").hide();
            $(".edit_profile .upload_documents_wrapper").hide();
            $(".edit_profile .post_project_content").show();
        });
    })
</script>

    <script>
        var contractorInput = document.getElementById("contractor_address");
        if(contractorInput) {
            initGoogleMaps()
        }

        function initGoogleMaps() {

            if (contractorInput) {
                // google.maps.event.addDomListener(window, 'load', function () {
                var places = new google.maps.places.Autocomplete(document.getElementById('contractor_address'));
                google.maps.event.addListener(places, 'place_changed', function () {
                    var place = places.getPlace();
                    // Extract latitude and longitude
                    var latitude = place.geometry.location.lat();
                    var longitude = place.geometry.location.lng();
                    // Assign latitude and longitude to hidden input fields
                    document.getElementById('contractor_latitude').value = latitude;
                    document.getElementById('contractor_longitude').value = longitude;
                    // Extract address components
                    var addressComponents = place.address_components;
                    let city = '';
                    let state = '';
                    let postcode = '';
                    let country = '';
                    addressComponents.forEach((component) => {
                        const types = component.types;
                        if (types.includes('locality')) {
                            city = component.long_name; // City
                        }

                        if (types.includes('administrative_area_level_1')) {
                            state = component.long_name; // State
                        }

                        if (types.includes('postal_code')) {
                            postcode = component.long_name; // Zip Code
                        }

                        if (types.includes('country')) {
                            country = component.long_name; // Country
                        }
                    });

                    // Assign extracted values to their respective fields
                    document.getElementById('contractor_city').value = city || '';
                    document.getElementById('contractor_state').value = state || '';
                    document.getElementById('contractor_zip_code').value = postcode || '';
                    document.getElementById('contractor_country').value = country || '';

                });
            }
        }
    </script>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/serviceProvider/seller-profile-edit.blade.php ENDPATH**/ ?>