<?php $__env->startPush("css"); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
<style>
    .error{
        width:120%;
        font-size:12px !important;
        color:red !important;
    }
    .select2-container.select2-container--open {z-index: 99999;}

</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="details_review_page service_profile">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="personal_information custom_section">
                        <div class="custom_profile_info">
                            <div class="custom_profile_img">
                                <div class="info_profile">
                                    <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($user->profile->pic??''); ?>" alt="">
                                </div>
                                <a href="#!" class="share_icon"><i class="fa-solid fa-share"></i></a>
                            </div>
                            <h4><?php echo e($user->name??''); ?></h4>
                            <div class="rating_star">
                                <div class="seller_rating">
                                    <span class="fa fa-star <?php if($user->ratingSum >= 1): ?> checked <?php endif; ?>"></span>
                                    <span class="fa fa-star <?php if($user->ratingSum >= 2): ?> checked <?php endif; ?>"></span>
                                    <span class="fa fa-star <?php if($user->ratingSum >= 3): ?> checked <?php endif; ?>"></span>
                                    <span class="fa fa-star <?php if($user->ratingSum >= 4): ?> checked <?php endif; ?>"></span>
                                    <span class="fa fa-star <?php if($user->ratingSum >= 5): ?> checked <?php endif; ?>"></span>
                                    <h6><?php echo e($user->ratingSum); ?> Stars</h6>
                                </div>
                            </div>
                        </div>
                        <?php if(isset($role) && $role == 'seller'): ?>
                        <div class="social_icons">
                            <a href="#!" class="btn btn_blue btn_has_icon">Hire Me <div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></a>
                            <a href="<?php echo e(url('service_provider_chat')); ?>" class="btn btn_black email_btn"><i class="fa-solid fa-envelope"></i></a>
                        </div>
                        <div class="edit_profile_btn">
                            <a href="<?php echo e(url('seller-profile-edit')); ?>" class="btn btn_black btn_has_icon">Edit Profile<div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></a>
                        </div>
                        <?php endif; ?>

                        <div class="contact_info">
                            <h5 >Company Information</h5>
                            <div class="description">
                                <h5>Description</h5>
                                <p><?php echo e($user->profile->bio??''); ?></p>
                                <a href="#!" data-bs-toggle="modal" data-bs-target="#service_description_content" class="description_view_more">View More<i class="fa-solid fa-chevron-down"></i></a>
                            </div>
                            <div class="dflex percent_txt">
                                <div class="social_icons"><h6 class="fw_bold">Years In Opearations</h6></div>
                                <div><h6>
                                        <?php echo e($user->created_at->diffInYears(now()) > 0? $user->created_at->diffInYears(now()) . ' years': 1 . ' Year'); ?>

                                    </h6></div>
                            </div>
                            <div class="dflex percent_txt">
                                <div class="social_icons"><h6 class="fw_bold">Projects Completed</h6></div>
                                <div><h6><?php echo e($user->getUserJobs->where('status','completed')->count()??'0'); ?></h6></div>
                            </div>
                            <div class="dflex percent_txt">
                                <div class="social_icons"><h6 class="fw_bold">Repeat Hire Rate</h6></div>
                                <div><h6>89%</h6></div>
                            </div>
                        </div>
                    </div>
                    <div class="custom_state_preferences">
                        <div class="custom_card service_geographical_cards portfolio">
                            <h5>Geographical Preferences</h5>
                        <?php if(isset($role) && $role == 'seller'): ?>
                            <div class="add_geographical_location">
                                <a href="#!" class="btn btn_blue" data-bs-toggle="modal" data-bs-target="#addLocation">Add Preferences</a>
                            </div>
                        <?php endif; ?>
                            <div class="suggest_links">
                                <?php $__empty_1 = true; $__currentLoopData = $user->getUserGeographicalPreference; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $geographical): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>

                                <div class="suggested_location ">
                                    <span class="card_links suggested_location_click" geograph-id="<?php echo e($geographical->id); ?>"><?php echo e($geographical->address ?? ''); ?> / <?php echo e($geographical->city ?? ''); ?>

                                    </span>
                                        <?php if(isset($role) && $role == 'seller'): ?>
                                            <button type="button" class="btn btn_orange delete_btn_loc" location-id="<?php echo e($geographical->id); ?>"><i class="fa-solid fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <span>No Data Available</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="custom_card service_geographical_cards portfolio">
                            <h5>Restricted States</h5>
                        <?php if(isset($role) && $role == 'seller'): ?>
                            <div class="add_geographical_location">
                                <a href="#!" class="btn btn_blue" data-bs-toggle="modal" data-bs-target="#addRestrictedStates">Add Restricted States</a>
                            </div>
                        <?php endif; ?>
                            <div class="suggest_links">
                                <?php $__empty_1 = true; $__currentLoopData = $user->getUserRestrictedState; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $restrictedState): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <div class="suggested_location">
                                        <span class="card_links"><?php echo e($restrictedState->state ?? '---'); ?></span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <div class="suggested_location">
                                        <span class="card_links">No States</span>
                                    </div>
                                <?php endif; ?>

                            </div>
                        </div>
                    </div>
                    <div class="custom_license_doc">
                        <?php $__empty_1 = true; $__currentLoopData = $user->getUserAttachments/*->where('status',1)*/; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <?php
                                $statusClass = $attachment->status == 1 ? 'badge-success' : ($attachment->status == 0 ? 'badge-danger' : 'badge-primary');
                                $statusText = $attachment->status == 1 ? 'Confirmed' : ($attachment->status == 0 ? 'Rejected' : 'Pending');
                            ?>
                            <?php if($attachment->section == 'state_license'): ?>
                                <div class="state_licenses custom_section">
                                    <h5 class="title">State License Status <span class="<?php echo e($statusClass); ?>"><?php echo e($statusText); ?></span></h5>
                                    <div class="license_document">

                                            <div class="doc_image">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/docx_img.png">

                                            </div>

                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="state_licenses custom_section">
                                    <h5 class="title">Insurance Status <span class="<?php echo e($statusClass); ?>"><?php echo e($statusText); ?></span></h5>
                                    <div class="license_document">

                                            <div class="doc_image">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/docx_img.png">
                                            </div>

                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="state_licenses custom_section">
                                <h5 class="title">No Approved Document</h5>
                                <div class="license_document">
                                    <a href="#!">
                                        <div class="doc_image">
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/docx_img.png">
                                        </div>
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="portfolio custom_section mb_20">
                        <div class="portofolio_button">
                            <h5>Portfolio</h5>
                            <?php if(isset($role) && $role == 'seller'): ?>
                                <button type="button" class="btn btn_black btn_has_icon" data-bs-toggle="modal" data-bs-target="#editPortfolio" >
                                    Add Portfolio
                                    <div class="btn_icon">
                                        <i class="fa-solid fa-arrow-right"></i>
                                    </div>
                                </button>
                            <?php endif; ?>
                        </div>
                        <div class="custom_portfolio_images">
                            <?php $__currentLoopData = $portfolio; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sellerPortfolio): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="custom_portfolio_wrapper">
                                    
                                    <a class="portfolio_cards <?php if(isset($role) && $role == 'seller'): ?> sellerPortfolio <?php else: ?> buyer_want_to_see_portfolio <?php endif; ?>" seller-portfolio-id="<?php echo e($sellerPortfolio->id); ?>">
                                        <div class="portofolio_imgs">
                                            <img src="<?php echo e(asset('website')); ?>/<?php echo e(optional($sellerPortfolio->portfolioAttachments->first())->file ?? ''); ?>" alt="">
                                        </div>
                                        <h6><?php echo e($sellerPortfolio->title??''); ?></h6>
                                        <button type=button" class="edit_cards"><i class="fa-solid fa-pen-to-square"></i></button>
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="projects_expertise">
                            <h5 class="mb_20">Expertise</h5>
                            <div class="suggest_links">
                                <?php if(isset($user->userCategory)): ?>
                                    <?php $__empty_1 = true; $__currentLoopData = $user->userCategory->where('status','approved'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sellerExpertise): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                         <a href="#!" class="card_links"><?php echo e($sellerExpertise->category->name??'--'); ?></a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                         <span> No Expertise Available</span>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>












                    </div>
                    <div class="reviews_wrapper custom_section">
                        <h5 class="title">Reviews</h5>
                            <?php $__currentLoopData = $user->ratingReview; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ratingReview): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="single_review">
                            <div class="review_user">
                                <div class="review_user_img">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/inside_logo.png" alt="">
                                </div>
                                <div class="client_review">
                                            <h5><?php echo e($ratingReview->senderBy->name??''); ?></h5>
                                            <?php
                                                $stars = $ratingReview->stars ?? 0; // Get the star rating, default to 0 if not set
                                            ?>
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <span class="fa fa-star <?php echo e($i <= $stars ? 'checked' : ''); ?>"></span>
                                    <?php endfor; ?>
                                </div>
                            </div>
                                    <h5><?php echo e($ratingReview->review??'---'); ?></h5>
                            <div class="review_date_main">
                                        <span class="review_date"><h6><?php echo e($ratingReview->created_at??'---'); ?></h6></span>
                            </div>
                        </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal fade service_description" id="service_description_content" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Service Provider Description</h2>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="description">
                        <p><?php echo e($user->profile->bio??''); ?>

                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="flag_view" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Details</h3>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Reason To Flag</label>
                                    <textarea placeholder="Enter Comments" rows="5" class="form-control"></textarea>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade edit_portfolio" id="editPortfolio" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Add Portfolio</h2>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('edit-portfolio')); ?>" method="post" enctype="multipart/form-data" id="edit_porfolio_form">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="user_id" value="<?php echo e($user->id??''); ?>">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <h3>Add Images</h3>
                                <div id="custom_file_upload" class="custom_file_upload">



                                    <div class="append_type_wrapper">
                                        <div class="append_type_file">
                                            <input type="file" class="file-input" name="portfolio_attachments[]" accept="image/*" />
                                            <a href="#!">
                                                <i class="fa-solid fa-image"></i>
                                            </a>
                                            <img src="" class="image_preview" alt="Image Preview" style="display: none;" />
                                            <button type="button" class="close-btn append_img_div_remove">
                                                <i class="fa-solid fa-close"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <label>Enter Title</label>
                                <div class="txt_field"><input type="text" class="form-control" name="title" placeholder="Enter Title"/></div>
                            </div>
                            <div class="col-md-12">
                                <label>Enter Description</label>
                                <div class="txt_field">
                                    <textarea type="text" class="form-control" name="description" placeholder="Enter Title"> </textarea>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade edit_portfolio" id="addExpertise" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Add Expertise</h2>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="<?php echo e(route('add_seller_expertise')); ?>" >
                        <?php echo csrf_field(); ?>
                        <div class="row custom_row">
                            <input name="user_id" type="hidden" value="<?php echo e($user->id); ?>">
                            <div class="col-md-12">
                                <div class="custom_multi_select">
                                    <label>Select Expertise</label>
                                    <select name="expertise_id[]" multiple class="form-control custom_multiselect" required multiple>
                                        <?php $__currentLoopData = $expertise; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($item->id); ?>" <?php if(isset($user->assignUserExpertise) && in_array($item->id, $user->assignUserExpertise->pluck('expertise_id')->toArray())): ?> selected <?php endif; ?> ><?php echo e($item->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade edit_portfolio portfolio_images" id="portfolio_images" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">

    </div>

    <div class="modal fade add_location" id="addLocation" tabindex="-1" aria-labelledby="exampleModalLabel" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content append_input_wrapper">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalLabel1">Add Geographical Preferences</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('store_geographical_preferences')); ?>" method="post">
                        <?php echo csrf_field(); ?>
                        <div class="address_container">
                            <div class="custom_address">
                                <div class="txt_field">
                                    <label for="" class="form-label">Address</label>
                                    <input type="text" class="form-control geography_address" placeholder="Enter Address" id="geographyAddress" name="geography_address" required>
                                    <input type="hidden" class="custom_lat" name="latitude">
                                    <input type="hidden" class="custom_lng" name="longitude">
                                    <input type="hidden" class="custom_city" name="geography_city">
                                    <input type="hidden" class="custom_state" name="geography_state">
                                    <input type="hidden" class="custom_postal" name="geography_postal">
                                    <input type="hidden" class="custom_country" name="geography_country">
                                </div>
                            </div>
                        </div>
                        <div class="radius_container">
                            <div class="txt_field">
                                <label for="" class="form-label">Radius (in yards)</label>
                                <input type="number" class="form-control" max="50000" min="0" name="geography_radius" placeholder="Enter Radius (in yards)" required>
                            </div>
                        </div>
                        <div class="custom_map">
                            <label for="" class="form-label">Map</label>
                            <div id="map" class="map" style="width: 100%;"></div>
                        </div>
                        <div class="submit_btn">
                            <button type="submit" class="btn btn_black">Submit</button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade add_location" id="addRestrictedStates" tabindex="-1" aria-labelledby="exampleModalLabel2" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content append_input_wrapper">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalLabel12">Add Restricted States</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('store_restricted_states')); ?>" method="post">
                        <?php echo csrf_field(); ?>
                        <div class="radius_container txt_field">
                            <div class="custom_multi_select">
                                <label for="restricted_states" class="form-label">Restricted States</label>
                                <select name="restricted_states[]" id="restricted_states" class="form-control custom_multiselect" multiple>
                                    <option value="" disabled>Select a state</option>
                                    <?php $__currentLoopData = ['Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($state); ?>" <?php if($user->getUserRestrictedState->pluck('state')->contains($state)): ?> selected <?php endif; ?>>
                                            <?php echo e($state); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="submit_btn">
                            <button type="submit" class="btn btn_black">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <div class="modal add_location" id="addLocationView" tabindex="-1" aria-labelledby="exampleModalLabel" role="dialog">
    </div>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.21.0/jquery.validate.min.js" integrity="sha512-KFHXdr2oObHKI9w4Hv1XPKc898mE4kgYx58oqsc/JqqdLMDI4YjOLzom+EMlW8HFUd0QfjfAvxSL6sEq/a42fQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
<script>
    $(document).ready(function() {
        $('[data-fancybox="gallery"]').fancybox({
            protect: false,
            clickOutside: false,
            closeExisting: false,
        });
    });
</script>
<script>
    $(document).ready(function() {
        $('.custom_multiselect').select2({
            placeholder: "Select An Option",
            allowClear: true
        });
        $("#edit_porfolio_form").validate({
            rules:{
                "portfolio_attachments[]":{
                    required:true,
                },
                "title[]":{
                    required:true,
                }
            }
        });

        // $(document).on("click",".suggest_links .suggested_location button",function () {
        //     $(this).closest(".suggested_location").remove();
        // })
        $(document).on("click", ".delete_btn_loc", function () {
            var loc_id = $(this).attr('location-id');
            var user_id = "<?php echo e($user->id); ?>";
            var thisDeleteButton = $(this);
            // Show confirmation dialog
            Swal.fire({
                title: "Are you sure?",
                text: "Do you really want to delete this location? This action cannot be undone.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "No, cancel!",
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "<?php echo e(url('delete_geo_location')); ?>/" + user_id + '/' + loc_id, // Replace with your route
                        success: function (response) {
                            Swal.fire("Deleted!", "The location has been deleted.", "success");
                            $(thisDeleteButton).closest(".suggested_location").remove();
                        }
                    });
                } else if (result.dismiss === Swal.DismissReason.cancel) {
                    Swal.fire("Cancelled", "The location is safe!", "info");
                }
            });
        });
    });
</script>

<script>
    $(document).ready(function() {
        // $(document).on("click",'.append_btn',function() {
        $('.append_type_file .append_img_div_remove').hide();
        $(document).on("input",'.append_type_file .file-input',function() {

            if($(this).val() != '')
            {
                $(this).closest('.custom_file_upload').append(`
                    <div class="append_type_wrapper">
                        <div class="append_type_file">
                            <input type="file" class="file-input" name="portfolio_attachments[]" accept="image/*" />
                            <a href="#!">
                                <i class="fa-solid fa-image"></i>
                            </a>
                            <img src="" class="image_preview" alt="Image Preview" style="display: none;" />
                            <button type="button" class="close-btn append_img_div_remove">
                                <i class="fa-solid fa-close"></i>
                            </button>
                        </div>
                    </div>
                `);
                $(this).closest('.append_type_file').find('.append_img_div_remove').show();
            }

                $('.append_type_file .file-input').each(function () {
                    if ($(this).val() === '' && $(this).closest('.append_type_file').find('.image_preview').attr("src").trim() === "") {
                        $(this).closest('.append_type_file').find('.append_img_div_remove').hide();
                    }
                });
        });

        <?php if(isset($role) && $role == 'seller'): ?>
        $(document).on("click",".sellerPortfolio",function () {

            let sellerPortfolioId = $(this).attr('seller-portfolio-id');
            $.ajax({
                url: "<?php echo e(url('get_portfolio_detail')); ?>/" + sellerPortfolioId, // Replace with your route
                success: function (response) {
                    $('#portfolio_images').html(response);
                    $('#portfolio_images').modal('show');
                    $('.append_type_file .append_img_div_remove').hide();
                    // $(document).on("click",".images_wrapper .append_img_div_remove",function () {
                    //     $(this).closest(".images_wrapper").remove();
                    // });
                }
            });

        });
        <?php else: ?>
            $(document).on("click",".buyer_want_to_see_portfolio",function () {
                let sellerPortfolioId = $(this).attr('seller-portfolio-id');
                $.ajax({
                    url: "<?php echo e(url('seller_portfolio')); ?>/" + sellerPortfolioId, // Replace with your route
                    success: function (response) {
                        $('#portfolio_images').html(response);
                        $('#portfolio_images').modal('show');
                        $('.append_type_file .append_img_div_remove').hide();
                        // $(document).on("click",".images_wrapper .append_img_div_remove",function () {
                        //     $(this).closest(".images_wrapper").remove();
                        // });
                    }
                });
            });
        <?php endif; ?>


        $(document).on("click",".append_type_wrapper .append_img_div_remove",function () {
            $(this).closest(".append_type_wrapper").remove();
        });

        $(document).on("click",".suggested_location_click",function () {
            let geograph_id = $(this).attr('geograph-id');
            $.ajax({
                url: "<?php echo e(url('get_geo_location')); ?>/" + geograph_id, // Replace with your route
                success: function (response) {
                    $('#addLocationView').html(response);
                    $('#addLocationView').modal('show');
                }
            });
        });
        $(document).on("click",".custom_portfolio_images .custom_portfolio_wrapper .append_img_div_remove",function () {
            $(this).closest(".custom_portfolio_wrapper").remove();
        });
        $(document).on("change", ".append_type_wrapper .append_type_file input[type='file']", function(event) {
            const fileDiv = $(this).closest('.append_type_file')[0];  // Get the closest div for file input
            const fileInput = fileDiv.querySelector('.file-input');  // Get the file input
            const img = fileDiv.querySelector('img');  // Get the image element
            const plusIcon = fileDiv.querySelector('a>i');  // Get the plus icon

            // Handle file input change
            const file = fileInput.files[0];  // Get the selected file
            if (file) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    img.src = e.target.result;  // Set the image source to the selected file
                    img.style.display = 'block';  // Show the image
                    plusIcon.style.display = 'none';  // Hide the plus icon
                };

                reader.readAsDataURL(file);  // Read the file as a Data URL
            }
        });
        $(document).on("change" , '.file-input', function () {
            var validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp','webp'];
            var invalidFiles = [];
            var fileInputs = document.getElementsByClassName('file-input');
            for (var i = 0; i < fileInputs.length; i++) {
                var files = fileInputs[i].files;
                for (var j = 0; j < files.length; j++) {
                    var fileName = files[j].name;
                    var fileExtension = fileName.split('.').pop().toLowerCase();

                    if (!validExtensions.includes(fileExtension)) {
                        invalidFiles.push(fileName); // Store invalid file names
                    }
                }
            }
            if (invalidFiles.length > 0) {
                var images = document.getElementsByClassName('image_preview');
                var job_input_type_file = document.getElementsByClassName('file-input');

                for (var i = 0; i < images.length; i++) {
                    images[i].src="";
                    job_input_type_file[i].value=""
                }
                Swal.fire({
                    title: "Fail",
                    text: "Only Images are allowed.",
                    icon: "error",
                    confirmButtonText: "OK",
                    confirmButtonColor: "#3085d6",
                    timer: 5000,
                    allowOutsideClick: false,
                });

                return;
            }

        })
    });

    // $(document).ready(function () {
    //     let map;
    //     let marker;
    //     let circle;
    //     const defaultLocation = { lat: 25.276987, lng: 55.296249 }; // Default location (e.g., Dubai)
    //
    //     // Initialize the map
    //     function initializeMap() {
    //         if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
    //             console.error('Google Maps API not loaded');
    //             return;
    //         }
    //         map = new google.maps.Map(document.getElementById("map"), {
    //             center: defaultLocation,
    //             zoom: 16,
    //         });
    //         marker = new google.maps.Marker({
    //             position: defaultLocation,
    //             map: map,
    //             draggable: true,
    //         });
    //         circle = new google.maps.Circle({
    //             map: map,
    //             radius: 100 * 0.9144, // Default radius in yards converted to meters
    //             fillColor: "#AA0000",
    //             strokeColor: "#AA0000",
    //             strokeOpacity: 0.5,
    //             fillOpacity: 0.2,
    //         });
    //
    //         circle.bindTo("center", marker, "position");
    //
    //         google.maps.event.addListener(marker, "dragend", function () {
    //             updateLatLngInputs(marker.getPosition());
    //         });
    //     }
    //
    //     // Update hidden input fields for lat/lng
    //     function updateLatLngInputs(location) {
    //         $(".custom_lat").val(location.lat());
    //         $(".custom_lng").val(location.lng());
    //     }
    //
    //     // Update hidden fields for city, state, postal, country
    //     function updateAddressComponents(place) {
    //         $(".custom_city").val(getAddressComponent(place, "locality"));
    //         $(".custom_state").val(getAddressComponent(place, "administrative_area_level_1"));
    //         $(".custom_postal").val(getAddressComponent(place, "postal_code"));
    //         $(".custom_country").val(getAddressComponent(place, "country"));
    //     }
    //
    //     // Get address components based on type
    //     function getAddressComponent(place, type) {
    //         for (let i = 0; i < place.address_components.length; i++) {
    //             let component = place.address_components[i];
    //             if (component.types.includes(type)) {
    //                 return component.long_name;
    //             }
    //         }
    //         return "";
    //     }
    //
    //     // Update the marker position and map center
    //     function updateMarkerAndMap(address) {
    //         const geocoder = new google.maps.Geocoder();
    //
    //         geocoder.geocode({ address: address }, function (results, status) {
    //             if (status === "OK") {
    //                 const location = results[0].geometry.location;
    //
    //                 // Update marker position
    //                 marker.setPosition(location);
    //                 map.setCenter(location);
    //
    //                 // Update hidden lat/lng fields
    //                 updateLatLngInputs(location);
    //
    //                 // Update address components
    //                 updateAddressComponents(results[0]);
    //             } else {
    //                 alert("Geocode was not successful for the following reason: " + status);
    //             }
    //         });
    //     }
    //
    //     // Initialize Autocomplete
    //     function initializeAutocomplete() {
    //         $(".geography_address").each(function () {
    //             let input = this;
    //             if (!input.dataset.autocompleteInitialized) {
    //                 let autocomplete = new google.maps.places.Autocomplete(input);
    //
    //                 autocomplete.addListener("place_changed", function () {
    //                     let place = autocomplete.getPlace();
    //
    //                     if (place.geometry) {
    //                         const location = place.geometry.location;
    //
    //                         // Update marker and map
    //                         marker.setPosition(location);
    //                         map.setCenter(location);
    //
    //                         // Update hidden lat/lng fields
    //                         updateLatLngInputs(location);
    //
    //                         // Update address components
    //                         updateAddressComponents(place);
    //                     }
    //                 });
    //
    //                 // Mark input as initialized
    //                 input.dataset.autocompleteInitialized = true;
    //             }
    //         });
    //     }
    //
    //     // Adjust radius when radius input changes
    //     $(document).on("input", ".radius_container input", function () {
    //         const radiusInYards = parseInt($(this).val()) || 0;
    //         const radiusInMeters = radiusInYards * 0.9144; // Convert yards to meters
    //         circle.setRadius(radiusInMeters);
    //     });
    //
    //     // Initialize map and autocomplete on page load
    //     initializeMap();
    //     initializeAutocomplete();
    // });
    let map;
    let marker;
    let circle;
    const defaultLocation = { lat: 25.276987, lng: 55.296249 }; // Default location (e.g., Dubai)

    // Function to initialize the map - only called when Google Maps is loaded
    function initializeMap() {
        // Wait for Google Maps to be available
        if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
            console.log('Google Maps not ready, retrying...');
            setTimeout(initializeMap, 100);
            return;
        }

        try {
            map = new google.maps.Map(document.getElementById("map"), {
                center: defaultLocation,
                zoom: 16,
            });

            marker = new google.maps.Marker({
                position: defaultLocation,
                map: map,
                draggable: true,
            });

            circle = new google.maps.Circle({
                map: map,
                radius: 100 * 0.9144, // Default radius in yards converted to meters
                fillColor: "#AA0000",
                strokeColor: "#AA0000",
                strokeOpacity: 0.5,
                fillOpacity: 0.2,
            });

            circle.bindTo("center", marker, "position");

            google.maps.event.addListener(marker, "dragend", function () {
                updateLatLngInputs(marker.getPosition());
            });

            // Initialize autocomplete after map is ready
            initializeAutocomplete();

            console.log('Map initialized successfully');
        } catch (error) {
            console.error('Error initializing map:', error);
        }
    }

    // Update hidden input fields for lat/lng
    function updateLatLngInputs(location) {
        $(".custom_lat").val(location.lat());
        $(".custom_lng").val(location.lng());
    }

    // Update hidden fields for city, state, postal, country
    function updateAddressComponents(place) {
        const addressComponents = place.address_components;

        let city = '';
        let state = '';
        let postal = '';
        let country = '';

        // Extract address components with comprehensive fallbacks
        addressComponents.forEach((component) => {
            const types = component.types;console.log(component, 'this is complent');

            // City extraction with multiple fallbacks
            if (types.includes('locality')) {
                city = component.orignal_name;
            } else if (types.includes('sublocality') && !city) {
                city = component.long_name;
            } else if (types.includes('administrative_area_level_2') && !city) {
                city = component.long_name;
            } else if (types.includes('administrative_area_level_3') && !city) {
                city = component.long_name;
            }

            // State/Province/Governorate extraction
            if (types.includes('administrative_area_level_1')) {
                state = component.long_name;
            } else if (types.includes('administrative_area_level_2') && !state) {
                state = component.long_name;
            }

            if (types.includes('postal_code')) {
                postal = component.long_name;
            }
            if (types.includes('country')) {
                country = component.long_name;
            }
        });

        // Validate that we have at least city information
        if (!city) {
            alert('Please select a more specific location that includes city information.');
            return false;
        }

        // Update hidden fields with extracted components
        $(".custom_city").val(city);
        $(".custom_state").val(state);
        $(".custom_postal").val(postal);
        $(".custom_country").val(country);

        // IMPORTANT: Keep the original formatted address from Google
        // Don't override the user's input with our simplified format
        // The formatted_address from Google is more accurate and complete
        $("#geographyAddress").val(place.formatted_address);


        return true;
    }



    // Update the marker position and map center

    // Initialize Autocomplete
    function initializeAutocomplete() {
        if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
            console.error('Google Maps Places API not available');
            return;
        }

        $(".geography_address").each(function () {
            let input = this;
            if (!input.dataset.autocompleteInitialized) {
                let autocomplete = new google.maps.places.Autocomplete(input, {
                    types: ['address'], // Allow full addresses
                    // Remove country restriction to allow worldwide
                    fields: ['address_components', 'geometry', 'formatted_address']
                });

                autocomplete.addListener("place_changed", function () {
                    let place = autocomplete.getPlace();

                    if (!place.geometry) {
                        alert('Please select a valid location from the dropdown suggestions.');
                        return;
                    }

                    // Validate and update address components
                    if (updateAddressComponents(place)) {
                        const location = place.geometry.location;

                        // Update marker and map
                        if (marker && map) {
                            marker.setPosition(location);
                            map.setCenter(location);
                        }

                        // Update hidden lat/lng fields
                        updateLatLngInputs(location);
                    } else {
                        // Clear the input if validation failed
                        $(input).val('');
                        $(".custom_city, .custom_state, .custom_postal, .custom_country, .custom_lat, .custom_lng").val('');
                    }
                });

                // Mark input as initialized
                input.dataset.autocompleteInitialized = true;
            }
        });
    }


    // Adjust radius when radius input changes
    $(document).on("input", ".radius_container input", function () {
        if (circle) {
            const radiusInYards = parseInt($(this).val()) || 0;
            const radiusInMeters = radiusInYards * 0.9144; // Convert yards to meters
            circle.setRadius(radiusInMeters);
        }
    });

    // Initialize when modal is opened
    $(document).on('shown.bs.modal', '#addLocation', function () {
        console.log('Modal opened, initializing map...');
        // Delay to ensure modal is fully rendered and Google Maps is ready
        setTimeout(function() {
            if (document.getElementById("map")) {
                initializeMap();
            } else {
                console.error('Map element not found');
            }
        }, 500);
    })
    function getAddressComponent(place, type) {
        if (!place.address_components) return '';

        for (let component of place.address_components) {
            if (component.types.includes(type)) {
                return component.long_name;
            }
        }
        return '';
    }

    $(document).on('submit', '#geographical-preference-form', function(e) {
        const city = $(".custom_city").val();
        const state = $(".custom_state").val();

        if (!city || !state) {
            e.preventDefault();
            alert('Please select a location that includes both city and state information.');
            return false;
        }

        // Additional validation for meaningful data
        if (city.length < 2 || state.length < 2) {
            e.preventDefault();
            alert('Please select a valid city and state.');
            return false;
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/serviceProvider/service_profile.blade.php ENDPATH**/ ?>