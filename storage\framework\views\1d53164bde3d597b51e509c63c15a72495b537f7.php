



<?php
    $chat_messages = $response['message'];
    setlocale(LC_TIME, 'fr_FR');
?>
<?php $__currentLoopData = $chat_messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
   <?php if(Auth::user()->hasRole('buyer') && $message->getFromUser->hasRole('seller') && $key == 0 && $message->status == 0): ?>
        <div class="accept_reject_msg">
            <div class="user_profile">
                <div class="user_img">
                    <img src="<?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($message->getFromUser->profile->pic??''); ?>">
                </div>
                <div class="user_name">
                    <h5><?php echo e($message->getFromUser->name); ?></h5>
                </div>
            </div>
            <div class="new_message">
                <h5><?php echo e($message->getFromUser->name); ?> would lke to message you</h5>
            </div>
            <div class="custom_btn">
                <button type="button" onclick="Acceptmessage('2','<?php echo e($message->id); ?>','<?php echo e($message->to_user_id); ?>','0','<?php echo e($message->product_id); ?>','<?php echo e(Auth::id()); ?>')" class="btn btn_red" value="rejected">Reject</button>
                <button type="button" onclick="Acceptmessage('1','<?php echo e($message->id); ?>','<?php echo e($message->to_user_id); ?>','0','<?php echo e($message->product_id); ?>','<?php echo e(Auth::id()); ?>')" class="btn btn_green" value="accepted">Accept</button>
            </div>
        </div>
        <script type="text/javascript"> $('#text_box_area').hide()</script>
    <?php elseif(Auth::user()->hasRole('buyer') && $message->getFromUser->hasRole('seller') && $key == 0 && $message->status == 2): ?>
        <div class="accept_reject_msg">
            <div class="user_profile">
                <div class="user_img">
                    <img src="<?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($message->getFromUser->profile->pic??''); ?>">
                </div>
                <div class="user_name">
                    <h5><?php echo e($message->getFromUser->name); ?></h5>
                </div>
            </div>
            <div class="new_message">
                <h5>Message Has Been Rejected</h5>
            </div>

        </div>
         <script type="text/javascript"> $('#text_box_area').hide()</script>
    <?php elseif($message->from_user_id == Auth::user()->id): ?>
       <div class="msg_container base_sent main_div_row" data-message-id="<?php echo e($message->id); ?>">
           <?php if(Auth::user()->hasRole('user')): ?>
               <?php if($message->name == "0"): ?>
               <?php else: ?>
                 
               <?php endif; ?>
           <?php endif; ?>
           <div class="message_container">
               <p><?php echo $message->content??''; ?></p>
               <?php if($message->name == "0"): ?>
               <?php else: ?>
                   <p class="time">
                       <span> <time datetime="<?php echo e(date("Y-m-dTH:i", strtotime($message->created_at->toDateTimeString()))); ?>"><?php if($message->getFromUser->name == "User"): ?> Mr do all <?php else: ?> <?php echo e($message->getFromUser->name??''); ?> <?php endif; ?> <?php echo e($message->created_at->diffForHumans()); ?></time></span>
                   </p>
               <?php endif; ?>
               <div class="msg_time"><p><time datetime="<?php echo e($message->created_at->timezone('Asia/Karachi')->format('h:i A')); ?>">
                           <?php echo e($message->created_at->timezone('Asia/Karachi')->format('h:i A')); ?>

                       </time></p>
               </div>
           </div>
           <div class="user_img">
               <img src="<?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($message->getFromUser->profile->pic??''); ?>" width="50" height="50" class=" img-responsive ">
           </div>
       </div>
    <?php else: ?>
        <div class="msg_container base_receive main_div_row" data-message-id="<?php echo e($message->id); ?>">
            <?php if($message->getFromUser->hasRole('customer')): ?>
                <a  href="<?php echo e(route('saller_profile')); ?>/<?php echo e(base64_encode(strtr($message->getFromUser->id, '._-', '+/='))); ?>">
                    <div class="user_img">
                        <img src="<?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($message->getFromUser->profile->pic??''); ?>" width="50" height="50" class=" img-responsive ">
                    </div>
                </a>
            <?php else: ?>
               <div class="user_img">
                    <img src="<?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($message->getFromUser->profile->pic??''); ?>" width="50" height="50" class=" img-responsive ">
                </div>
            <?php endif; ?>
            <div class="message_container">
                <p><?php echo $message->content??''; ?></p>
                <?php if($message->name == "0"): ?>
                <?php else: ?>
                    <p class="time">
                        <span> <time datetime="<?php echo e(date("Y-m-dTH:i", strtotime($message->created_at->toDateTimeString()))); ?>"><?php if($message->getFromUser->name == "User"): ?> Mr do all <?php else: ?> <?php echo e($message->getFromUser->name??''); ?> <?php endif; ?> <?php echo e($message->created_at->diffForHumans()); ?></time></span>
                    </p>
                <?php endif; ?>
                <div class="msg_time"><p><time datetime="<?php echo e($message->created_at->timezone('Asia/Karachi')->format('h:i A')); ?>">
                            <?php echo e($message->created_at->timezone('Asia/Karachi')->format('h:i A')); ?>

                        </time></p>
                </div>
            </div>
            <?php if(Auth::user()->hasRole('user')): ?>
                <?php if($message->name == "0"): ?>
                <?php else: ?>
                
                <?php endif; ?>
            <?php endif; ?>
        </div>
    <script type="text/javascript"> $('#text_box_area').show()</script>
    <?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/ajax/get_all_users.blade.php ENDPATH**/ ?>