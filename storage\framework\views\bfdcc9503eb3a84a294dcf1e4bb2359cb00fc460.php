<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">User Management</h2>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('backbtn_nav'); ?>
    <a class="navbar_backBtn" href="<?php echo e(url('users')); ?>">
        <i class="fa-solid fa-chevron-left"></i>
    </a>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>

    <section class="projects_wrapper">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="staff_info custom_projects_tabs document_section">
                        <div class="row form_row_gap">
                            <div class="col-md-12">
                                <div class="user_management">
                                    <div class="staff_profile">
                                        <div class="profile_img">
                                            <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($user->profile->pic??''); ?>">
                                        </div>
                                        <div class="custom_resolved_project">
                                            <label>User Role:<span><?php if($user->hasRole('buyer')): ?> Client <?php elseif($user->hasRole('seller')): ?> Contractor <?php else: ?> Staff <?php endif; ?></span></label>
                                        </div>
                                    </div>
                                    <div class="suspend_ban_btn">
                                        <?php if($user->user_status == 'suspended'): ?>
                                            <button type="button" class="btn btn_black user_perform_action" user-id="<?php echo e($user->id); ?>" data-action="active"> Active <img src="<?php echo e(asset('website')); ?>/assets/images/ban.png"></button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn_black user_perform_action" user-id="<?php echo e($user->id); ?>" data-action="suspended"> Suspend <img src="<?php echo e(asset('website')); ?>/assets/images/ban.png"></button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn_maroon user_perform_action" user-id="<?php echo e($user->id); ?>" data-action="ban"> Ban <i class="fa-solid fa-eye-slash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <h4 class="info_title">Personal Information</h4>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="txt_field">
                                    <label for="" class="form-label">First Name:</label>
                                    <span><?php echo e($user->first_name??''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="txt_field">
                                    <label for="" class="form-label">Last Name:</label>
                                    <span><?php echo e($user->last_name??''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="txt_field">
                                    <label for="" class="form-label">Date of Birth:</label>
                                    <span><?php echo e($user->created_at->format('F d Y')??''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <h4 class="info_title2">Contact Information</h4>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="txt_field">
                                    <label for="email" class="form-label">Email:</label>
                                    <span><?php echo e($user->email??''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="txt_field">
                                    <label for="phone_no" class="form-label phoneLabel">Phone Number:</label>
                                    <span><?php echo e($user->profile->phone??''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="custom_projects_tabs document_section">
                        <div class="cms_tabs">
                            <ul class="nav projects nav-pills">
                                <li class="active"><a href="#open_projects" data-toggle="tab" aria-expanded="false">Open Projects</a></li>
                                <li class=""><a href="#closed_projects" data-toggle="tab" aria-expanded="false">Closed Projects</a></li>
                                <li class=""><a href="#dispute_projects" data-toggle="tab" aria-expanded="false">Disputed Projects</a></li>
                            </ul>
                        </div>
                        <div class="custom_search_filter custom_flex">
                            <div class="txt_field custom_search">
                                <input type="text" placeholder="Search" class="custom_search_box">
                            </div>
                            <div class="filter_btn">
                                <form class="fliters_form_wrapper" id="">
                                    <div class="dropdown">
                                        <button type="button" class="btn btn_black dropdown-toggle" id="filterDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                            Filter<img src="<?php echo e(asset('website')); ?>/assets/images/filter.png"></button>
                                        <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                                            <li>
                                                <h5 class="filter_title">Projects Posted Date:</h5>
                                            </li>
                                            <li>
                                                <div class="txt_field">
                                                    <input type="date" class="form-control" id="" placeholder="DD/MM/YYYY">
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <?php if($user->hasRole('buyer')): ?>
                            <div class="tab-content custom_tab_content buyer">
                                <div class="tab-pane active" id="open_projects">
                                    <div class="row custom_rowGap">
                                        <?php if(isset($user->getUserJobs)): ?>
                                            <?php $__empty_1 = true; $__currentLoopData = $user->getUserJobs->where('status','on_going'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <div class="col-md-12 document_wrapper">
                                                    <div class="open_projects document_details">
                                                        <div class="date_posted">
                                                            <label>Posted On:<span><?php echo e($job->created_at->format('F d Y')); ?>  <?php echo e($job->user_id); ?></span></label>
                                                        </div>
                                                        <div class="custom_categories">
                                                            <a href="#!" class=""><?php echo e($job->category->name ?? '----'); ?> / <?php echo e($job->subCategory->name ?? '----'); ?></a>
                                                        </div>
                                                        <div class="mt_20">
                                                            <h5>Budget:</h5>
                                                            <h3>$<?php echo e($job->project_budget_min??''); ?> - $<?php echo e($job->project_budget_max??''); ?></h3>
                                                        </div>
                                                        <div class="custom_user_project_img">
                                                            <h5>Photos</h5>
                                                            <div class="custom_projects_images">
                                                                <?php if(!empty($job->jobFiles)): ?>
                                                                    <?php $__currentLoopData = $job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php
                                                                            $fileUrl = $file->file ?? '---';
                                                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                        ?>

                                                                        <?php if($isImage): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid">
                                                                                </div>
                                                                            </a>
                                                                        <?php elseif($isVideo): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <video controls preload="metadata" class="img-fluid video-player">
                                                                                        <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                        Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                                                    </video>
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <div class="project_photos">
                                                                                <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="further_details">
                                                            <?php if(!empty($job->jobQuestionAnswer)): ?>
                                                                <?php $__currentLoopData = $job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <div class="custom_budget_detail">
                                                                        <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                                                        <?php
                                                                            $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                                                        ?>
                                                                        <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $value)): ?>
                                                                            <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="">
                                                                                <div class="custom_images">
                                                                                    <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="max-width: 100%;">
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </div>

                                                        <?php if(isset($job->jobAssignStaffMeasurements) && !$job->jobAssignStaffMeasurements->isEmpty()): ?>
                                                            <div class="project_detail">
                                                                <div class="custom_flex">
                                                                    <h3>Further Details</h3>
                                                                </div>
                                                                <div class="project_scope">
                                                                    <h5>Measurements : </h5>
                                                                    <?php $__currentLoopData = $job->jobAssignStaffMeasurements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $measurement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <h6>Name:<?php echo e($measurement->name??'-'); ?>, Number:<?php echo e($measurement->number??'-'); ?>, Unit:<?php echo e($measurement->unit??'-'); ?></h6>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <h5>Specifications</h5>
                                                                    <h6><?php echo $job->staff_specifications ?? '----'; ?></h6>
                                                                </div>
                                                            </div>

                                                            <?php if(isset($job->jobAssignStaffDocuments) && !empty($job->jobAssignStaffDocuments)): ?>
                                                                <div class="col-md-12">
                                                                    <div class="project_detail">
                                                                        <div class="">
                                                                            <h5> Media</h5>
                                                                        </div>
                                                                        <div class="media_download">
                                                                            <?php $__currentLoopData = $job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php
                                                                                    $fileUrl = $file->image ?? '---';
                                                                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                                ?>
                                                                                <?php if($isImage): ?>
                                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                        <div class="custom_images">
                                                                                            <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="">
                                                                                        </div>
                                                                                    </a>
                                                                                <?php elseif($isVideo): ?>
                                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                        <div class="custom_images staff_video">
                                                                                            <div class="downloadable_video">
                                                                                                <video controls preload="metadata" class="video-player">
                                                                                                    <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                                    Your browser does not support this video format.
                                                                                                </video>
                                                                                            </div>
                                                                                        </div>
                                                                                    </a>
                                                                                <?php else: ?>
                                                                                    <div class="custom_images">
                                                                                        <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                                    </div>
                                                                                <?php endif; ?>
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>

                                                        <?php endif; ?>
                                                        <div class="mt_20">
                                                            <a href="<?php echo e(url('projects_pending')); ?>/<?php echo e($job->id); ?>" class="btn btn_black">View <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <p class="text-danger">There is no jobs against this user</p>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="tab-pane" id="closed_projects">
                                    <div class="row custom_rowGap">
                                        <?php if(isset($user->getUserJobs)): ?>
                                            <?php $__empty_1 = true; $__currentLoopData = $user->getUserJobs->where('status','completed'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <div class="col-md-12 document_wrapper">
                                                    <div class="open_projects document_details">
                                                        <div class="date_posted">
                                                            <label>Posted On:<span><?php echo e($job->created_at->format('F d Y')); ?> </span></label>
                                                        </div>
                                                        <div class="custom_categories">
                                                            <a href="#!" class=""><?php echo e($job->category->name ?? '----'); ?> / <?php echo e($job->subCategory->name ?? '----'); ?></a>
                                                        </div>
                                                        <div class="mt_20">
                                                            <h5>Budget:</h5>
                                                            <h3>$<?php echo e($job->project_budget_min??''); ?> - $<?php echo e($job->project_budget_max??''); ?></h3>
                                                        </div>
                                                        <div class="custom_user_project_img">
                                                            <h5>Photos</h5>
                                                            <div class="custom_projects_images">
                                                                <?php if(!empty($job->jobFiles)): ?>
                                                                    <?php $__currentLoopData = $job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php
                                                                            $fileUrl = $file->file ?? '---';
                                                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                        ?>

                                                                        <?php if($isImage): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid">
                                                                                </div>
                                                                            </a>
                                                                        <?php elseif($isVideo): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <video controls preload="metadata" class="img-fluid video-player">
                                                                                        <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                        Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                                                    </video>
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <div class="project_photos">
                                                                                <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="further_details">
                                                            <?php if(!empty($job->jobQuestionAnswer)): ?>
                                                                <?php $__currentLoopData = $job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <div class="custom_budget_detail">
                                                                        <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                                                        <?php
                                                                            $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                                                        ?>
                                                                        <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $value)): ?>
                                                                            <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="">
                                                                                <div class="custom_images">
                                                                                    <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="max-width: 100%;">
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </div>

                                                        <?php if(isset($job->jobAssignStaffMeasurements) && !empty($job->jobAssignStaffMeasurements)): ?>
                                                            <div class="project_detail">
                                                                <div class="custom_flex">
                                                                    <h3>Further Details</h3>
                                                                </div>
                                                                <div class="project_scope">
                                                                    <h5>Measurements : </h5>
                                                                    <?php $__currentLoopData = $job->jobAssignStaffMeasurements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $measurement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <h6>Name:<?php echo e($measurement->name??'-'); ?>, Number:<?php echo e($measurement->number??'-'); ?>, Unit:<?php echo e($measurement->unit??'-'); ?></h6>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <h5>Specifications</h5>
                                                                    <h6><?php echo $job->staff_specifications ?? '----'; ?></h6>
                                                                </div>
                                                            </div>

                                                            <?php if(isset($job->jobAssignStaffDocuments) && !empty($job->jobAssignStaffDocuments)): ?>
                                                                <div class="col-md-12">
                                                                    <div class="project_detail">
                                                                        <div class="">
                                                                            <h5> Media</h5>
                                                                        </div>
                                                                        <div class="media_download">
                                                                            <?php $__currentLoopData = $job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php
                                                                                    $fileUrl = $file->image ?? '---';
                                                                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                                ?>
                                                                                <?php if($isImage): ?>
                                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                        <div class="custom_images">
                                                                                            <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="">
                                                                                        </div>
                                                                                    </a>
                                                                                <?php elseif($isVideo): ?>
                                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                        <div class="custom_images staff_video">
                                                                                            <div class="downloadable_video">
                                                                                                <video controls preload="metadata" class="video-player">
                                                                                                    <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                                    Your browser does not support this video format.
                                                                                                </video>
                                                                                            </div>
                                                                                        </div>
                                                                                    </a>
                                                                                <?php else: ?>
                                                                                    <div class="custom_images">
                                                                                        <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                                    </div>
                                                                                <?php endif; ?>
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>

                                                        <?php endif; ?>
                                                        <div class="mt_20">
                                                            <a href="<?php echo e(url('projects_pending')); ?>/<?php echo e($job->id); ?>" class="btn btn_black">View <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <p class="text-danger">There is no jobs against this user</p>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="tab-pane" id="dispute_projects" >
                                    <div class="row custom_rowGap">
                                        <?php
                                            $processedJobIds = [];
                                        ?>
                                        <?php $__currentLoopData = $user->getUserJobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $disputed): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php $__currentLoopData = $disputed->disputedJobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if(in_array($job->job_id, $processedJobIds)): ?> <?php continue; ?> <?php endif; ?>
                                                <?php $processedJobIds[] = $job->job_id; ?>
                                                <div class="col-md-12 document_wrapper">
                                                    <div class="open_projects document_details">
                                                        <div class="date_posted">
                                                            <label>Posted On:<span><?php echo e($job->job->created_at->format('F d Y')); ?> </span></label>
                                                        </div>
                                                        <div class="custom_categories">
                                                            <a href="#!" class=""><?php echo e($job->job->category->name ?? '----'); ?> / <?php echo e($job->job->subCategory->name ?? '----'); ?></a>
                                                        </div>
                                                        <div class="mt_20">
                                                            <h5>Budget:</h5>
                                                            <h3>$<?php echo e($job->job->project_budget_min??''); ?> - $<?php echo e($job->job->project_budget_max??''); ?></h3>
                                                        </div>
                                                        <div class="custom_user_project_img">
                                                            <h5>Photos</h5>
                                                            <div class="custom_projects_images">
                                                                <?php if(!empty($job->job->jobFiles)): ?>
                                                                    <?php $__currentLoopData = $job->job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php
                                                                            $fileUrl = $file->file ?? '---';
                                                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                        ?>

                                                                        <?php if($isImage): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid">
                                                                                </div>
                                                                            </a>
                                                                        <?php elseif($isVideo): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <video controls preload="metadata" class="img-fluid video-player">
                                                                                        <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                        Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                                                    </video>

                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <div class="project_photos">
                                                                                <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="further_details">
                                                            <?php if(!empty($job->job->jobQuestionAnswer)): ?>
                                                                <?php $__currentLoopData = $job->job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <div class="custom_budget_detail">
                                                                        <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                                                        <?php
                                                                            $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                                                        ?>
                                                                        <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $value)): ?>
                                                                            <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="">
                                                                                <div class="custom_images">
                                                                                    <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="max-width: 100%;">
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php if(isset($job->job->jobAssignStaffMeasurements) && !empty($job->job->jobAssignStaffMeasurements)): ?>
                                                            <div class="project_detail">
                                                                <div class="custom_flex">
                                                                    <h3>Further Details</h3>
                                                                </div>
                                                                <div class="project_scope">
                                                                    <h5>Measurements : </h5>
                                                                    <?php $__currentLoopData = $job->job->jobAssignStaffMeasurements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $measurement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <h6>Name:<?php echo e($measurement->name??'-'); ?>, Number:<?php echo e($measurement->number??'-'); ?>, Unit:<?php echo e($measurement->unit??'-'); ?></h6>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <h5>Specifications</h5>
                                                                    <h6><?php echo $job->job->staff_specifications ?? '----'; ?></h6>
                                                                </div>
                                                            </div>

                                                            <?php if(isset($job->jobAssignStaffDocuments) && !empty($job->job->jobAssignStaffDocuments)): ?>
                                                                <div class="project_media">
                                                                    <div class="custom_flex">
                                                                        <h5>Media</h5>
                                                                    </div>
                                                                    <div class="media_download">
                                                                        <?php $__currentLoopData = $job->job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $documents): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <div class="images_with_document">
                                                                                <a href="<?php echo e(asset('website')); ?>/<?php echo e($documents->image??'--'); ?>" data-fancybox="">
                                                                                    <div class="custom_images">
                                                                                        <img src="<?php echo e(asset('website')); ?>/<?php echo e($documents->image??'--'); ?>">
                                                                                    </div>
                                                                                </a>
                                                                                <span><?php echo e($documents->name??'---'); ?></span>
                                                                            </div>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                        <div class="mt_20">
                                                            <a href="<?php echo e(url('projects_pending')); ?>/<?php echo e($job->job->id); ?>" class="btn btn_black">View <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                                                        </div>
                                                    </div>
                                                </div>

                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="tab-content custom_tab_content seller">
                                <div class="tab-pane active" id="open_projects">
                                    <div class="row custom_rowGap">
                                        <?php if(isset($user->getSellerJobs)): ?>
                                            <?php $__empty_1 = true; $__currentLoopData = $user->getSellerJobs->where('status','on_going'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>

                                                <div class="col-md-12 document_wrapper">
                                                    <div class="open_projects document_details">
                                                        <div class="date_posted">
                                                            <label>Posted On:<span><?php echo e($job->created_at->format('F d Y')); ?></span></label>
                                                        </div>
                                                        <div class="custom_categories">
                                                            <a href="#!" class=""><?php echo e($job->category->name ?? '----'); ?> / <?php echo e($job->subCategory->name ?? '----'); ?></a>
                                                        </div>
                                                        <div class="mt_20">
                                                            <h5>Budget:</h5>
                                                            <h3>$<?php echo e($job->project_budget_min??''); ?> - $<?php echo e($job->project_budget_max??''); ?></h3>
                                                        </div>
                                                        <div class="custom_user_project_img">
                                                            <h5>Photos</h5>
                                                            <div class="custom_projects_images">
                                                                <?php if(!empty($job->jobFiles)): ?>
                                                                    <?php $__currentLoopData = $job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php
                                                                            $fileUrl = $file->file ?? '---';
                                                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                        ?>

                                                                        <?php if($isImage): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid">
                                                                                </div>
                                                                            </a>
                                                                        <?php elseif($isVideo): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <video controls preload="metadata" class="img-fluid video-player">
                                                                                        <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                        Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                                                    </video>
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <div class="project_photos">
                                                                                <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="further_details">
                                                            <?php if(!empty($job->jobQuestionAnswer)): ?>
                                                                <?php $__currentLoopData = $job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <div class="custom_budget_detail">
                                                                        <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                                                        <?php
                                                                            $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                                                        ?>
                                                                        <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $value)): ?>
                                                                            <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="">
                                                                                <div class="custom_images">
                                                                                    <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="max-width: 100%;">
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </div>

                                                        <?php if(isset($job->jobAssignStaffMeasurements) && !empty($job->jobAssignStaffMeasurements)): ?>
                                                            <div class="project_detail">
                                                                <div class="custom_flex">
                                                                    <h3>Further Details</h3>
                                                                </div>
                                                                <div class="project_scope">
                                                                    <h5>Measurements : </h5>
                                                                    <?php $__currentLoopData = $job->jobAssignStaffMeasurements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $measurement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <h6>Name:<?php echo e($measurement->name??'-'); ?>, Number:<?php echo e($measurement->number??'-'); ?>, Unit:<?php echo e($measurement->unit??'-'); ?></h6>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <h5>Specifications</h5>
                                                                    <h6><?php echo $job->staff_specifications ?? '----'; ?></h6>
                                                                </div>
                                                            </div>

                                                            <?php if(isset($job->jobAssignStaffDocuments) && !empty($job->jobAssignStaffDocuments)): ?>
                                                                <div class="col-md-12">
                                                                    <div class="project_detail">
                                                                        <div class="">
                                                                            <h5> Media</h5>
                                                                        </div>
                                                                        <div class="media_download">
                                                                            <?php $__currentLoopData = $job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php
                                                                                    $fileUrl = $file->image ?? '---';
                                                                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                                ?>
                                                                                <?php if($isImage): ?>
                                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                        <div class="custom_images">
                                                                                            <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="">
                                                                                        </div>
                                                                                    </a>
                                                                                <?php elseif($isVideo): ?>
                                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                        <div class="custom_images staff_video">
                                                                                            <div class="downloadable_video">
                                                                                                <video controls preload="metadata" class="video-player">
                                                                                                    <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                                    Your browser does not support this video format.
                                                                                                </video>
                                                                                            </div>
                                                                                        </div>
                                                                                    </a>
                                                                                <?php else: ?>
                                                                                    <div class="custom_images">
                                                                                        <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                                    </div>
                                                                                <?php endif; ?>
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>

                                                        <?php endif; ?>
                                                        <div class="mt_20">
                                                            <a href="<?php echo e(url('projects_pending')); ?>/<?php echo e($job->id); ?>" class="btn btn_black">View <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <p class="text-danger">There is no jobs against this user</p>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="tab-pane" id="closed_projects">
                                    <div class="row custom_rowGap">
                                        <?php if(isset($user->getSellerJobs)): ?>
                                            <?php $__empty_1 = true; $__currentLoopData = $user->getSellerJobs->where('status','completed'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <div class="col-md-12 document_wrapper">
                                                    <div class="open_projects document_details">
                                                        <div class="date_posted">
                                                            <label>Posted On:<span><?php echo e($job->created_at->format('F d Y')); ?> </span></label>
                                                        </div>
                                                        <div class="custom_categories">
                                                            <a href="#!" class=""><?php echo e($job->category->name ?? '----'); ?> / <?php echo e($job->subCategory->name ?? '----'); ?></a>
                                                        </div>
                                                        <div class="mt_20">
                                                            <h5>Budget:</h5>
                                                            <h3>$<?php echo e($job->project_budget_min??''); ?> - $<?php echo e($job->project_budget_max??''); ?></h3>
                                                        </div>
                                                        <div class="custom_user_project_img">
                                                            <h5>Photos</h5>
                                                            <div class="custom_projects_images">
                                                                <?php if(!empty($job->jobFiles)): ?>
                                                                    <?php $__currentLoopData = $job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php
                                                                            $fileUrl = $file->file ?? '---';
                                                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                        ?>

                                                                        <?php if($isImage): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid">
                                                                                </div>
                                                                            </a>
                                                                        <?php elseif($isVideo): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <video controls preload="metadata" class="img-fluid video-player">
                                                                                        <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                        Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                                                    </video>
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <div class="project_photos">
                                                                                <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="further_details">
                                                            <?php if(!empty($job->jobQuestionAnswer)): ?>
                                                                <?php $__currentLoopData = $job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <div class="custom_budget_detail">
                                                                        <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                                                        <?php
                                                                            $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                                                        ?>
                                                                        <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $value)): ?>
                                                                            <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="">
                                                                                <div class="custom_images">
                                                                                    <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="max-width: 100%;">
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php if(isset($job->jobAssignStaffMeasurements) && !empty($job->jobAssignStaffMeasurements)): ?>
                                                            <div class="project_detail">
                                                                <div class="custom_flex">
                                                                    <h3>Further Details</h3>
                                                                </div>
                                                                <div class="project_scope">
                                                                    <h5>Measurements : </h5>
                                                                    <?php $__currentLoopData = $job->jobAssignStaffMeasurements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $measurement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <h6>Name:<?php echo e($measurement->name??'-'); ?>, Number:<?php echo e($measurement->number??'-'); ?>, Unit:<?php echo e($measurement->unit??'-'); ?></h6>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <h5>Specifications</h5>
                                                                    <h6><?php echo $job->staff_specifications ?? '----'; ?></h6>
                                                                </div>
                                                            </div>

                                                            <?php if(isset($job->jobAssignStaffDocuments) && !empty($job->jobAssignStaffDocuments)): ?>
                                                                <div class="col-md-12">
                                                                    <div class="project_detail">
                                                                        <div class="">
                                                                            <h5> Media</h5>
                                                                        </div>
                                                                        <div class="media_download">
                                                                            <?php $__currentLoopData = $job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php
                                                                                    $fileUrl = $file->image ?? '---';
                                                                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                                ?>
                                                                                <?php if($isImage): ?>
                                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                        <div class="custom_images">
                                                                                            <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="">
                                                                                        </div>
                                                                                    </a>
                                                                                <?php elseif($isVideo): ?>
                                                                                    <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                        <div class="custom_images staff_video">
                                                                                            <div class="downloadable_video">
                                                                                                <video controls preload="metadata" class="video-player">
                                                                                                    <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                                    Your browser does not support this video format.
                                                                                                </video>
                                                                                            </div>
                                                                                        </div>
                                                                                    </a>
                                                                                <?php else: ?>
                                                                                    <div class="custom_images">
                                                                                        <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                                    </div>
                                                                                <?php endif; ?>
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>

                                                        <?php endif; ?>
                                                        <div class="mt_20">
                                                            <a href="<?php echo e(url('projects_pending')); ?>/<?php echo e($job->id); ?>" class="btn btn_black">View <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <p class="text-danger">There is no jobs against this user</p>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="tab-pane" id="dispute_projects" >
                                    <div class="row custom_rowGap">
                                        <?php
                                            $processedJobIds = []; // Array to track already processed job_ids
                                        ?>

                                        <?php $__currentLoopData = $user->getSellerJobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $disputed): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php $__currentLoopData = $disputed->disputedJobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if(in_array($job->job_id, $processedJobIds)): ?>
                                                    <?php continue; ?>
                                                <?php endif; ?>
                                                <?php
                                                    $processedJobIds[] = $job->job_id; // Add job_id to the processed list
                                                ?>
                                                <div class="col-md-12 document_wrapper">
                                                    <div class="open_projects document_details">
                                                        <div class="date_posted">
                                                            <label>Posted On:<span><?php echo e($job->job->created_at->format('F d Y')); ?> </span></label>
                                                        </div>
                                                        <div class="custom_categories">
                                                            <a href="#!" class=""><?php echo e($job->job->category->name ?? '----'); ?> / <?php echo e($job->job->subCategory->name ?? '----'); ?></a>
                                                        </div>
                                                        <div class="mt_20">
                                                            <h5>Budget:</h5>
                                                            <h3>$<?php echo e($job->job->project_budget_min??''); ?> - $<?php echo e($job->job->project_budget_max??''); ?></h3>
                                                        </div>
                                                        <div class="custom_user_project_img">
                                                            <h5>Photos</h5>
                                                            <div class="custom_projects_images">
                                                                <?php if(!empty($job->job->jobFiles)): ?>
                                                                    <?php $__currentLoopData = $job->job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php
                                                                            $fileUrl = $file->file ?? '---';
                                                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                                        ?>

                                                                        <?php if($isImage): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid">
                                                                                </div>
                                                                            </a>
                                                                        <?php elseif($isVideo): ?>
                                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                                <div class="project_photos">
                                                                                    <video controls preload="metadata" class="img-fluid video-player">
                                                                                        <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                                        Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                                                    </video>
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <div class="project_photos">
                                                                                <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="further_details">
                                                            <?php if(!empty($job->job->jobQuestionAnswer)): ?>
                                                                <?php $__currentLoopData = $job->job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <div class="custom_budget_detail">
                                                                        <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                                                        <?php
                                                                            $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                                                        ?>
                                                                        <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $value)): ?>
                                                                            <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="">
                                                                                <div class="custom_images">
                                                                                    <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="max-width: 100%;">
                                                                                </div>
                                                                            </a>
                                                                        <?php else: ?>
                                                                            <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php if(isset($job->job->jobAssignStaffMeasurements) && !empty($job->job->jobAssignStaffMeasurements)): ?>
                                                            <div class="project_detail">
                                                                <div class="custom_flex">
                                                                    <h3>Further Details</h3>
                                                                </div>
                                                                <div class="project_scope">
                                                                    <h5>Measurements : </h5>
                                                                    <?php $__currentLoopData = $job->job->jobAssignStaffMeasurements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $measurement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <h6>Name:<?php echo e($measurement->name??'-'); ?>, Number:<?php echo e($measurement->number??'-'); ?>, Unit:<?php echo e($measurement->unit??'-'); ?></h6>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <h5>Specifications</h5>
                                                                    <h6><?php echo $job->job->staff_specifications ?? '----'; ?></h6>
                                                                </div>
                                                            </div>

                                                            <?php if(isset($job->jobAssignStaffDocuments) && !empty($job->job->jobAssignStaffDocuments)): ?>
                                                                <div class="project_media">
                                                                    <div class="custom_flex">
                                                                        <h5>Media</h5>
                                                                    </div>
                                                                    <div class="media_download">
                                                                        <?php $__currentLoopData = $job->job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $documents): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <div class="images_with_document">
                                                                                <a href="<?php echo e(asset('website')); ?>/<?php echo e($documents->image??'--'); ?>" data-fancybox="">
                                                                                    <div class="custom_images">
                                                                                        <img src="<?php echo e(asset('website')); ?>/<?php echo e($documents->image??'--'); ?>">
                                                                                    </div>
                                                                                </a>
                                                                                <span><?php echo e($documents->name??'---'); ?></span>
                                                                            </div>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                        <div class="mt_20">
                                                            <a href="<?php echo e(url('projects_pending')); ?>/<?php echo e($job->job->id); ?>" class="btn btn_black">View <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                                                        </div>
                                                    </div>
                                                </div>

                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
    <script>
        $(document).ready(function() {
            $('[data-fancybox="gallery"]').fancybox({
                protect: false,
                clickOutside: false,
                closeExisting: false,
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            $(document).on('click', '.user_perform_action', function () {
                const userId = $(this).attr('user-id');
                const action = $(this).data('action');
                Swal.fire({
                    title: `Are you sure?`,
                    text: `Do you want to ${action} this user?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes',
                    cancelButtonText: 'No'
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire({
                            title: `Type '${action}' to confirm`,
                            input: 'text',
                            inputPlaceholder: `Type '${action}' here...`,
                            showCancelButton: true,
                            confirmButtonText: 'Confirm',
                            cancelButtonText: 'Cancel',
                            preConfirm: (inputValue) => {
                                if (inputValue.toLowerCase() !== action.toLowerCase()) {
                                    Swal.showValidationMessage(`You must type '${action}' to confirm.`);
                                    return false;
                                }
                                return true;
                            }
                        }).then((inputResult) => {
                            if (inputResult.isConfirmed) {
                                // Proceed with AJAX request
                                $.ajax({
                                    url: '<?php echo e(url('user_suspend_or_ban')); ?>/' + userId + '/' + action, //Replace with your endpoint URL
                                    success: function (response) {
                                        Swal.fire('Success!', `User has been ${action}ed successfully!`, 'success');
                                        if (response.action === 'ban') {
                                            window.location.href = '/users'; // Replace with your users page URL
                                        } else {
                                            location.reload(); // Reload the page to reflect changes
                                        }//ends if...
                                        // Optionally, you can reload the page or update the UI here
                                    },
                                    error: function (xhr, status, error) {
                                        Swal.fire('Error!', `An error occurred: ${xhr.responseText || error}`, 'error');
                                    }
                                });
                            } else {
                                Swal.fire('Canceled', 'Action was not performed.', 'info');
                            }
                        });
                    } else {
                        Swal.fire('Canceled', 'Action was not performed.', 'info');
                    }
                });
            });

            $(".custom_search .custom_search_box").on("keyup", function () {
                var searchValue = $(this).val().toLowerCase();

                $(".document_details").each(function () {
                    var titleText = $(this).find(".custom_categories a").text().toLowerCase();

                    if (titleText.includes(searchValue)) {
                        $(this).closest('.document_wrapper').show();
                    } else {
                        $(this).closest('.document_wrapper').hide();
                    }
                });
            });
            $(".custom_search .custom_search_box").on("input", function () {
                if ($(this).val() === "") {
                    $(".document_wrapper").show();
                }
            });

            //        Filter Functionality
            $(".filter_btn input[type='date']").on("change", function () {
                let selectedDate = $(this).val();
                if (!selectedDate) {
                    $(".document_wrapper").show();
                    return;
                }

                $(".document_details").each(function () {
                    let submittedDate = $(this).find(".date_posted label span").text().trim();
                    let formattedDate = new Date(submittedDate).toISOString().split("T")[0];

                    if (formattedDate === selectedDate) {
                        $(this).closest('.document_wrapper').show();
                    } else {
                        $(this).closest('.document_wrapper').hide();
                    }
                });
            });
        });

    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/admin/user_service_provider.blade.php ENDPATH**/ ?>