<?php $__env->startPush('css'); ?>


<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">Dispute Management</h2>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <section class="projects_wrapper pagination_scroll_tbl">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_projects_tabs scrollable_tbl">
                        <div class="custom_search_filter custom_flex">
                            <div class="txt_field custom_search">
                                <input type="text" placeholder="Search" class="custom_search_box">
                            </div>
                            <div class="filter_btn">
                                <form class="fliters_form_wrapper" id="">
                                    <div class="dropdown">
                                        <button type="button" class="btn btn_black dropdown-toggle" id="filterDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                            Filter<img src="<?php echo e(asset('website')); ?>/assets/images/filter.png"></button>
                                        <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                                            <li>
                                                <h5 class="filter_title">Dispute Date:</h5>
                                            </li>
                                            <li>
                                                <div class="txt_field">
                                                    <input type="date" class="form-control" id="" placeholder="DD/MM/YYYY">
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <div class="custom_table">
                                <table id="" class="table table-striped myTable datatable">
                                    <thead>
                                    <tr>
                                        <th>Client</th>
                                        <th>Dispute Title</th>
                                        <th>Service Provider</th>
                                        <th>Date Opened</th>
                                        <th>Assigned Staff</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $disputes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><a href="<?php echo e(url('admin_dispute_chat')); ?>/<?php echo e($element->job->id); ?>"><?php echo e($element->job->user->name); ?></a></td>
                                            <td><?php echo e($element->dispute_title??'--'); ?></td>
                                            <td><?php echo e($element->job->jobOffersPaid->getStaffDetail->name??''); ?></td>
                                            <td><?php echo e(date('d-m-Y', strtotime($element->created_at))); ?></td>
                                            <td><?php echo e(isset($element->job->jobAssignStaff) ?  $element->job->jobAssignStaff->name : '--'); ?></td>
                                            <td><span class="<?php echo e($element->status == '0' ? 'secondary'  : 'primary'); ?>"><?php if($element->status == '0'): ?> Pending <?php else: ?> Resolved <?php endif; ?></span></td>
                                            <td><div class="dropdown">
                                                    <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                        <li><a class="dropdown-item" href="<?php echo e(url('admin_dispute_chat')); ?>/<?php echo e($element->job->id); ?>"><i class="fa-solid fa-eye"></i>View</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script>
    //        Filter Functionality
    $(document).ready(function () {
        $(".filter_btn input[type='date']").on("change", function () {
            let selectedDate = $(this).val();
            if (!selectedDate) {
                $(".myTable tbody tr").show();
                return;
            }

            $(".myTable tbody tr").each(function () {
                let submittedDate = $(this).find("td:nth-child(4)").text().trim();
                if (submittedDate === "---") {
                    $(this).hide();
                    return;
                }

                let dateParts = submittedDate.split(" | ")[0].split("-");
                let formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

                $(this).toggle(formattedDate === selectedDate);
            });
        });
    });
</script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/admin/admin_dispute_management.blade.php ENDPATH**/ ?>