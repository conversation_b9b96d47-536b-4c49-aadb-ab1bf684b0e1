<?php $__env->startPush("css"); ?>
    <style>
        .error{
            color:red !important;
            font-size:12px !important;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="project_overview">
        <div class="container">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="services_offer">
                        <form class="row custom_row" id="make_your_offer_form" action="<?php echo e(route('estimate_job_offer')); ?>" method="post">
                            <?php echo csrf_field(); ?>
                            <div class="col-md-12">
                                <input type="hidden" name="job_id" value="<?php echo e($job->id??0); ?>">
                                <div class="make_offer">
                                    <h3>Make your Estimate Offer</h3>
                                    <div class="row custom_row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <input type="checkbox" id="one" name="hide_bid_amount" value="yes" <?php if(isset($offer) && $offer->hide_bid_amount == 'yes'): ?> checked <?php endif; ?>>
                                                <label for="one">Hide bid amount from other Contractors</label>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label>Labor Expense <span>(Fixed) </span></label>
                                                <input type="number" id="labour_expense" max="99999" name="labour_expense" required  <?php if(isset($offer) && $offer->labour_expense): ?> value="<?php echo e($offer->labour_expense??''); ?>" <?php endif; ?> placeholder="0000" class="form-control project_budget_min">
                                                <i class="fa-solid fa-dollar-sign dollor_icon"></i>
                                                <i class="fa-solid fa-circle-info info_icon"></i>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="switch_button">
                                                <h6>Materials Cost</h6>
                                                <div class="custom_switch_toggle">
                                                    <span>Fixed</span>
                                                    <label class="switch">
                                                        <input type="checkbox" value="yes" name="range_or_fixed" <?php if(isset($offer) && $offer->range_or_fixed == 'yes'): ?> checked <?php endif; ?> >
                                                        <span class="slider" ></span>
                                                    </label>
                                                    <span id="switch_and_fixed_text">Range</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field fixed_amount">
                                                <label>Amount</label>
                                                <input type="number" name="amount" max="99999" id="amount" required <?php if(isset($offer) && $offer->amount): ?> value="<?php echo e($offer->amount??''); ?>" <?php endif; ?> min="0" placeholder="0000" class="form-control project_budget_min">
                                                <i class="fa-solid fa-dollar-sign dollor_icon"></i>
                                            </div>
                                            <div class="txt_field amount_range">
                                                <label>Minimum</label>
                                                <input type="number" name="min_amount" id="min_amount" required min="0" max="99999" placeholder="0000" class="form-control project_budget_min" <?php if(isset($offer) && $offer->min_amount): ?> value="<?php echo e($offer->min_amount??''); ?>" <?php endif; ?>>
                                                <i class="fa-solid fa-dollar-sign dollor_icon"></i>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="txt_field amount_range">
                                                <label>Maximum</label>
                                                <input type="number" placeholder="0000" required name="max_amount" max="99999" id="max_amount" <?php if(isset($offer) && $offer->max_amount): ?> value="<?php echo e($offer->max_amount??''); ?>" <?php endif; ?> min="0" class="form-control project_budget_min">
                                                <i class="fa-solid fa-dollar-sign dollor_icon"></i>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label>Full description of work</label>
                                                <textarea type="text" placeholder="Please enter the full description of work here. Keep in mind, this is the binding argreement between contractor and the customer so include all the details regarding materials used and qunatity ,etc." required name="description" rows="4" class="form-control"><?php if(isset($offer) && $offer->comment): ?><?php echo e($offer->comment??'Please enter the full description of work here. Keep in mind, this is the binding argreement between contractor and the customer so include all the details regarding materials used and qunatity ,etc.'); ?><?php endif; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="update_btn_div">
                                    <div class="custom_submit_btn">

                                        <button type="button" onclick="window.location.href='<?php echo e(route('explore_view_project')); ?>/<?php echo e($job->id??0); ?>'" class="btn btn_transparent">Cancel</button>
                                        <button type="submit" class="btn btn_black btn_has_icon" id="form_submit_btn" >Submit Estimate <div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function () {
            $('#make_your_offer_form').on('submit', function () {
                var submitBtn = $('#form_submit_btn');
                submitBtn.prop('disabled', true);
                submitBtn.html('Processing... <div class="btn_icon"><i class="fa-solid fa-spinner fa-spin"></i></div>');
            });
        });
    </script>
<script>

    $(document).ready(function () {
        var fixed_checked = document.getElementById('switch_and_fixed_text');

        // Initialize based on existing data
        <?php if(isset($offer) && $offer->range_or_fixed == 'yes'): ?>
        // Range is selected
        $('.txt_field.amount_range').show();
        $('#max_amount').attr('required', true);
        $('#min_amount').attr('required', true);
        $('#amount').attr('required', false);
        $('.txt_field.fixed_amount').hide();
        // fixed_checked.innerHTML = "Fixed";

        // Clear fixed amount when range is selected
        $('#amount').val('');
        <?php else: ?>
        // Fixed is selected (default)
        $('.txt_field.fixed_amount').show();
        $('#max_amount').attr('required', false);
        $('#min_amount').attr('required', false);
        $('#amount').attr('required', true);
        $('.txt_field.amount_range').hide();
        fixed_checked.innerHTML = "Range";

        // Clear range amounts when fixed is selected
        $('#min_amount').val('');
        $('#max_amount').val('');
        <?php endif; ?>

        // Handle toggle change
        $(document).on("change", ".switch_button .switch input[type=checkbox]", function() {
            if ($(this).is(":checked")) {
                // Checkbox is checked = Range mode
                fixed_checked.innerHTML = "Range";
                $('.txt_field.fixed_amount').hide();
                $('.txt_field.amount_range').show();

                // Update required attributes
                $('#amount').attr('required', false);
                $('#max_amount').attr('required', true);
                $('#min_amount').attr('required', true);

                // Clear fixed amount value
                $('#amount').val('');

            } else {
                // Checkbox is unchecked = Fixed mode
                // fixed_checked.innerHTML = "Fixed";
                $('.txt_field.amount_range').hide();
                $('.txt_field.fixed_amount').show();

                // Update required attributes
                $('#max_amount').attr('required', false);
                $('#min_amount').attr('required', false);
                $('#amount').attr('required', true);

                // Clear range amount values
                $('#min_amount').val('');
                $('#max_amount').val('');
            }
        });

        // Optional: Add validation for range inputs
        // $('#min_amount, #max_amount').on('blur', function() {
        //     var minAmount = parseFloat($('#min_amount').val()) || 0;
        //     var maxAmount = parseFloat($('#max_amount').val()) || 0;
        //
        //     if (minAmount > 0 && maxAmount > 0 && minAmount >= maxAmount) {
        //         alert('Maximum amount must be greater than minimum amount');
        //         $(this).focus();
        //     }
        // });
    });


    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    

    
    
    
    
    $(document).on("input", ".project_budget_min", function () {
        var value = $(this).val();
        // Allow only positive numbers and decimals
        value = value.replace(/[^0-9.]/g, '');
        if ((value.match(/\./g) || []).length > 1) {
            value = value.substring(0, value.length - 1);
        }//ends if....
        $(this).val(value);
    });
    $(document).on('click', '#form_submit_btn', function () {
        let isValid = true;
        // Validate all required inputs
        $('#make_your_offer_form [required]').each(function () {
            if ($(this).prop('required') && !$(this).val().trim()) {
                isValid = false;
                $(this).addClass('is-invalid'); // Add a class to highlight invalid fields
            } else {
                $(this).removeClass('is-invalid'); // Remove invalid class if field is valid
            }
        });

        // If form is valid, submit it
        if (isValid) {
            $('#make_your_offer_form').submit();
        } else {
            alert('Please fill in all required fields.');
        }
    });

</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/serviceProvider/estimate_bid_offer.blade.php ENDPATH**/ ?>