<?php $__env->startPush("css"); ?>
    <style>
        header,footer{display: none;}
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <section id="wrapper" class="login_register">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-7 col-md-6 custom_column_padding">
                    <div class="custom_banner">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                            <div class="banner_content custom_login_title">
                                <a href="<?php echo e(url('home')); ?>">
                                    <div class="site_logo">
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png">
                                    </div>
                                </a>
                                <div class="site_title">
                                    <h1>Tackle any home improvement project, effortlessly</h1>
                                </div>
                                <div class="site_key_parameter">
                                    <a href="javascript:void(0)"><i class="fa-regular fa-circle-check"></i>Free consultation</a>
                                    <a href="javascript:void(0)"><i class="fa-solid fa-award"></i>Satisfaction Guaranteed</a>
                                    <a href="javascript:void(0)"><img src="<?php echo e(asset('website')); ?>/assets/images/banner_sheild.png">Protected Payments</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5 col-md-6 custom_column_padding">
                    <div class="login_box reset_password">
                        <form class="form-horizontal form-material" method="post" action="<?php echo e(route('password.request')); ?>" id="reset_form">
                            <?php echo e(csrf_field()); ?>

                            <input type="hidden" name="token" value="<?php echo e($token); ?>">
                            <div class="authentication_text">
                                <h1><?php echo e(__('Reset Password')); ?></h1>
                                <h6>Set the new password for your account so you can login and access all the features.</h6>
                            </div>
                            <div class="txt_field">
                                <label for="" class="form-label">Email</label>
                                <input placeholder="Email" id="email" type="email"
                                       class="form-control<?php echo e($errors->has('email') ? ' is-invalid' : ''); ?>" name="email"
                                       value="<?php echo e($email ?: old('email')); ?>" required autofocus>

                                <?php if($errors->has('email')): ?>
                                    <span class="invalid-feedback">
                                    <strong><?php echo e($errors->first('email')); ?></strong>
                                </span>
                                <?php endif; ?>
                            </div>
                            <div class="txt_field">
                                <label for="" class="form-label">New Password</label>
                                <input id="password" placeholder="Password" type="password" class="form-control<?php echo e($errors->has('password') ? ' is-invalid' : ''); ?> password_eye"
                                       name="password" required>
                                <i class="fa-solid fa-eye custom_eye_icon"></i>
                                <i class="fa-solid fa-eye-slash custom_eye_icon"></i>
                                        <span id="passwordError" class="error-message" style="color: red; display: none;">Password must be 8-15 characters with at least 1 uppercase, 1 lowercase, 1 number, and 1 special character.</span>
                                <?php if($errors->has('password')): ?>
                                    <span class="invalid-feedback">
                                    <strong><?php echo e($errors->first('password')); ?></strong>
                                </span>
                                <?php endif; ?>
                            </div>
                            <div class="txt_field">
                                <label for="" class="form-label">Confirm Password</label>
                                <input placeholder="Confirm Password" id="password-confirm" type="password" class="form-control password_eye"
                                       name="password_confirmation" required>
                                <i class="fa-solid fa-eye custom_eye_icon"></i>
                                <i class="fa-solid fa-eye-slash custom_eye_icon"></i>
                            </div>
                            <div class="submit_btn">
                                <button class="btn btn_black"
                                        type="submit">Reset<div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>






                
                    
                        
                            
                        
                        
                            
                        
                        
                            
                            
                            
                        
                    
                






























                                        
                                        

















<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            $("#email").attr("readonly", true);
        });
        $(document).ready(function(){
            $.validator.addMethod("passwordRegex", function (value, element) {
                return this.optional(element) || /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$#!%*?&-])[A-Za-z\d@$#!%*?&-]{8,15}$/.test(value);
            }, "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.");
            $('#reset_form').validate({
                rules: {
                    password: {
                        required: true,
                        passwordRegex: true,
                        minlength: 8,
                        maxlength: 15
                    },
                    password_confirmation: {
                        required: true,
                        equalTo: "#password"
                    }
                },
                messages: {
                    password: {
                        required: "Please provide a password",
                        minlength: "Your password must be at least 8 characters long",
                        maxlength: "Your password must be at most 15 characters long"
                    },
                    password_confirmation: {
                        required: "Please provide a password",
                        equalTo: "Password does not match"
                    }
                }
            })
        })
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/auth/passwords/reset.blade.php ENDPATH**/ ?>