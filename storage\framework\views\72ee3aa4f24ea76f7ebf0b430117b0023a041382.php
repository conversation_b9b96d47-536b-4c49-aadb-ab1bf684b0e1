<?php $__env->startPush("css"); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="details_review_page">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="projects_completion">
                        <div class="personal_information custom_section">
                            <div class="custom_profile_info">
                                <div class="custom_profile_img">
                                    <div class="info_profile">
                                        <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($user->profile->pic??''); ?>" alt="">
                                    </div>
                                    <a href="#!" class="share_icon"><i class="fa-solid fa-share"></i></a>
                                </div>
                                <h4><?php echo e($user->first_name??''); ?> <?php echo e($user->last_name??''); ?></h4>
                                    <div class="rating_star">
                                    <div class="seller_rating">
                                        <span><i class="fa fa-star <?php if($user->ratingSum >= 1): ?> checked <?php endif; ?>"></i></span>
                                        <span><i class="fa fa-star <?php if($user->ratingSum >= 2): ?> checked <?php endif; ?>"></i></span>
                                        <span><i class="fa fa-star <?php if($user->ratingSum >= 3): ?> checked <?php endif; ?>"></i></span>
                                        <span><i class="fa fa-star <?php if($user->ratingSum >= 4): ?> checked <?php endif; ?>"></i></span>
                                        <span><i class="fa fa-star <?php if($user->ratingSum >= 5): ?> checked <?php endif; ?>"></i></span>
                                        <h6><?php echo e($user->ratingSum); ?> Stars</h6>
                                    </div>
                            </div>
                            </div>

                            <?php if(isset($role) && $role == 'buyer'): ?>
                            <div class="edit_profile_btn">
                                <a href="<?php echo e(url('edit_profile')); ?>" class="btn btn_black btn_has_icon">Edit Profile <div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></a>
                            </div>
                            <?php endif; ?>

                            <div class="contact_info">
                                <h5 >Personal Information</h5>
                                <div class="dflex">
                                    <div class="social_icons phone"><i class="fa-solid fa-phone"></i><h6 class="fw_bold">Phone:</h6></div>
                                    <div><h6> <?php echo e($user->profile->phone??''); ?></h6></div>
                                </div>
                                <div class="dflex">
                                    <div class="social_icons email_icon"><i class="fa-solid fa-envelope"></i><h6 class="fw_bold">Email:</h6></div>
                                    <div><h6><?php echo e($user->email??''); ?></h6></div>
                                </div>
                            </div>
                        </div>
                        <div class="custom_card">
                            <h3>Projects Completed</h3>
                            <h3><?php echo e($user?->jobCompleted->count() ?? 0); ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="reviews_wrapper custom_section">
                        <h5 class="title">Reviews</h5>
                        <?php $__currentLoopData = $user->ratingReview; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ratingReview): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="single_review">
                            <div class="review_user">
                                <div class="review_user_img">
                                        <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($ratingReview->senderBy->profile->pic??''); ?>" alt="">
                                </div>
                                <div class="client_review">
                                        <h5><?php echo e($ratingReview->senderBy->name??''); ?></h5>
                                        <?php
                                            $stars = $ratingReview->stars ?? 0; // Get the star rating, default to 0 if not set
                                        ?>
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <span class="fa fa-star <?php echo e($i <= $stars ? 'checked' : ''); ?>"></span>
                                    <?php endfor; ?>
                                </div>
                            </div>
                                <h5><?php echo e($ratingReview->review??'---'); ?></h5>
                            <div class="review_date_main">
                                    <span class="review_date"><h6><?php echo e($ratingReview->created_at??'---'); ?></h6></span>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/buyer/my_profile.blade.php ENDPATH**/ ?>