<?php $__env->startPush('css'); ?>


<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">Chat Management</h2>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <section class="projects_wrapper pagination_scroll_tbl">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_projects_tabs scrollable_tbl">
                        <div class="custom_search_filter custom_flex">
                            <div class="txt_field custom_search">
                                <input type="text" placeholder="Search" class="custom_search_box">
                            </div>
                            
                                
                                    
                            
                        </div>
                        <div class="table-responsive">
                            <div class="custom_table">
                                <table id="" class="table table-striped myTable datatable">
                                    <thead>
                                    <tr>
                                        <th>sender / receiver</th>
                                        <th>Job</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $chats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <?php echo e($chat->getSender->name ?? '--'); ?> / <?php echo e($chat->getReceiver->name ?? '--'); ?>

                                            </td>
                                            <td><?php echo e($chat->getItem->project_number ?? '--'); ?> / <?php echo e($chat->getItem->project_title ?? '--'); ?> </td>
                                            <td><div class="dropdown">
                                                    <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                        <li><a class="dropdown-item" href="<?php echo e(url('buyer_chat')); ?>/<?php echo e($chat->id); ?>"><i class="fa-solid fa-eye"></i>View</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>


<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/admin/admin_chat_management.blade.php ENDPATH**/ ?>