<?php $__env->startPush("css"); ?>
<style>
    .error{
        color:red !important;
        font-size:12px !important;
    }
</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="project_overview">
        <div class="container">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="services_offer">
                        <form class="row custom_row" id="make_your_offer_form" action="<?php echo e(route('store_job_offer')); ?>" method="post">
                            <?php echo csrf_field(); ?>
                            <div class="col-md-12">
                                <input type="hidden" name="job_id" value="<?php echo e($job->id??''); ?>">
                                <div class="make_offer">
                                    <h3>Make your Offer</h3>
                                    <div class="row custom_row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <input type="checkbox" id="one" name="hide_bid_amount" value="yes" <?php if(isset($offer) && $offer->hide_bid_amount == 'yes'): ?> checked <?php endif; ?>>
                                                <label for="one">Hide bid amount from other Contractors</label>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label>Labor Expense <span>(Fixed) </span></label>

                                                <input type="number" id="labour_expense" max="99999" name="labour_expense" required  <?php if(isset($offer) && $offer->labour_expense): ?>  value="<?php echo e(old('labour_expense', $offer->labour_expense ?? '')); ?>" <?php endif; ?> placeholder="0000" class="form-control project_budget_min">
                                                <i class="fa-solid fa-dollar-sign dollor_icon"></i>
                                                <i class="fa-solid fa-circle-info info_icon"></i>
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label>Materials Expense <span>(Fixed) </span></label>
                                                <input type="number" id="material_expense" max="99999" name="material_expense" required  <?php if(isset($offer) && $offer->material_expense): ?> value="<?php echo e($offer->material_expense??''); ?>" <?php endif; ?> placeholder="0000" class="form-control project_budget_min">
                                                <i class="fa-solid fa-dollar-sign dollor_icon"></i>
                                                <i class="fa-solid fa-circle-info info_icon"></i>
                                            </div>
                                        </div>


                                        <div class="col-md-12">
                                            <div class="txt_field">
                                                <label>Full description of work </label>
                                                <textarea type="text" placeholder="Please enter the full description of work here. Keep in mind, this is the binding argreement between contractor and the customer so include all the details regarding materials used and qunatity ,etc." required name="description" rows="4" class="form-control"><?php if(isset($offer) && $offer->comment): ?><?php echo e($offer->comment ??'Please enter the full description of work here. Keep in mind, this is the binding argreement between contractor and the customer so include all the details regarding materials used and qunatity ,etc.'); ?><?php endif; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="services_offer">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="project_ongoing">
                                    <div class="custom_timeline">
                                        <h3>Milestone</h3>
                                        <div class="custom_btn">
                                            <div class="create_btn">
                                                <a href="#!" class="btn btn_black" id="create_milestone_button">Create Milestone<span><i class="fa-solid fa-arrow-right"></i> </span></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table myTable datatable" id="milestoneTable">
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                            <div class="col-md-12 update_btn_div">
                                <?php if($milestones->count() > 0): ?>
                                    <div class="custom_submit_btn">

                                        <button type="button" onclick="window.location.href='<?php echo e(route('explore_view_project')); ?>/<?php echo e($job->id??0); ?>'" class="btn btn_transparent">Cancel</button>
                                        <button type="submit" class="btn btn_black btn_has_icon" id="form_submit_btn" >Submit Bid <div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></button>
                                    </div>
                                <?php endif; ?>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <div class="modal fade create_milestone" id="create_milestone" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Create Milestone</h3>
                </div>
                <div class="modal-body">

                    <form id="create_milestone_from" method="post" novalidate>
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <input type="hidden" name="job_id" value="<?php echo e($job->id??''); ?>" required>
                            <div class="col-md-12">
                                <div class="new_milestone">
                                    <span id="put_iteration"></span>
                                    <h6>Milestone Details</h6>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Title/Description</label>
                                    <input type="text" name="title" id="create_title_milestone" placeholder="Title Here" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Milestone Amount</label>
                                    <input type="number" name="milestone_amount" id="create_amount_milestone" max="99999" placeholder="0000" class="form-control project_budget_min" required>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Expected Release Date</label>
                                    <input type="date" name="date" placeholder="DD/MM/YYYY" id="create_date_milestone" class="form-control" required>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" id="submit_milestone_button" class="btn btn_black milestoneBtn">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade create_milestone" id="edit_milestone" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>

    <script>

        $(document).ready(function () {
            var form = $('#make_your_offer_form');

            // Restore saved values
            ['labour_expense', 'material_expense', 'description', 'hide_bid_amount'].forEach(function (name) {
                var field = form.find('[name="' + name + '"]');
                var savedValue = localStorage.getItem(name);

                if (field.length && savedValue !== null) {
                    if (field.attr('type') === 'checkbox') {
                        field.prop('checked', savedValue === 'yes');
                    } else {
                        field.val(savedValue);
                    }
                }
            });

            // Save values on input/change
            form.find('input, textarea').on('input change', function () {
                var field = $(this);
                var name = field.attr('name');

                if (field.attr('type') === 'checkbox') {
                    localStorage.setItem(name, field.is(':checked') ? 'yes' : '');
                } else {
                    localStorage.setItem(name, field.val());
                }
            });
            form.on('submit', function () {
                ['labour_expense', 'material_expense', 'description', 'hide_bid_amount'].forEach(function (name) {
                    localStorage.removeItem(name);
                });
            });
        });


        // $(document).ready(function() {
        //     $('#amount, #min_amount, #max_amount, #labour_expense, #create_amount_milestone').on('input', function() {
        //         var value = $(this).val();
        //         if (value.indexOf('.') !== -1) {
        //             $(this).val(value.substring(0, value.indexOf('.')));
        //         }
        //         if (parseFloat(value) > 99999) {
        //             $(this).val('99999');
        //         }
        //         if (value.startsWith('0') && value.length > 1) {
        //             $(this).val(value.slice(1));
        //         }
        //         if (parseFloat(value) <= 0 && value !== "") {
        //             $(this).val('');
        //         }
        //         if (isNaN(value) && value !== "") {
        //             $(this).val('');
        //         }
        //     });
        // });




        $(document).ready(function(){
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('create_date_milestone').setAttribute('min', today);
    })
        $(document).ready(function () {
            var fixed_checked = document.getElementById('switch_and_fixed_text');
            <?php if(isset($offer) && $offer->range_or_fixed == 'yes'): ?>
                $('.txt_field.fixed_amount').show();
                $('#max_amount').attr('required', false);
                $('#min_amount').attr('required', false);
                $('#amount').attr('required', true);
                $('.txt_field.amount_range').hide(); // Hide the field if unchecked
            <?php else: ?>
                $('.txt_field.amount_range').show(); // Hide the field if unchecked
                $('#max_amount').attr('required', true);
                $('#min_amount').attr('required', true);
                $('#amount').attr('required', false);
                $('.txt_field.fixed_amount').hide();
            <?php endif; ?>
            $(document).on("change", ".switch_button .switch input[type=checkbox]", function() {
                if ($(this).is(":checked")) {
                    fixed_checked.innerHTML="Fixed"
                    $('.txt_field.amount_range').hide();
                    $('#max_amount').attr('required', false);
                    $('#min_amount').attr('required', false);
                    $('#amount').attr('required', true);
                    $('.txt_field.fixed_amount').show(); // Show the field if checked
                } else {
                    fixed_checked.innerHTML="Fixed"
                    $('.txt_field.fixed_amount').hide();
                    $('#max_amount').attr('required', true);
                    $('#min_amount').attr('required', true);
                    $('#amount').attr('required', false);

                    $('.txt_field.amount_range').show(); // Hide the field if unchecked
                }
            });
        });

        $(document).on('click', '#form_submit_btn', function () {
            let isValid = true;
            let $btn = $(this);
            $btn.prop('disabled', true).html('Submitting.. <span><i class="fa-solid fa-spinner fa-spin"></i></span>');
            // Validate all required inputs
            $('#make_your_offer_form [required]').each(function () {
                if ($(this).prop('required') && !$(this).val().trim()) {
                    isValid = false;
                    $(this).addClass('is-invalid'); // Add a class to highlight invalid fields
                } else {
                    $(this).removeClass('is-invalid'); // Remove invalid class if field is valid
                }
            });

            // If form is valid, submit it
            if (isValid) {

                $('#make_your_offer_form').submit();
            } else {
                alert('Please fill in all required fields.');
            }
        });

        $(document).on('submit', '#create_milestone_from', function (event) {
            event.preventDefault(); // Prevent default form submission
            let isValid = true;
            // let $btn = $(this);
            let buttonName = 'Submit';
            let $btn = $('#submit_milestone_button');
            $btn.prop('disabled', true).html('Submitting.. <span><i class="fa-solid fa-spinner fa-spin"></i></span>');
            var submitButton = document.getElementById('submit_milestone_button');
            submitButton.disabled = true;

            $('#create_milestone_from [required]').each(function () {
                if (!$(this).val()) {
                    isValid = false;
                    $btn .prop('disabled', false).html(buttonName + ' <span><i class="fa-solid fa-arrow-right"></i></span>');
                    $(this).addClass('is-invalid'); // Add a class to highlight invalid fields (optional)
                } else {

                    $(this).removeClass('is-invalid'); // Remove invalid class if field is valid
                }
            });

            if (!isValid) {
                alert('Please fill in all required fields.');
                return;
            }

            var job_id = "<?php echo e($job->id ?? 0); ?>";
            $.ajax({
                url: "<?php echo e(route('create_milestone')); ?>",
                method: "POST",
                data: $('#create_milestone_from').serialize(),
                success: function(response) {
                    submitButton.disabled = false;
                    $btn.prop('disabled', false).html(buttonName + ' <span><i class="fa-solid fa-arrow-right"></i></span>');
                    $('#create_milestone').modal('hide');

                    $('#create_date_milestone').val("");
                    $('#create_title_milestone').val("");
                    $('#create_amount_milestone').val("");


                    milestoneTable(job_id);
                    $('.update_btn_div').load(' .update_btn_div > *');

                },
                error: function(xhr, status, error) {
                    $btn.prop('disabled', false);
                    Swal.fire('Error', 'Something went wrong. Please try again.', 'error');
                    // console.error("Error:", error);
                    // alert('An error occurred while creating the milestone.');
                }
            });
        });

        $(document).on('click', '.edit_milestone', function () {
            var id = $(this).attr('data-id');
            $.ajax({
                url: "<?php echo e(url('edit_milestone')); ?>/" + id,
                success: function (data) {
                    $('#edit_milestone').html(data);
                    $('#edit_milestone').modal('show');
                }
            });
        });

        $(document).ready(function() {
            
            
            
            
            
            
            

            
            
            
            var job_id = "<?php echo e($job->id ?? 0); ?>";
            milestoneTable(job_id)
        });

        function milestoneTable(job_id) {
            $.ajax({
                url: "<?php echo e(route('milestone_table')); ?>/" + job_id, // Correct the URL concatenation
                method: 'GET',
                data: { job_id: job_id }, // Pass the job_id dynamically
                success: function (data) {
                    $('#milestoneTable').html(data); // Update the table content
                },
            });
        }//ends

        $(document).on('click', '.delete_milestone', function () {
            var id = $(this).attr('data-id');
            Swal.fire({
                title: 'Are you sure?',
                text: "Do you want to delete this milestone?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes',
                cancelButtonText: 'No'
            }).then((result) => {
                if (result.isConfirmed) {
                    // User confirmed, proceed with deletion
                    $.ajax({
                        url: "<?php echo e(url('delete_milestone')); ?>/" + id,
                        method: 'GET', // Ensure this matches the backend route's method
                        success: function (data) {
                            Swal.fire(
                                'Deleted!',
                                'The milestone has been deleted.',
                                'success'
                            );
                            location.reload();
                        }
                    });
                }
            });
        });
        $(document).on('click', '#create_milestone_button', function() {
            $('#create_milestone').modal('show');
            var elements = document.getElementsByClassName('get_last_iteration');
            var put_iteration = document.getElementById('put_iteration');

            var lastIteration = elements.length;

            put_iteration.innerHTML = lastIteration + 1;
        });
        $(document).ready(function () {
            $(document).on('submit', '#updateMilestoneForm', function (e) {
                e.preventDefault();
                var job_id = "<?php echo e($job->id ?? 0); ?>";
                var formData = $(this).serialize();
                formData += '&_token=' + '<?php echo e(csrf_token()); ?>';  // Adding CSRF token

                $.ajax({
                    url: "<?php echo e(route('update_milestone')); ?>",
                    type: 'POST',
                    data: formData,
                    success: function (data) {
                        if (data.type === 'success') {
                            Swal.fire({
                                title: 'Done!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    milestoneTable(job_id);
                                    $('#edit_milestone').modal('hide');

                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Error updating milestone: ' + error,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            });
        });
        $(document).on("input", ".project_budget_min", function () {
            var value = $(this).val();
            // Allow only positive numbers and decimals
            value = value.replace(/[^0-9.]/g, '');
            if ((value.match(/\./g) || []).length > 1) {
                value = value.substring(0, value.length - 1);
            }//ends if....
            $(this).val(value);
        });


    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/serviceProvider/services_bid_offer.blade.php ENDPATH**/ ?>