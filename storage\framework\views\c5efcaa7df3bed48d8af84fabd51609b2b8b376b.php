<?php $__env->startPush("css"); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <section class="hero_section">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner home_page_banner">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <?php if(!empty($home->section_one_header_image)): ?>

                                <source src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_one_header_image); ?>" type="video/mp4">
                            <?php else: ?>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                            <?php endif; ?>
                        </video>
                        <div class="background_banner">
                            <div class="banner_content">
                                <h1><?php echo e($home->section_one_heading??''); ?></h1>
                                <h6><?php echo e($home->section_one_description??''); ?></h6>
                                <div class="banner_btn">
                                    <?php if(Auth::check() && auth()->user()->hasRole('seller')): ?>
                                        <a href="<?php echo e(url('seller_home')); ?>" class="btn btn_pink">Post a Project
                                            <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                        </a>
                                    <?php elseif(Auth::check() && auth()->user()->hasRole('buyer')): ?>
                                        <a href="<?php echo e(url('buyer_home')); ?>" class="btn btn_pink">Post a Project
                                            <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                        </a>
                                    <?php else: ?>

                                        <a href="<?php echo e(url('registration_role')); ?>" class="btn btn_pink">Post a Project
                                        <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                    </a>
                                    <?php endif; ?>
                                    <a class="explain_process" href="#explain_process"><p>How does it work?</p></a>
                                </div>
                                <div class="site_key_parameter">
                                    <a href="javascript:void(0)"><i class="fa-regular fa-circle-check"></i>Free consultation
                                        <div class="tooltip_hover">
                                            <span class="hover_txt">Find out how much your project is going to cost from multiple contractors, easily</span>
                                        </div>
                                    </a>
                                    <a href="javascript:void(0)"><i class="fa-solid fa-award"></i>Satisfaction Guaranteed
                                        <div class="tooltip_hover">
                                            <span class="hover_txt">Contractors are vetted, and payments will only be released for satisfactory work as it is completed</span>
                                        </div>
                                    </a>
                                    <a href="javascript:void(0)"><img src="<?php echo e(asset('website')); ?>/assets/images/banner_sheild.png">Protected Payments
                                        <div class="tooltip_hover">
                                            <span class="hover_txt">Create milestones to release as the job is completed. No payment is released to the contractor until the job is completed</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="homepage_role">
                            <a href="<?php echo e(url('register?role_id=contractor')); ?>" class="btn btn_transparent">Are you a contractor<img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                            <a href="<?php echo e(url('register?role_id=client')); ?>" class="btn btn_transparent">Are you a Buyer<img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="custom_logo_banner">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="logo_images">
                        <div class="home_logo">
                            <?php if(!empty($home->section_two_logo_icon_one)): ?>
                            <img src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_two_logo_icon_one); ?>">
                            <?php else: ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/images/logoipsum-280.png">
                            <?php endif; ?>
                        </div>
                        <div class="home_logo">
                            <?php if(!empty($home->section_two_logo_icon_two)): ?>
                            <img src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_two_logo_icon_two); ?>">
                            <?php else: ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/images/logoipsum-280.png">
                            <?php endif; ?>
                        </div>
                        <div class="home_logo">
                            <?php if(!empty($home->section_two_logo_icon_three)): ?>
                            <img src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_two_logo_icon_three); ?>">
                            <?php else: ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/images/logoipsum-280.png">
                            <?php endif; ?>
                        </div>
                        <div class="home_logo">
                            <?php if(!empty($home->section_two_logo_icon_four)): ?>
                            <img src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_two_logo_icon_four); ?>">
                            <?php else: ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/images/logoipsum-280.png">
                            <?php endif; ?>
                        </div>
                        <div class="home_logo">
                            <?php if(!empty($home->section_two_logo_icon_five)): ?>
                            <img src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_two_logo_icon_five); ?>">
                            <?php else: ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/images/logoipsum-280.png">
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="home_site_details" id="explain_process">
        <div class="container">
            <div class="row custom_row">
                <div class="col-md-12">
                    <h1><?php echo e($home->section_three_heading??''); ?></h1>
                </div>
                <div class="col-md-3 col-sm-6 custom_column">
                    <div class="site_visiting custom_flex custom_arrow">
                        <div class="blogs_icon">
                            <img src="<?php echo e(asset('website')); ?>/assets/images/paper-plane-top.png">
                        </div>
                        <h2><?php echo e($home->section_three_sub_one_heading??""); ?></h2>
                        <h5><?php echo e($home->section_three_sub_one_description??''); ?></h5>


                    </div>
                </div>
                <div class="col-md-3 col-sm-6 custom_column">
                    <div class="site_visiting custom_flex custom_arrow">
                        <div class="blogs_icon">
                            <span><i class="fa-solid fa-user-group"></i></span>
                        </div>
                        <h2><?php echo e($home->section_three_sub_two_heading??''); ?></h2>
                        <h5><?php echo e($home->section_three_sub_two_description??''); ?></h5>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 custom_column">
                    <div class="site_visiting custom_flex custom_arrow">
                        <div class="blogs_icon">
                            <span><i class="fa-solid fa-list-check"></i></span>
                        </div>
                        <h2><?php echo e($home->section_three_sub_three_heading??''); ?></h2>
                        <h5><?php echo e($home->section_three_sub_three_description??''); ?></h5>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 custom_column">
                    <div class="site_visiting custom_flex">
                        <div class="blogs_icon">
                            <img src="<?php echo e(asset('website')); ?>/assets/images/shield-check.png">
                        </div>
                        <h2><?php echo e($home->section_three_sub_four_heading??''); ?></h2>
                        <h5><?php echo e($home->section_three_sub_four_description??''); ?></h5>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="what_drives_us">
        <div class="container">
            <div class="row custom_align_row custom_row">
                <div class="col-md-6">
                    <div class="mission_value_images">
                        <div class="plumbering_img">
                            <?php if(!empty($home->section_four_image_one)): ?>
                                <img src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_four_image_one); ?>">
                            <?php else: ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/images/plumbering.png">
                            <?php endif; ?>
                        </div>
                        <div class="welding_img">
                            <?php if(!empty($home->section_four_image_two)): ?>
                                <img src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_four_image_two); ?>">
                            <?php else: ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/images/welding.png">
                            <?php endif; ?>
                        </div>
                        <div class="solding_iron_img">
                            <?php if(!empty($home->section_four_image_three)): ?>
                                <img src="<?php echo e(asset('website')); ?>/<?php echo e($home->section_four_image_three); ?>">
                            <?php else: ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/images/solding_iron.png">
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mission_values">
                        <h1><?php echo e($home->section_four_heading??''); ?></h1>
                        <div class="home_section_tabs">
                            <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="pills-homeowners-tab" data-bs-toggle="pill" data-bs-target="#pills-homeowners" type="button" role="tab" aria-controls="pills-homeowners" aria-selected="true"><?php echo e($home->section_four_sub_one_heading??''); ?></button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="pills-contractors-tab" data-bs-toggle="pill" data-bs-target="#pills-contractors" type="button" role="tab" aria-controls="pills-contractors" aria-selected="false"><?php echo e($home->section_four_sub_two_heading??''); ?></button>
                                </li>
                            </ul>
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane fade show active" id="pills-homeowners" role="tabpanel" aria-labelledby="pills-homeowners-tab" tabindex="0">
                                    <div class="mission_listing">
                                        <ul>
                                            <li><?php echo e($home->section_four_sub_one_description_one??''); ?></li>
                                            <li><?php echo e($home->section_four_sub_one_description_two??''); ?></li>
                                            <li><?php echo e($home->section_four_sub_one_description_three??''); ?></li>
                                            <li><?php echo e($home->section_four_sub_one_description_four??''); ?></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="pills-contractors" role="tabpanel" aria-labelledby="pills-contractors-tab" tabindex="0">
                                    <div class="mission_listing">
                                        <ul>
                                            <li><?php echo e($home->section_four_sub_two_description_one??''); ?></li>
                                            <li><?php echo e($home->section_four_sub_two_description_two??''); ?></li>
                                            <li><?php echo e($home->section_four_sub_two_description_three??''); ?></li>
                                            <li><?php echo e($home->section_four_sub_two_description_four??''); ?></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="categories_of_expertise homepage_cards expertise_cards">
        <div class="container">
            <div class="row custom_row">
                <div class="col-md-12">
                    <h1><?php echo e($home->section_five_heading??''); ?></h1>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-house-user"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_one_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_one_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-campground"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_two_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_two_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-house-chimney"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_three_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_three_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-broom"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_four_heading??""); ?></h4>
                            <p><?php echo e($home->section_five_sub_four_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-panorama"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_five_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_five_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-hammer"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_six_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_six_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-palette"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_seven_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_seven_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-wrench"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_eight_heading??""); ?></h4>
                            <p><?php echo e($home->section_five_sub_eight_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-lightbulb"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_nine_heading??""); ?></h4>
                            <p><?php echo e($home->section_five_sub_nine_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-warehouse"></i></span>
                            </div>
                            <h4><?php echo e($home->section_five_sub_ten_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_ten_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/window-frame-open.png">
                            </div>
                            <h4><?php echo e($home->section_five_sub_eleven_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_eleven_description??''); ?></p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-4 col-6 custom_column">
                    <a href="<?php echo e(url('registration_role')); ?>">
                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/block-brick.png">
                            </div>
                            <h4><?php echo e($home->section_five_sub_twelve_heading??''); ?></h4>
                            <p><?php echo e($home->section_five_sub_twelve_description??''); ?></p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>
    <section class="home_slider">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1><?php echo e($home->section_six_heading??''); ?></h1>
                </div>
                <div class="col-md-12">
                    <div class="swiper mySwiper">
                        <div class="swiper-wrapper">
                            <?php if(!empty($hometest)): ?>
                            <?php $__empty_1 = true; $__currentLoopData = $hometest; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $test): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="swiper-slide">
                                <div class="custom_user_feedback">
                                    <div class="user_img">
                                        <img src="<?php echo e(asset('website')); ?>/<?php echo e($test->image); ?>">
                                    </div>
                                    <div class="user_feedback">
                                        <h5><?php echo e($test->description??''); ?></h5>
                                    </div>
                                    <div class="user_name">
                                        <h2><?php echo e(ucwords($test->name??"")); ?></h2>
                                        <h5><?php echo e(ucwords($test->role ?? '')); ?></h5>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="swiper-button-arrow right">
                            <span>
                                <i class="fa-solid fa-arrow-right"></i>
                            </span>
                    </div>
                    <div class="swiper-button-arrow left">
                            <span>
                                <i class="fa-solid fa-arrow-left"></i>
                            </span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="newsletter">
        <div class="container">
            <div class="row custom_row">
                <div class="col-md-12">
                    <h1><?php echo e($footerData->section_one_heading??''); ?></h1>
                </div>
                <div class="col-md-12">
                    <form id="subscribeForm">
                        <?php echo csrf_field(); ?>
                        <div class="txt_field newsletter_contact">
                            <input id="email" placeholder="Email address" type="email" class="form-control" name="email" value="">
                            <button type="submit" class="btn btn_white">Subscribe
                                <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                            </button>
                        </div>
                        <div class="is_subscribe"></div>
                    </form>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush("js"); ?>
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script>
    var swiper = new Swiper(".mySwiper", {
        navigation: {
            nextEl: ".swiper-button-arrow.right" ,
            prevEl: ".swiper-button-arrow.left",
        },
        loop: true,
        autoplay: {
            delay: 4000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
        },
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/main/home.blade.php ENDPATH**/ ?>