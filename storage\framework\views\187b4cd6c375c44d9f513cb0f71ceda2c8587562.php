<div class="modal-dialog modal-dialog-centered " role="document">
    <div class="modal-content">
        <div class="modal-header">
            <div class="posted_view_modal_content">
                <div class="company_name">
                    <h2><?php echo e($sellerBid->getStaffDetail->profile->company_name??'----'); ?></h2>
                </div>
                <div class="custom_header">
                    <div class="logo_and_name">
                        <div class="logo">
                            <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($sellerBid?->getStaffDetail?->profile->pic??''); ?>" alt="">
                        </div>
                        <div class="bidder_name">
                            <h5> <?php echo e($sellerBid->getStaffDetail->name??'----'); ?>      </h5>
                            <p>  <?php echo e($sellerBid->created_at->format('d-m-Y H:i')); ?>  </p>
                        </div>
                    </div>
                    <div class="view_profile">
                        <a href="<?php echo e(url('buyer_chat')); ?>" class="btn btn_black email_button"><i class="fa-solid fa-envelope"></i></a>
                        <a href="<?php echo e(url('service_profile')); ?>/<?php echo e($sellerBid->staff_id); ?>" class="btn btn_black">View Profile</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-body">
            <div class="row custom_rowGap">
                <div class="col-md-6 col-sm-4 col-6">
                    <h5>Labor Expense</h5>
                    <h3>$<?php echo e($sellerBid->labour_expense??"0"); ?></h3>
                </div>
                <div class="col-md-6 col-sm-4 col-6">
                    <h5>Total Milestone Price</h5>
                    <h3><?php if(isset($jobMilestone) && !empty($jobMilestone)): ?> $<?php echo e($jobMilestone->sum('amount') ?? 0); ?> <?php endif; ?></h3>
                </div>
                <div class="col-md-6 col-sm-4 col-6">
                    <h5>Materials Cost</h5>





                    <h3>$ <?php echo e($sellerBid->material_expense ?? '0'); ?></h3>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-12">
                    <h5>Comment</h5>
                    <h6><?php echo e($sellerBid->comment ?? '0'); ?></h6>
                </div>
                <div class="col-md-12">
                    <div class="project_ongoing_report">
                        <h3>Milestone</h3>
                        <div class="row">
                           <?php if(isset($jobMilestone) && !empty($jobMilestone)): ?>
                                <?php $__currentLoopData = $jobMilestone; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 col-sm-6 custom_column_card">
                                        <div class="milestone_detail">
                                            <h5><?php echo e($loop->iteration ?? $key); ?></h5>
                                            <div class="milestone_icon">
                                                <span><i class="fa-solid fa-flag"></i></span>
                                                
                                            </div>
                                            <div class="milestone_card">
                                                <h5><?php echo e($milestone->title??'---'); ?></h5>
                                                <p>$ <?php echo e($milestone->amount??'0'); ?></p>
                                                <span class="due_date"><i class="fa-regular fa-calendar"></i>Date <?php echo e($milestone->date??'---'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                           <?php endif; ?>
                        </div>
                    </div>
                </div>
                    <?php if(isset($sellerBid) && ($sellerBid->getJobDetail->assign_seller_id == '' || $sellerBid->getJobDetail->assign_seller_id == null)): ?>
                <div class="col-md-12">
                    <div class="modal_btn">
                        <button type="button" class="btn btn_light_red update_status_btn" data-status="rejected" >Reject</button>
                        <button type="button" class="btn btn_green  update_status_btn" data-status="accepted">Accept </button>
                    </div>
                </div>
                    <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
<script>
    $(document).on('click', '.update_status_btn', function () {
        var status = $(this).attr('data-status');
        var jobOfferId = '<?php echo e($sellerBid->id); ?>'; // Assuming each button has this data attribute for the job offer ID
        // Show confirmation dialog
        Swal.fire({
            title: 'Are you sure?',
            text: "Do you want to " + status + " this job offer?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No'
        }).then((result) => {
            if (result.isConfirmed) {
                // User confirmed, proceed with the AJAX request
                console.log(jobOfferId);
                $.ajax({
                    url: "<?php echo e(url('update_job_offer_status')); ?>/" + jobOfferId + "/" + status,
                    method: 'GET', // Adjust method if necessary
                    success: function (data) {
                        Swal.fire(
                            'Updated!',
                            'The job offer status has been updated to ' + status + '.',
                            'success'
                        ).then(() => {
                            window.location.href = "<?php echo e(url('project_contract')); ?>/<?php echo e($sellerBid->id); ?>/<?php echo e($sellerBid->job_id); ?>";
                        });
                    },
                    error: function (xhr) {
                        Swal.fire(
                            'Error!',
                            'There was an error updating the job offer status.',
                            'error'
                        );
                    }
                });
            }
        });
    });
</script>
<?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/ajax/view_seller_bid_modal_ajax.blade.php ENDPATH**/ ?>