<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;

class UserChatSidebar implements ShouldBroadcast
{
    use SerializesModels;

    public $user; // you can pass any data you want

    public function __construct($user)
    {
        $this->user = $user;
    }

    public function broadcastOn()
    {
        return new Channel('sidebar-user-channel');
    }

    public function broadcastAs()
    {
        return 'user-added';
    }
}
