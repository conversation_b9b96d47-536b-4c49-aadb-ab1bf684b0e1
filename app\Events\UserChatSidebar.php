<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;

class UserChatSidebar implements ShouldBroadcast
{
    use SerializesModels;

    public $user; // you can pass any data you want
    public $groupMembers; // array of user IDs who should receive this event

    public function __construct($user = null, $groupMembers = [])
    {
        $this->user = $user;
        $this->groupMembers = $groupMembers;
    }

    public function broadcastOn()
    {
        // If group members are specified, broadcast to individual user channels
        if (!empty($this->groupMembers)) {
            $channels = [];
            foreach ($this->groupMembers as $memberId) {
                $channels[] = new Channel('sidebar-user-channel-' . $memberId);
            }
            return $channels;
        }

        // Fallback to global channel if no specific members
        return new Channel('sidebar-user-channel');
    }

    public function broadcastAs()
    {
        return 'user-added';
    }
}
