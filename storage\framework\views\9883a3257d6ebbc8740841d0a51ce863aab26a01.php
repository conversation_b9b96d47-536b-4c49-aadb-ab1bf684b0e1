<?php $__env->startPush('css'); ?>
    <link href="<?php echo e(asset('plugins/components/morrisjs/morris.css')); ?>" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="<?php echo e(asset('plugins/components/dropify/dist/css/dropify.min.css')); ?>">

<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">E-Wallet</h2>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <section class="projects_wrapper pagination_scroll_tbl">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="ewallet_sales">
                        <div class="custom_sales">
                            <h3>Sales Statistics</h3>
                        </div>
                        <div class="statistics_card">
                            <div class="row">
                                <div class="col-md-3 col-sm-6 custom_stats_column">
                                    <div class="custom_card">
                                        <h5>Released Milestone</h5>
                                        <div class="total_sales_graph">
                                            <div class="chart_img">
                                                <svg width="0" height="0">
                                                    <defs>
                                                        <linearGradient id="lineGradient7" x1="0%" y1="0%" x2="0%" y2="100%">
                                                            <stop offset="0%" style="stop-color:rgba(34, 128, 194, 0.25); stop-opacity:1" />
                                                            <stop offset="100%" style="stop-color:rgba(34, 128, 194, 0); stop-opacity:1" />
                                                        </linearGradient>
                                                    </defs>
                                                </svg>
                                                <span class="peity-line1" data-width="100%" data-height="60">6,2,8,4,3,8,1,3,6,5,9,2,8,1,4,8,9,8,2,1</span>
                                            </div>
                                        </div>
                                        <h3>$ <?php echo e($milestones->where('requested','released')->sum('amount')  ?? '0'); ?> </h3>

                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 custom_stats_column">
                                    <div class="custom_card">
                                        <h5>Withdrawn Amount</h5>
                                        <div class="chart_img">
                                            <svg width="0" height="0">
                                                <defs>
                                                    <linearGradient id="lineGradient8" x1="0%" y1="0%" x2="0%" y2="100%">
                                                        <stop offset="0%" style="stop-color:rgba(119, 185, 42, 0.25); stop-opacity:1" />
                                                        <stop offset="100%" style="stop-color:rgba(119, 185, 42, 0); stop-opacity:1" />
                                                    </linearGradient>
                                                </defs>
                                            </svg>
                                            <span class="peity-line2" data-width="100%" data-height="60">1,2,8,9,8,4,1,8,2,9,5,6,3,1,8,4,8,2,6</span>
                                        </div>
                                        <h3>$ <?php echo e($reqWithdrawal->where('status','approved')->sum('amount') ?? '0'); ?> </h3>

                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 custom_stats_column">
                                    <div class="custom_card">
                                        <h5>Rejected Withdrawals</h5>
                                        <div class="chart_img">
                                            <svg width="0" height="0">
                                                <defs>
                                                    <linearGradient id="lineGradient9" x1="0%" y1="0%" x2="0%" y2="100%">
                                                        <stop offset="0%" style="stop-color:rgba(228, 2, 85, 0.25); stop-opacity:1" />
                                                        <stop offset="100%" style="stop-color:rgba(228, 2, 85, 0); stop-opacity:1" />
                                                    </linearGradient>
                                                </defs>
                                            </svg>
                                            <span class="peity-line3" data-width="100%" data-height="60">6,2,8,4,3,8,1,3,6,5,9,2,8,1,4,8,9,8,2,1</span>
                                        </div>
                                        <h3>$ <?php echo e($reqWithdrawal->where('status','rejected')->sum('amount') ?? '0'); ?></h3>

                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 custom_stats_column">
                                    <div class="custom_card">
                                        <h5>Admin Commission</h5>
                                        <div class="chart_img">
                                            <svg width="0" height="0">
                                                <defs>
                                                    <linearGradient id="lineGradient10" x1="0%" y1="0%" x2="0%" y2="100%">
                                                        <stop offset="0%" style="stop-color:rgba(246, 166, 0, 0.25); stop-opacity:1" />
                                                        <stop offset="100%" style="stop-color:rgba(246, 166, 0, 0); stop-opacity:1" />
                                                    </linearGradient>
                                                </defs>
                                            </svg>
                                            <span class="peity-line4" data-width="100%" data-height="60">6,2,8,4,3,8,1,3,6,5,9,2,8,1,4,8,9,8,2,1</span>
                                        </div>
                                        <?php
                                            $approvedAmount = $reqWithdrawal->where('status', 'approved')->sum('amount') ?? 0; // Total approved amount
                                            $adminCommission = ($approvedAmount * 12) / 100; // Calculate 12% commission
                                        ?>
                                        <h3>$<?php echo e($adminCommission ?? '0.0'); ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="white-box ewallet_chart">
                        <h3 >Overall Sales</h3>
                        <div class="overall_sale_chart">
                            <canvas id="line-chart" ></canvas>
                        </div>
























                    </div>
                </div>
                <div class="col-md-6">
                    <div class="custom_projects_tabs scrollable_tbl request_withdrawal">
                        <h3>Requested Withdrawals</h3>
                        <div class="table-responsive">
                            <div class="custom_table">
                                <table id="" class="table table-striped without_pagination_tbl datatable">
                                    <thead>
                                    <tr>
                                        <th>Service Provider</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $reqWithdrawal; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $withdrawal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><a href="#!" class="withdrawal_view_id" withdrawal-id="<?php echo e($withdrawal->id); ?>" ><?php echo e($withdrawal->user->name??'----'); ?></a></td>
                                            <td>$<?php echo e($withdrawal->amount??'----'); ?></td>
                                            <td><?php echo e($withdrawal->created_at->format('d-m-y')??'----'); ?></td>
                                            <td>
                                                <span class="<?php echo e($withdrawal->status == 'approved' ? 'success' : ($withdrawal->status == 'rejected' ? 'danger' : 'secondary')); ?>"><?php echo e($withdrawal->status??'----'); ?></span>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                            <tr>
                                                <td colspan="5">No Withdrawal Request</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="custom_projects_tabs scrollable_tbl">
                        <h3>Transaction History</h3>
                        <div class="table-responsive">
                            <div class="custom_table">
                                <table id="" class="table table-striped myTable datatable">
                                    <thead>
                                    <tr>
                                        <th>Service Provider</th>
                                        <th>Amount</th>
                                        <th> Status </th>
                                        <th>Date</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $reqWithdrawal->where('status','approved'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Withdrawal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="withdrawal_view_id" withdrawal-id="<?php echo e($withdrawal->id); ?>">
                                            <td ><?php echo e($Withdrawal->user->name??"---"); ?></td>
                                            <td>$<?php echo e($Withdrawal->amount??"---"); ?></td>
                                            <td><?php echo e($Withdrawal->status??"---"); ?></td>
                                            <td><?php echo e($Withdrawal->updated_at->format('d-m-y')??"---"); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="modal fade credit_details_modal" id="credit_details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
    </div>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

    <script src="<?php echo e(asset('plugins/components/peity/jquery.peity.min.js')); ?>"></script>
    <script src="<?php echo e(asset('plugins/components/peity/jquery.peity.init.js')); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
    <script>
        $(document).ready(function() {
            $('[data-fancybox="gallery"]').fancybox({
                protect: false,
                clickOutside: false,
                closeExisting: false,
            });
        });
    </script>

    <script>
        function initializePeityCharts() {
            $('.peity-line1').each(function () {
                $(this).peity("line", {
                    fill: "url(#lineGradient7)",
                    stroke: "#2280C2",
                    width: $(this).parent().width() || 100, // Set width dynamically
                    height: 30
                });
            });

            $('.peity-line2').each(function () {
                $(this).peity("line", {
                    stroke: "#77B92A",
                    fill: "url(#lineGradient8)",
                    width: $(this).parent().width() || 100,
                    height: 30
                });
            });

            $('.peity-line3').each(function () {
                $(this).peity("line", {
                    stroke: "#E40255",
                    fill: "url(#lineGradient9)",
                    width: $(this).parent().width() || 100,
                    height: 30
                });
            });

            $('.peity-line4').each(function () {
                $(this).peity("line", {
                    stroke: "#F6A600",
                    fill: "url(#lineGradient10)",
                    width: $(this).parent().width() || 100,
                    height: 30
                });
            });
        }

        // Initialize on page load
        initializePeityCharts();

        // Reinitialize on window resize
        $(window).on("resize", function () {
            initializePeityCharts();
        });


    </script>

    <script>
        // Get the canvas context for the chart
        const ctx = document.getElementById("line-chart").getContext('2d');

        // Define gradients
        const gradientOne = ctx.createLinearGradient(0, 0, 0, 400);
        gradientOne.addColorStop(0, 'rgba(34, 128, 194, 0.5)');
        gradientOne.addColorStop(1, 'rgba(255, 255, 255, 0)');

        const gradientTwo = ctx.createLinearGradient(0, 0, 0, 400);
        gradientTwo.addColorStop(0, 'rgba(119, 185, 42, 0.5)');
        gradientTwo.addColorStop(1, 'rgba(255, 255, 255, 0)');

        const gradientThree = ctx.createLinearGradient(0, 0, 0, 400);
        gradientThree.addColorStop(0, 'rgba(228, 2, 85, 0.5)');
        gradientThree.addColorStop(1, 'rgba(255, 255, 255, 0)');

        // Data from the server
        const monthlyData = <?php echo json_encode($monthlyData, 15, 512) ?>;

        // Labels for months
        const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        // Prepare data for the chart
        const releasedMilestones = Object.values(monthlyData.releasedMilestones);
        const approvedWithdrawals = Object.values(monthlyData.approvedWithdrawals);
        const rejectedWithdrawals = Object.values(monthlyData.rejectedWithdrawals);

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Released Milestones',
                        data: releasedMilestones,
                        borderColor: '#2280C2',
                        backgroundColor: gradientOne,
                        tension: 0.4,
                        borderWidth: 2,
                        fill: 'start'
                    },
                    {
                        label: 'Approved Withdrawals',
                        data: approvedWithdrawals,
                        borderColor: '#77b92a',
                        backgroundColor: gradientTwo,
                        tension: 0.4,
                        borderWidth: 2,
                        fill: 'start'
                    },
                    {
                        label: 'Rejected Withdrawals',
                        data: rejectedWithdrawals,
                        borderColor: '#E40255',
                        backgroundColor: gradientThree,
                        tension: 0.4,
                        borderWidth: 2,
                        fill: 'start'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false, // Ensures that chart adapts height
                plugins: {
                    legend: { display: true, position: 'bottom' },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `$ ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: { display: false },
                        ticks: { font: { size: 12 } }
                    },
                    y: {
                        ticks: {
                            callback: value => `$${value}`,
                            font: { size: 12 }
                        },
                        grid: { display: false }
                    }
                }
            }
        });


        $(document).on('click','.withdrawal_view_id',function (){
            var id = $(this).attr('withdrawal-id');
            $.ajax({
                url: '<?php echo e(url('withdrawal_request_view')); ?>/' + id, //Replace with your desired URL
                success: function(response) {
                    $('#credit_details').html(response);
                    $('#credit_details').modal('show');
                },
            });
        });

    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/admin/admin_ewallet.blade.php ENDPATH**/ ?>