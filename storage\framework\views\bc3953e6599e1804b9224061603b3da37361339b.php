<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="notification_page">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="notification custom_projects_tabs">
                        <div class="row ">
                            <div class="col-md-12 ">
                                <div class="notify_title">

                                    <h3>Notification</h3>





                                </div>
                                <?php if(!empty($notifications)): ?>
                                    <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                        <?php if($notification->type == 'DocApproved'): ?>
                                        <div class="col-md-12">
                                            <div class="notify_status">
                                                <div class="user_profile">
                                                    <div class="profile_img">
                                                        <i class="fa-solid fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <h6><?php echo e($notification->data['title'] ??''); ?></h6>
                                                        <p><?php echo e(ucwords($notification->data['message']??'')); ?></p>
                                                    </div>



                                                </div>
                                                <div class="status_time">
                                                    <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>



                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                            <?php if($notification->type == 'contact_request'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'] ??''); ?></h6>
                                                                <p>Your contact request has been submitted successfully. Our team will review your message and get back to you as soon as possible.</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php if($notification->type == 'DocRejected'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6><?php echo e($notification->data['title'] ??''); ?></h6>
                                                            <p><?php echo e(ucwords($notification->data['message']??'')); ?></p>
                                                        </div>
                                                        
                                                        
                                                        
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'user_registered'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6>New User Created</h6>
                                                            <p>A new user has been successfully created.<br>
                                                                <b>Name</b>: <?php echo e(ucwords($notification->data['name'] ?? '')); ?> | <b>Email</b>: <?php echo e($notification->data['email'] ?? ''); ?>.</p>
                                                        </div>
                                                        
                                                        
                                                        
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'job_offer_awarded'): ?>
                                        <div class="col-md-12">
                                            <div class="notify_status">
                                                <div class="user_profile">
                                                    <div class="profile_img">
                                                        <i class="fa-solid fa-user"></i>
                                                    </div>
                                                    <div>

                                                        <h6>Job Offer Approve</h6>
                                                        <p>Congratulations! <?php echo e(ucwords( $notification->data['name'] ?? '')); ?> has accepted your bid and awarded you the job "<?php echo e($notification->data['title'] ?? ''); ?>" for <?php echo e($notification->data['amount'] ?? ''); ?>.</p>
                                                    </div>



                                                </div>
                                                <div class="status_time">
                                                    <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>



                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                        <?php if($notification->type == 'job_posted'): ?>
                                        <div class="col-md-12">
                                            <div class="notify_status">
                                                <div class="user_profile">
                                                    <div class="profile_img">
                                                        <i class="fa-solid fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <h6>New Job Posted</h6>
                                                        <p><?php echo e(ucwords($notification->data['name'] ?? '')); ?> has created a new job listing: "<?php echo e($notification->data['project_title'] ?? ''); ?>"</p>
                                                    </div>



                                                </div>
                                                <div class="status_time">
                                                    <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>



                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                        <?php if($notification->type == 'job_offer_stripe_payment'): ?>
                                        <div class="col-md-12">
                                            <div class="notify_status">
                                                <div class="user_profile">
                                                    <div class="profile_img">
                                                        <i class="fa-solid fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <h6>Payment Recived</h6>
                                                        <p><?php echo e(ucwords($notification->data['name'] ?? '')); ?> has payed <?php echo e($notification->data['amount']??''); ?> for this job "<?php echo e($notification->data['title'] ?? ''); ?>"</p>
                                                    </div>



                                                </div>
                                                <div class="status_time">
                                                    <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>



                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                        <?php if($notification->type == 'disputeCreated'): ?>
                                        <div class="col-md-12">
                                            <div class="notify_status">
                                                <div class="user_profile">
                                                    <div class="profile_img">
                                                        <i class="fa-solid fa-user"></i>
                                                    </div>
                                                    <div>
                                                                <h6>Dispute</h6>
                                                                <p><?php echo e($notification->data['message'] ?? 'A new Dispute is created'); ?></p>
                                                            </div>
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>

                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'estimateBid'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6>Estimate Bid</h6>
                                                                <?php if(auth()->user()->hasRole('buyer')): ?>
                                                                <p>You have a new estimate bid for the job: <?php echo e($notification->data['project_name']); ?></p>
                                                                <?php else: ?>
                                                                <p>You make an estimate bid for the job: <?php echo e($notification->data['project_name']); ?></p>
                                                                <?php endif; ?>
                                                    </div>



                                                </div>
                                                <div class="status_time">
                                                    <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>



                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                        <?php if($notification->type == 'jobOfferBidSeller'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6><?php echo e($notification->data['title'] ?? 'Dispute Created'); ?></h6>
                                                            <p><?php echo e($notification->data['message'] ?? ''); ?>"</p>
                                                        </div>
                                                        
                                                        
                                                        
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'UserRequest'): ?>
                                        <div class="col-md-12">
                                            <div class="notify_status">
                                                <div class="user_profile">
                                                    <div class="profile_img">
                                                        <i class="fa-solid fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <h6>User Category Request</h6>
                                                        <p><?php echo e(ucwords($notification->data['name'] ?? '')); ?> has submitted a request to be approved for an additional category.</p>
                                                    </div>



                                                </div>
                                                <div class="status_time">
                                                    <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>



                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                        <?php if($notification->type == 'milestone_request'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                            <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                        </div>
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'milestone_release'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                            <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                        </div>
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'job_payment_accepted'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                            <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                        </div>
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'withdrawal_request'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6><?php echo e($notification->data['title']??''); ?></h6>
                                                            <p><?php echo e($notification->data['message'] ?? 'Withdrawal Requested!'); ?></p>
                                                        </div>
                                                        
                                                        
                                                        
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'estimate_accepted'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                            <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                        </div>
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'job_offer_accepted_wire'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6>Wire Transfer</h6>
                                                                <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php if($notification->type == 'job_reposted'): ?>
                                            <div class="col-md-12">
                                                <div class="notify_status">
                                                    <div class="user_profile">
                                                        <div class="profile_img">
                                                            <i class="fa-solid fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                            <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                        </div>
                                                    </div>
                                                    <div class="status_time">
                                                        <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        
                                                        
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($notification->type == 'milestoneDocsUploaded'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>


                                            





















                                        <?php if(Auth::check() && auth()->user()->hasRole('buyer')): ?>
                                            <?php if($notification->type == 'jobOfferBidBuyer'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'] ?? 'Dispute Created'); ?></h6>
                                                                <p><?php echo e($notification->data['message'] ?? ''); ?>"</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'job_staff_assigned'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                                <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'job_payment_accepted'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                                <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'milestone_release'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                                <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'job_status_staff'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                                <p>
                                                                    <?php echo e($notification->job_status == 'job_rejected_staff' ? 'Heads up!' : 'Good news!'); ?>

                                                                    <?php echo e($notification->data['name'] ?? ''); ?> has
                                                                    <?php echo e($notification->job_status == 'job_rejected_staff' ? 'rejected' : 'agreed to take on'); ?>

                                                                    your project: "<?php echo e($notification->data['job_title'] ?? ''); ?>"
                                                                </p>
                                                            </div>
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'milestone_request'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                                <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                    <?php endif; ?>
                                            <?php if($notification->type == 'job_assigned'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6>Job Assign</h6>
                                                                <p>Hello <?php echo e(ucwords(auth()->user()->name??'')); ?>, your job has been successfully assigned to <?php echo e(ucwords($notification->data['name'] ?? 'a seller')); ?> (<?php echo e($notification->data['job_title'] ?? ''); ?>).</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?php if($notification->type == 'job_status_staff'): ?>
                                                <div class="col-md-12">
                                                    <div class="notify_status">
                                                        <div class="user_profile">
                                                            <div class="profile_img">
                                                                <i class="fa-solid fa-user"></i>
                                                            </div>
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                                <p><?php echo e($notification->data['name'] ?? ''); ?> has <strong><?php echo e($notification->job_status == 'job_rejected_staff' ? 'rejected' : 'taken'); ?></strong> the project "<?php echo e($notification->data['job_title'] ?? ''); ?>"
                                                                    submitted by <?php echo e($notification->data['buyer_name'] ?? ''); ?>.</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            
                                                            
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function(){
            function updateNotificationCount() {
                let count = $('.notify_status').filter(function() {
                    return !$(this).find('.mark-as-read').length;
                }).length;
                $('#notification-count').text(count);
            }
            $('.mark-as-read').click(function(e) {
                e.preventDefault();
                let notificationId = $(this).data('id');
                let button = $(this);
                $.ajax({
                    url : "<?php echo e(route('mark_as_read')); ?>/" + notificationId,
                    method : 'POST',
                    data:{
                        '_token' : "<?php echo e(csrf_token()); ?>"
                    },
                    success :function(response){
                        button.closest('.notify_status').fadeOut(300, function() {
                            $(this).remove();
                            updateNotificationCount();
                        });
                    },
                    error:function(xhr){
                        console.log(xhr);
                    }
                })
            })
            $('.mark-all-read').click(function(e){
                e.preventDefault();
                let button = $(this);
                $.ajax({
                    url : " <?php echo e(route('mark_all_as_read')); ?>",
                    method : 'POST',
                    data:{
                        '_token' : "<?php echo e(csrf_token()); ?>",
                    },
                    success: function(response) {
                        $('.notify_status').each(function() {
                            if ($(this).find('.mark-as-read').length) {
                                $(this).fadeOut(300, function() {
                                    $(this).remove();
                                });
                            }
                        });
                        updateNotificationCount();
                    },
                    error :function (xhr){
                        console.log(xhr);
                    }
                })
            })
        })

    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/website_notification.blade.php ENDPATH**/ ?>