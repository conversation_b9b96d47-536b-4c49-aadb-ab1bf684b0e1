@extends("layouts.master")
@push('css')
<link rel="stylesheet" href="{{ asset('css/chat.css') }}" />
<script src="https://code.jquery.com/jquery-1.11.1.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<script>
    var base_url = '{{ url("/chat") }}';
</script>
@endpush
@section('navbar-title')
    <div  class="topbar">
        <h2 id="">Dispute</h2>
    </div>
@endsection

@section('content')
    <section class="chat_sec custom_chats dispute_chat">
        <div class="container-fluid">
            <div class="row Chat_box">
                <div class="col-lg-4 col-md-5">
                    <div class="chats_detail">
                        <div class="search-btn-container searchbar_input">
                            <input type="search" id="myinput" class="custom_search_box form-control" placeholder="Search">
                        </div>
                        <div class="document_details">
                            <div class="participant_name">
                                <h5>Participants Details</h5>
                            </div>
                            <div class="row custom_row">
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <div class="custom_title">
                                        <h5>Client Name:</h5>
                                        <h6>{{ $job->user->name }}</h6>
                                        <h6>{{ $job->user->email }}</h6>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <div class="custom_title">
                                        <h5>Contractor Name:</h5>
                                        <h6>{{ $job->jobOffersPaid->getStaffDetail->name??'' }}</h6>
                                        <h6>{{ $job->jobOffersPaid->getStaffDetail->email??'' }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="vertical-tabs nav-detail">
                            <ul class="nav nav-tabs chat_users_show all_users_chats">
                                @if(count($groups) > 0)
                                    @foreach($groups as $user)
                                        <li class="contacts" style="cursor: pointer;">
                                            <a class="chat-box-toggle user_profile_msg" data-user-name="@if(Auth::id() == $user->sender_id) {{ $user->getReceiver->name??'' }} @else {{ $user->getSender->name??'' }} @endif" data-user-id="@if(Auth::id() == $user->sender_id) {{ base64_encode(strtr($user->getReceiver->id, '._-', '+/='))??'' }} @else {{ base64_encode(strtr($user->getSender->id, '._-', '+/='))??'' }} @endif" data-sender="{{ $user->sender_id }}" data-image="@if(Auth::id() == $user->sender_id) {{asset('storage')}}/uploads/users/{{ $user->getReceiver->profile->pic??'' }}  @else {{asset('storage')}}/uploads/users/{{ $user->getSender->profile->pic??'' }}  @endif" data-item="{{ $user->admin_id }}" data-id="{{ $user->group_id }}" data-user="@if(Auth::id() == $user->sender_id) {{ $user->getReceiver->name }} @else {{ $user->getSender->name }}  @endif" data-group="0">
                                                <div class="current_user_detail">
                                                    <div class="user_img">
                                                        <img @if(Auth::id() == $user->sender_id) src="{{asset('storage')}}/uploads/users/{{ $user->getReceiver->profile->pic??'' }}"  @else src="{{asset('storage')}}/uploads/users/{{ $user->getSender->profile->pic??'' }}"  @endif  class="avatar">
                                                    </div>
                                                    <div class="user_msg">
                                                        <h6> @if(Auth::id() == $user->sender_id) {{ $user->getReceiver->name??'' }} @else {{ $user->getSender->name??'' }} @endif </h6>
                                                        <p>{{ $user->getItem->project_title }}</p>
                                                    </div>
                                                </div>
                                                <div class="time">
                                                    <p>10:30 Pm</p>
                                                    <span class="status">03</span>
                                                </div>
                                                <?php   $Message_user = App\MessageViewed::where('group_id',$user->group_id)->where('receiver',Auth::id())->where('viewed',0)->count(); ?>
                                                @if($Message_user > 0)
                                                    <span style="background-color: red;" class="badge badge-xs badge-danger">{{ $Message_user }}</span>
                                                @endif
                                            </a></li>
                                    @endforeach
                                @endif
                                @if(count($users) > 0)
                                    @foreach($users as $user)
                                        <li class="contacts" style="cursor: pointer;">
                                            <a class="chat-box-toggle user_profile_msg" data-item="0" data-id="{{ $user->id }}" @if($user->name == 'User')  data-user="Mr do all" @else data-user="{{ $user->name }}" @endif  data-group="1">
                                                <div class="current_user_detail">
                                                    <div class="user_img">
                                                        <img  src="{{asset('storage')}}/uploads/users/{{ $user->profile->pic??'' }}"    class="avatar">
                                                    </div>
                                                    <div class="user_msg">
                                                        <h6>@if($user->name == 'User') Mr do all @else {{ $user->name??'' }} @endif</h6>
                                                        {{--  <span style="font-size: 12px"> @if($user->hasRole('owner'))  (Property Owner)  @else  {{ "(Project Holder)" }}  @endif</span> --}}
                                                    </div>
                                                </div>
                                                <div class="time">
                                                    <p>10:30 Pm</p>
                                                    <span class="status">03</span>
                                                </div>
                                                <?php   $Message_user = App\MessageViewed::where('group_id',Auth::id())->where('receiver',Auth::id())->where('viewed',0)->count(); ?>
                                                @if($Message_user > 0)
                                                    <span style="background-color: red;" class="badge badge-xs badge-danger">{{ $Message_user }}</span>
                                                @endif
                                            </a></li>
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-8 col-md-7">
                    <div class="users_chats">
                        <div class="chats_head">
                            <div class="profile user_profile">
                                <div class="back_btn">
                                    <button type="button" class='btn btn_black chat_back_btn'><i class="fa-solid fa-chevron-left"></i></button>
                                </div>
                                <div class="product_details">
                                    <div class="image user_img">
                                        <img src="{{asset('storage')}}/uploads/users/no_avatar.jpg" class="img-fluid img-circle person-image">
                                    </div>
                                </div>
                                {{--                            <div class="desc">--}}
                                {{--                                <h5><span id="user-name"></span></h5>--}}
                                {{--                                <div class="bid" id="offer_dev">--}}
                                {{--                                </div>--}}
                                {{--                            </div>--}}
                                {{--                            <div class="bid_button" id="makeOfferDiv"></div>--}}
                                <div class="user_name">
                                    <h5 id="user_name_top"></h5>
                                    <p></p>
                                </div>
                            </div>
                            <div class="dispute_btn">
                                <button type="button" id="resolve_dispute_button" class="btn btn_red" data-toggle="modal" data-target="#resolve_dispute">Resolve Dispute</button>
                                <p id="dispute_decision"></p>
                            </div>
                        </div>
                        <div class="vertical-content chat_messages">
                            <div class="tab-content scrollbar" id="chat-area">
                                <div class="tab-pane active " id="tab">
                                    <h2><span id="user-name"></span></h2>
                                    <div id='get_all_users'  class="get_all"> </div>
                                </div>
                            </div>
                        </div>
                        <div class="preview_upload_file"></div>
                        <div class="row text-bar-row" id="text_box_area">
                            <div class="col-md-12">
                                <div class="send_msg">
                                    <div class="input-group">
                                        {{--     <input type="text-area" class="form-control text_message_area" placeholder="Write Your Message" aria-label="" aria-describedby="button-addon2"> --}}
                                        <div style="display: none;" id="attempt" > </div>
                                        <textarea rows="1" class="form-control text_message_area"
                                                  placeholder="Write Your Message" id="message-line"></textarea>
                                        <form id="uploadImage" method="post" action="{{ route('uplaod_image_cus') }}">
                                            {{ csrf_field() }}
                                            <div class="avatar-upload">
                                                <div class="avatar-edit">
                                                    <input type='file' id="uploadFile" name="uploadFile" accept=".png, .jpg, .jpeg, .pdf, .pptx, .docx, .doc" />
                                                    <label for="uploadFile" >
                                                        <img id="uploadFile_preview" src="{{ asset('website') }}/assets/images/file_upload.png">
                                                        {{-- <i class="fa-solid fa-plus"></i> --}}
                                                    </label>
                                                </div>
                                            </div>
                                        </form>
                                        <button class="btn btn_blue send_message send-button" type="button" disabled data-group=""
                                                id="message_submit_btn"><i class="fa-solid fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @include('message.message.chat-box')
        <input type="hidden" id="current_user" value="{{ \Auth::user()->id??'' }}" />
        <input type="hidden" id="pusher_app_key" value="{{ env('PUSHER_APP_KEY') }}" />
        <input type="hidden" id="pusher_cluster" value="{{ env('PUSHER_APP_CLUSTER') }}" />
    </section>

    <div class="modal fade credit_details_modal resolve_dispute" id="resolve_dispute" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="exampleModalLabel1">Resolve Dispute</h3>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form  method="post" action="{{ route('decision_dispute_staff') }}">
                        <div class="row">
                            {{ csrf_field() }}
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>My Decision is in favor of</label>
                                    <input type="hidden" name="resolve_milestone_id" id="resolve_milestone_id">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="custom_modal_radio">
                                    <input class="form-check-input" type="radio" name="decision" value="Service Provider"  id="flexCheckChecked1" checked>
                                    <label for="flexCheckChecked1">Service Provider</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="custom_modal_radio">
                                    <input class="form-check-input" type="radio" name="decision" value="Buyer" id="flexCheckChecked2" >
                                    <label for="flexCheckChecked2">Buyer</label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="transferred_btn">
                                    <button type="submit" class="btn btn_black">Submit<i class="fa-solid fa-check"></i></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('js')
<script src="https://js.pusher.com/4.1/pusher.min.js"></script>
<script src="{{ asset('js/chat.js') }}"></script>
<script src="https://malsup.github.io/jquery.form.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
<script>
    $(document).ready(function () {
        $('.send_msg button').attr('disabled','disabled');
        $('.send_msg input[type="text"]').keyup(function(){
            if($(this).val().length > 0){
                $('.send_msg button').removeAttr('disabled');
            }
            else if($(this).val().length == 0){
                $('.send_msg button').attr('disabled','disabled');
            }
        });

        $(document).on("click", ".custom_chats .users_chats .send_message", function () {
            const $chatMessages = $('.custom_chats .users_chats .chat_messages');
            if ($chatMessages.length > 0) {
                $chatMessages.animate({
                    scrollTop: $chatMessages[0].scrollHeight
                }, 2000);
            }
        });

        $(window).on('load', function () {
            // Function to activate the first chat on page load
            function activateFirstChat() {
                // $(".users_chats").hide(); // Hide all chat sections initially
                
            }

            // Call the function after everything has loaded
            activateFirstChat();

            // Handle click event for dynamically toggling the active class
            $(document).on("click", ".custom_chats .all_users_chats a.chat-box-toggle", function () {
                // Remove the 'actived' class from all chats
                $(".custom_chats .all_users_chats a.chat-box-toggle").removeClass("actived");
                // Add the 'actived' class to the clicked chat
                $(this).addClass("actived");
                $(".custom_chats .users_chats .chat_messages").animate({
                    scrollTop: $('.custom_chats .users_chats .chat_messages').get(0).scrollHeight
                }, 2000);
            });

        });

        $(".searchbar_input .custom_search_box").on("keyup", function () {
            var searchValue = $(this).val().toLowerCase();

            $(".custom_chats .all_users_chats .user_profile_msg").each(function () {
                var titleText = $(this).find(".user_msg h6").text().toLowerCase();

                if (titleText.includes(searchValue)) {
                    $(this).closest('.custom_chats .all_users_chats li').show();
                } else {
                    $(this).closest('.custom_chats .all_users_chats li').hide();
                }
            });
        });
        $(".searchbar_input .custom_search_box").on("input", function () {
            if ($(this).val() === "") {
                $(".custom_chats .all_users_chats li").show();
            }
        });

        $(window).on('load', function(){
            $(".custom_chats .users_chats .chat_messages").animate({
                scrollTop: $('.custom_chats .users_chats .chat_messages').get(0).scrollHeight
            }, 2000);
        });
        $(".preview_upload_file").hide();
    });
</script>
<script>
    $('#text_box_area').hide();
    //get all messages
    var item;
    function Acceptmessage(status,message_id,user_id,group,item,sender){
        $.ajax({
            type: 'POST',
            url: "{{route('accept_message')}}",
            data: {
                '_token'        :   '{{csrf_token()}}',
                status         :   status,
                message_id         :   message_id,
            },
            success: function(result) {
                getallmessages(user_id,group,item,sender)

            },
            error : function(error) {
                console.log(error);
            }
        });
    }
    function getOfferDiv(item,user_id,sender){
    }
    let displayedMessages = {};
    function getallmessages(user_id,group,item,sender){
        $.ajax({
            type: 'POST',
            url: "{{route('get_message_process_cus')}}",
            data: {
                '_token'        :   '{{csrf_token()}}',
                user_id         :   user_id,
                group         :   group,
                item         :   item,
            },
            success: function(result) {
                // alert(result);
                // console.log(displayedMessages)

                $('#get_all_users').html(result);
                $(".accept_reject_msg .custom_btn").show();
                $('#get_all_users').html(result);
                $('#message_submit_btn').attr('user-id',user_id);
                $('.scrollbar').scrollTop($('.scrollbar')[0].scrollHeight);
                // messageCount()
                // get_side_bar_user()

                // $('#get_all_users').animate({ scrollTop: $('.msg_container:last').offset().top }, 'slow');
            },
            error : function(error) {
                console.log(error);
            }
        });
        if(interval){
            clearInterval(interval);
        }
        timer(user_id,group,item,sender);
    }
    // $(document).ready(){
    //     setTimeout(updateChat, 500);
    // }
    user_id = null;
    interval = null;
    $('.image').hide();
    // $('#resolve_dispute_button').hide();
    $(".users_chats .back_btn").hide();
    $(document).on('click','.chat-box-toggle',function(){
        $(this).closest('li').find('.badge-danger').hide();
        // image = $(this).closest('li').find('img').attr('src');
        image = $(this).attr('data-image');
         self_resolved   = $(this).attr('data-self_resolved');
        user_name_top = $(this).attr('data-user-name');
        user_name_id = $(this).attr('data-user-id');
        $(this).closest('ul').find('a.actived').removeClass("actived");
        $(this).addClass("actived");
        var user_name   = $(this).attr('data-user');
        milestone_id   = $(this).attr('data-milestone_id');
        dispute_status   = $(this).attr('data-milestone-status');
        dispute_decision   = $(this).attr('data-dispute-decision');
        sender   = $(this).attr('data-sender');
        item     = $(this).attr('data-item');
        user_id     = $(this).attr('data-id');
        group     = $(this).attr('data-group');

        $('#message_submit_btn').attr('data-group',group)
        $('#message_submit_btn').attr('data-item',item)
        $('#message_submit_btn').attr('data-sender',sender)
        $('#resolve_milestone_id').val(milestone_id)
        // $(".Chat_box .tab-col").hide();
        // $(".content-col").show();
        //          $(".content-col .chat_back_btn").show();
        group
        if(group == 1){
            $('#user-name').html(user_name);
            $('#user_name_top').html(user_name_top);
        }else{
            $('#user_name_top').html(user_name_top);
            // $('#user-name').html('<a href=" route('product_detail') /'+item+'">'+user_name+'</a>');
            // $('#user_name_top').html('<a href=" route('saller_profile')/'+user_name_id+'">'+user_name_top+'</a>');
            // $('.product_anchor').attr('href',' route('product_detail') /'+item+'');
        }

        var windowWidth = $(window).width();

        if (windowWidth < 992) {
            $(".custom_chats .chats_detail").hide();
            $(".users_chats").show();
            $(".users_chats .back_btn").show();
        } else {
            $(".users_chats").show();
            $(".custom_chats .chats_detail").show();
        }

        $('.person-image').attr('src',image);
        $('.image').show();
        if(dispute_status == 0 || dispute_status == ''){
               $('#resolve_dispute_button').show();
               $('#text_box_area').css("display", "flex");
               $('#dispute_decision').html(dispute_decision)
        }else{
               $('#resolve_dispute_button').hide();
               $('#escalate_to_staff').hide();
               $('#text_box_area').hide();
              if(self_resolved == 1){
                    $('#dispute_decision').html(dispute_decision)
              }else{
                    $('#dispute_decision').html("The decision is in favor of the "+dispute_decision)
              }  
        }

        getallmessages(user_id,group,item,sender);
        getOfferDiv(item,user_id,sender)
    });
    $(window).on('resize', function() {
        var windowWidth = $(this).width();

        if (windowWidth < 992) {
            $(".custom_chats .chats_detail").hide();
            $(".users_chats").show();
            $(".users_chats .back_btn").show();
        } else {
            $(".users_chats").show();
            $(".custom_chats .chats_detail").show();
        }
    });

    $(".users_chats .chat_back_btn").on( "click", function() {
        $(".custom_chats .chats_detail").show();
        $(".users_chats").hide();
        $(".users_chats .back_btn").hide();
    });

    if(sessionStorage.getItem("noti")){
        user_id = sessionStorage.getItem("noti")
        getallmessages(user_id);
        $('.chat-box-toggle[data-id='+user_id+']').addClass("actived");
        $('#text_box_area').css("display", "flex");
        $('.chat-box-toggle[data-id='+user_id+']').trigger('click');
        sessionStorage.removeItem('noti');
    }
    function timer(user_id,group,item,sender){
        if(user_id != null){
//            interval = setInterval(function () {
//               getallmessages(user_id,group,item,sender);
//               getOfferDiv(item,user_id,sender)
//           },9000);
        }

    }

    //post message
    $(document).on('click','#group_create',function(){
        if($(this).attr('data-show') == '1'){
            $('.Chat_group').css('display','block');
            $(this).attr('data-show','0')
        }else{
            $('.Chat_group').css('display','none');
            $(this).attr('data-show','1')
        }
    });
    $(document).on('click','.back_group_modal',function(){
        $('.Chat_group').css('display','none');
    });

    $(document).on('click','.back_group_modal',function(){
        $('.Chat_group_edit').css('display','none');
    });

    $(document).on('click','#message_submit_btn',function(){
        // console.log($(this));
        $(this).prop('disabled', true);
        $('#uploadFile_preview').attr('src', '{{ asset('website') }}/assets/images/file_upload.png');
        $(".preview_upload_file").hide();
        var message = $('#message-line').val().replace("\n", "<br/>");
        var attempt = $('#attempt').html();
        message =   attempt+message;
        var to_user_id = $(this).attr('user-id');
        var group = $(this).attr('data-group');
        item = $(this).attr('data-item')
        if($.trim(message) === "" || $.trim(message) === "<br/>"){

        }else{

            $.ajax({
                type: 'POST',
                url: "{{route('send_message_process_cus')}}",
                data: {
                    '_token'        :   '{{csrf_token()}}',
                    to_user_id      :   to_user_id,
                    message         :   message,
                    group           :   group,
                    item           :   item,

                },
                success: function(result) {

                    $('#message-line').val('');
                    $('#message-line').html('');
                    $('#attempt').html('')
                    // setTimeout(getallmessages(to_user_id), 500);
                    getallmessages(to_user_id,group,item);
                },
                error : function(error) {
                    // showSwal('OOPS',"Unable to Complete Request.",'error');
                }
            });
        }

    });
    // $('#message-line').keypress(function(e) {
    //     if(e.which == 13) {
    //         jQuery('#message_submit_btn').click();
    //     }
    // })
    $('#message-line').keydown(function (e) {
        if (e.keyCode === 13 && e.ctrlKey) {
            //console.log("enterKeyDown+ctrl");
            $(this).val(function(i,val){
                return val + " \n";
            });
        }
    }).keypress(function(e){
        if (e.keyCode === 13 && !e.ctrlKey) {
            jQuery('#message_submit_btn').click();
            // $('#message_submit_btn').off('click');
            $('#message-line').val('');
            console.log($('#message_submit_btn'))
            // Disable the button
        }
    });
</script>

<script type="text/javascript">
    $(document).ready(function () {

        // if (!$.browser.webkit) {
        //     $('.chat-area').html('<p>Sorry! Non webkit users. :(</p>');
        // }
    });
</script>

<script type="text/javascript">
    $(document).ready(function(){
        var checkboxes = $('.checkboxes');
        checkboxes.change(function(){
            if($('.checkboxes:checked').length>0) {
                checkboxes.removeAttr('required');
            } else {
                checkboxes.attr('required', 'required');
            }
        });
    });


    function messageCount(){
        $.ajax({
            type: 'get',
            url: "",
            success: function(result) {
                $('#mess_count').html(parseInt(result.count));
            },
            error : function(error) {
                // showSwal('OOPS',"Unable to Complete Request.",'error');
            }
        });
    }



    $("input[name='query']").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $(".contacts").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

    $('#uploadFile').on('change', function(e){
        $('#uploadFile_preview').attr('src','{{ asset('website') }}/images/loading (2).gif')
        var file = e.target.files[0]
        $('#uploadImage').ajaxSubmit({
            target: "#attempt",
            resetForm: true,
            success:    function() {
                if(file.type == "image/png" || file.type == "image/jpeg"|| file.type == "image/jpg"){
                    // Create a temporary URL for the image file
                    var imageUrl = URL.createObjectURL(file);

                    // Dynamically add the image preview to a custom div
                    $('.preview_upload_file').html(`<div class="custom_img_upload"><div class="image_preview_wrapper"><img src="${imageUrl}" class="custom-preview-img" alt="Preview Image"></div><button type="button" class="remove_preview"><i class="fa-solid fa-close"></i></button></div>`);

                    // Revoke the URL to free up memory when the image is loaded
                    $('.custom-preview-img').on('load', function () {
                        URL.revokeObjectURL(imageUrl);
                    });

                    // Update the #uploadFile_preview element
                    var output = document.getElementById('uploadFile_preview');
                    output.src = imageUrl;

                    // Revoke the URL to free memory after loading in #uploadFile_preview
                    output.onload = function () {
                        URL.revokeObjectURL(output.src); // free memory
                    };
                }else{
                    $('#uploadFile_preview').attr('src','{{ asset('website') }}/images/loaded.png');
                }
                $(".preview_upload_file:has(.image_preview_wrapper)").show();
                $(document).on("click",".preview_upload_file button.remove_preview",function(){
                    $(".preview_upload_file").empty(); // Remove the preview elements
                    $('#uploadFile_preview').attr('src','{{ asset('website') }}/assets/images/file_upload.png');
                    $('#uploadFile').val(''); // Reset the file input
                })
            }
        });
    });

</script>
<script>
    $(".fancybox1").fancybox({

    });

    $(document).on('click','.accept_button',function(){
        // $(this).remove();
        let  id = $(this).attr('data-id')
        let  price = $(this).attr('data-price')
        let  group = $(this).attr('data-group')
        let  service = $(this).attr('data-service')
        let  itemPrice = $(this).attr('data-item-price')
        let minusService = parseInt(itemPrice) - parseInt(service);
        $('#group_id').val(group)
        $('#offer_price').val(price)
        $('#offer_price2').val(parseInt(price) - parseInt(service))
        $('#item_id').val(id)
        $('#service_html').html('Le montant que vous avez négocié avec l’acheteur est déduit du prix original de <span>€ ' + minusService + '</span> que vous avez renseigné lors de la publication de votre article.');
        // Vous recevrez <span class="price">€ ' + (parseInt(price) - parseInt(service)) + '</span>

        $('#offer_modal').modal('show');

    });
    @if(count($groups) > 0)
get_side_bar_user()
//                    setInterval(function () {
//                        get_side_bar_user()
//                   },9000);
    @endif
    function get_side_bar_user(){
        @if (isset($job_id))
            job_id = {{ $job_id }};
        @else
            job_id = 0;
        @endif
$.ajax({
            type: 'get',
            url: "{{route('get_side_bar_user')}}",
            data:{job_id:job_id},
            success: function(result) {

                $('.chat_users_show').html(result)
                $(".custom_chats .all_users_chats li:first-child a.chat-box-toggle").click().addClass('actived');
            },
            error : function(error) {
                // showSwal('OOPS',"Unable to Complete Request.",'error');
            }
        });
    }

    // Pusher subscription for real-time sidebar updates
    var pusher = new Pusher('{{ env('PUSHER_APP_KEY') }}', {
        cluster: '{{ env('PUSHER_APP_CLUSTER') }}',
        forceTLS: true
    });
    // Subscribe to user-specific sidebar channel
    var userSpecificChannel = 'sidebar-user-channel-{{ Auth::id() }}';
    var channel = pusher.subscribe(userSpecificChannel);
    channel.bind('user-added', function(data) {
        get_side_bar_user();
    });

</script>
<script>
    $(document).ready(function(){
        var messageInput = $('#message-line');
        var sendButton = $('#message_submit_btn');
        messageInput.on('input', function() {
            if (messageInput.val().length > 0) {
                sendButton.prop('disabled', false);
            } else {
                sendButton.prop('disabled', true);
            }
        });
        var chatWindow = $('#chat-area');
        sendButton.on('click', function() {
            chatWindow.animate({ scrollTop: chatWindow[0].scrollHeight }, 500);
        });


        $('.accept_reject_msg button').click(function() {
            var btnValue = $(this).val();
            if (btnValue === "accepted") {
                console.log(btnValue);
                $('#get_all_users').show();
                $(this).closest('.tab-pane').find(".accept_reject_msg").hide();
            }
            else if (btnValue === "rejected") {
                $(this).closest('.tab-pane').find(".accept_reject_msg .new_message h5").html("Message Has Been Rejected");
                $(this).closest('.tab-pane').find(".accept_reject_msg .custom_btn").hide();
                console.log(btnValue);
            };
        });
    });
</script>
@endpush
