<?php $__env->startPush('css'); ?>


<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
    <div  class="topbar">
        <h2 id="">Projects</h2>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <section class="document_section custom_label">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="custom_projects_tabs">
                        <div class="custom_search_filter custom_flex">
                            <div class="txt_field custom_search">
                                <input type="text" placeholder="Search" class="custom_search_box">
                            </div>
                            <div class="filter_btn">
                                <button type="button" class="btn btn_black">
                                    Filter<img src="<?php echo e(asset('website')); ?>/assets/images/filter.png"></button>
                            </div>
                        </div>
                        <div class="row custom_row">
                           <?php $__currentLoopData = $group_chat; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 col-sm-12 col-xs-12">
                                <div class="document_details">
                                    <div class="row custom_row">
                                        <div class="col-md-6 col-sm-6 col-xs-12">
                                            <div class="custom_title">
                                                <div class="custom_categories staff_categories">
                                                    <a href="javascript:void(0);" class="">Title :  <?php echo e($element->getItem->project_title??''); ?> </a>
                                                </div>
                                                <h6>Client Name:</h6>
                                                <h3><?php echo e($element->getItem->user->name); ?></h3>
                                                <label>Email: <span><?php echo e($element->getItem->user->email); ?></span></label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6 col-xs-12">
                                            <div class="custom_title">
                                                <h6>Contractor Name:</h6>
                                                <h3><?php echo e($element->getItem->jobOffersPaid->getStaffDetail->name??''); ?></h3>
                                                <label>Email: <span><?php echo e($element->getItem->jobOffersPaid->getStaffDetail->email??''); ?></span></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="milestone_disputed">
                                        <label>Milestones Disputed:<span><?php echo e($milestoneIds = $number_of_chat->where('admin_id', $element->admin_id)->pluck('milestone_id')->implode(' & ')); ?></span></label>
                                    </div>
                                    <div class="mt_20">
                                        <a href="<?php echo e(url('staff_chat')); ?>/<?php echo e($element->getItem->id); ?>" class="btn btn_black">View <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_up.png"></a>
                                    </div>
                                </div>
                            </div>
                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>

<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/dashboard/staff/staff_dispute.blade.php ENDPATH**/ ?>