<?php $__env->startPush("css"); ?>
    <link rel="stylesheet" href="<?php echo e(asset('plugins/components/dropify/dist/css/dropify.min.css')); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
    <style>
        .custom_justify{display: flex;justify-content: space-between;align-items: center}
        .custom_justify a{color:#4a4a4a;    font-family: 'LeagueSpartan-Regular';}
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="posted_view_page sp_explore_page" >
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    
                    
                    
                    
                    
                    
                    
                </div>
                <div class="col-md-4">
                    <div class="listing_section">
                        <p style="color: #000;font-size:15px">Fixed Bid</p>
                        <?php if(isset($job->jobOffers) && !empty($job->jobOffers) && $job->jobOffers->count() > 0 ): ?>
                            <?php
                                $userOffers = $job->jobOffers->where('staff_id', auth()->user()->id);
                                $otherOffers = $job->jobOffers->where('staff_id', '!=', auth()->user()->id);
                            ?>
                            <?php $__currentLoopData = $userOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="post_list first_post staff_first_post fixed_bid_color">
                                    <div class="dflex">
                                        <div class="profile_name_inline">
                                            <div class="post_profile">
                                                <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($offer?->getStaffDetail?->profile->pic??''); ?>" alt="">
                                            </div>
                                            <h5><?php echo e($offer?->getStaffDetail->name ?? '---'); ?></h5>
                                        </div>
                                        <h6><?php echo e($offer->created_at->format('d-m-Y H:i')); ?> </h6>
                                    </div>
                                    <div class="custom_justify">
                                        <div>



                                            <h5>Labor Expense: $<?php echo e($offer->labour_expense ?? '0'); ?></h5>
                                            <h5>Materials Expense: $<?php echo e($offer->material_expense ?? '0'); ?></h5>
                                            <h5>Total Bid : $<?php echo e($offer->getJobDetail->jobMilestone->where('staff_id',auth()->user()->id)->sum('amount')); ?></h5>



                                            


                                        </div>

                                        <?php if($offer->status != 'accepted'): ?>
                                            <a href="<?php echo e(url("services_bid_offer/$job->id")); ?>" class="view_link">View/Edit</a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                            <?php $__currentLoopData = $otherOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="post_list first_post">
                                    <div class="dflex">
                                        <div class="profile_name_inline">
                                            <div class="post_profile">
                                                <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($offer?->getStaffDetail?->profile->pic??''); ?>" alt="">
                                            </div>
                                            <h5><?php echo e($offer->getStaffDetail->name ?? '---'); ?></h5>
                                        </div>
                                        <h6><?php echo e($offer->created_at->format('d-m-Y H:i')); ?></h6>
                                    </div>
                                    <div class="custom_justify">
                                        <?php if(auth()->user()->hasRole('buyer')): ?>
                                            <div>

                                                <?php if($offer->range_or_fixed == 'yes'): ?>
                                                    <h5>Total Bid: $ <?php echo e($offer->min_amount ?? '0'); ?> - $<?php echo e($offer->max_amount ?? '0'); ?></h5>
                                                <?php else: ?>
                                                    <h5>Total Bid: $ <?php echo e($offer->amount ?? '0'); ?></h5>
                                                <?php endif; ?>
                                                <h5>Labor Expense: $<?php echo e($offer->labour_expense ?? '0'); ?></h5>
                                                <h5>Materials Expense: $<?php echo e($offer->material_expense ?? '0'); ?></h5>
                                            </div>
                                        <?php else: ?>
                                            <?php if($offer->hide_bid_amount == 'no'): ?>
                                                <div>

                                                    <?php if($offer->range_or_fixed == 'yes'): ?>

                                                        <h5>Total Bid: $ <?php echo e(($offer->labour_expense ?? 0) + ($offer->material_expense ?? 0)); ?> </h5>
                                                    <?php else: ?>

                                                        <h5>Total Bid: $ <?php echo e(($offer->labour_expense ?? 0) + ($offer->material_expense ?? 0)); ?> </h5>

                                                    <?php endif; ?>
                                                    <h5>Labor Expense: $<?php echo e($offer->labour_expense ?? '0'); ?></h5>
                                                    <h5>Materials Expense: $<?php echo e($offer->material_expense ?? '0'); ?></h5>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="post_list first_post staff_first_post">
                                <h6>No Fixed Bid Offer Found</h6>
                            </div>
                        <?php endif; ?>
                        
                            <div class="estimate_bid_section">
                                <p >Estimate Bid</p>
                            </div>
                        <?php if(isset($job->estimateOffers) && !empty($job->estimateOffers) && $job->estimateOffers->count() > 0): ?>
                            <?php
                                $userEstimateOffers = $job->estimateOffers->where('seller_id', auth()->user()->id);
                                $otherEstimateOffers = $job->estimateOffers->where('seller_id', '!=', auth()->user()->id);
                            ?>
                            <?php $__currentLoopData = $userEstimateOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="post_list first_post staff_first_post estimate_bid_color">
                                    <div class="dflex">
                                        <div class="profile_name_inline">
                                            <div class="post_profile">
                                                <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($offer?->getSellerDetail?->profile->pic??''); ?>" alt="">
                                            </div>
                                            <h5><?php echo e($offer->getSellerDetail->name ?? '---'); ?></h5>
                                        </div>
                                        <h6><?php echo e($offer->created_at->format('d-m-Y H:i')); ?> </h6>
                                    </div>
                                    <div class="custom_justify">

                                        <?php
                                            $labour = (float) ($offer->labour_expense ?? 0);
                                            $priceDisplay = ($offer->min_amount && $offer->max_amount)
                                                ? '$' . number_format($labour + $offer->min_amount, 2) . ' --- $' . number_format($labour + $offer->max_amount, 2)
                                                : '$' . number_format($labour + ($offer->amount ?? 0), 2);
                                        ?>

                                        <div>



                                            <h5>Labor Expense:  $<?php echo e($offer->labour_expense ?? '0'); ?></h5>
                                            <?php if($offer->range_or_fixed == 'yes'): ?>
                                                <h5>Materials Expense: $ <?php echo e($offer->min_amount ?? '0'); ?> - $<?php echo e($offer->max_amount ?? '0'); ?></h5>
                                                <h5>Total Bid: <?php echo e($priceDisplay); ?></h5>
                                            <?php else: ?>
                                                <h5>Materials Expense Bid: $ <?php echo e($offer->amount ?? '0'); ?></h5>
                                                <h5>Total Bid:  <?php echo e($priceDisplay); ?></h5>
                                            <?php endif; ?>
                                            <?php if(!empty($offer->material_expense)): ?>
                                            <h5>Materials Expense: $<?php echo e($offer->material_expense ?? '0'); ?></h5>
                                            <?php endif; ?>
                                        </div>

                                        
                                        <a href="<?php echo e(url("estimate_bid_offer/$job->id")); ?>" id="editOrFixed" class="view_link">View/Edit</a>
                                        

                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            
                            <?php $__currentLoopData = $otherEstimateOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="post_list first_post estimate_bid_color">
                                    <div class="dflex">
                                        <div class="profile_name_inline">
                                            <div class="post_profile">
                                                <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($offer?->getSellerDetail?->profile->pic??''); ?>" alt="">
                                            </div>
                                            <h5><?php echo e($offer->getSellerDetail->name ?? '---'); ?></h5>
                                        </div>
                                        <h6><?php echo e($offer->created_at->format('d-m-Y H:i')); ?></h6>
                                    </div>
                                    <div class="custom_justify">
                                        <?php if(auth()->user()->hasRole('buyer')): ?>
                                            <div>

                                                <?php if($offer->range_or_fixed == 'no'): ?>
                                                    <h5>Total Bid: $ <?php echo e($offer->min_amount ?? '0'); ?> --- Buyer$<?php echo e($offer->max_amount ?? '0'); ?></h5>
                                                <?php else: ?>
                                                    <h5>Total Bid: $ <?php echo e($offer->amount ?? '0'); ?></h5>
                                                <?php endif; ?>
                                                <h5>Labor Expense: $<?php echo e($offer->labour_expense ?? '0'); ?></h5>
                                                <h5>Materials Expense: $<?php echo e($offer->material_expense ?? '0'); ?></h5>
                                            </div>
                                        <?php else: ?>
                                            <?php if($offer->hide_bid_amount == 'no'): ?>
                                                <div>
                                                    <?php
                                                        $labour = (float) ($offer->labour_expense ?? 0);
                                                        $otherPrice = ($offer->min_amount && $offer->max_amount)
                                                            ? '$' . number_format($labour + $offer->min_amount, 2) . ' --- $' . number_format($labour + $offer->max_amount, 2)
                                                            : '$' . number_format($labour + ($offer->amount ?? 0), 2);
                                                    ?>


                                                    <h5>Labor Expense: $<?php echo e($offer->labour_expense ?? '0'); ?></h5>
                                                    <?php if($offer->range_or_fixed == 'yes'): ?>
                                                        <h5>Materials Expense: $ <?php echo e($offer->min_amount ?? '0'); ?> ---Seller $<?php echo e($offer->max_amount ?? '0'); ?></h5>
                                                        <h5>Total Bid : <?php echo e($otherPrice); ?> seelerr</h5>
                                                    <?php else: ?>
                                                        <h5>Materials Expense: $ <?php echo e($offer->amount ?? '0'); ?></h5>
                                                        <h5>Total Bid :<?php echo e($otherPrice); ?> </h5>
                                                    <?php endif; ?>
                                                    <?php if(!empty($offer->material_expense)): ?>
                                                        <h5>Materials Expense: $<?php echo e($offer->material_expense ?? '0'); ?></h5>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="post_list first_post staff_first_post">
                                <h6>No Estimate Bid Offer Found</h6>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="offer_contact_btn">









                        <?php if(!$job->jobOffers()->where('staff_id', auth()->user()->id)->exists() && !$job->estimateOffers()->where('seller_id',auth()->user()->id)->exists()): ?>





                            <a href="#" class="btn btn_blue btn_has_icon make_your_offer_start" id="make_offer_btn">
                                Make your Offer
                                <div class="btn_icon">
                                    <i class="fa-solid fa-arrow-right"></i>
                                </div>
                            </a>
                        <?php endif; ?>
                        <?php if(isset($job->getChat->id) && $job->getChat->status == 0): ?>
                            <button  disabled class="btn btn_black btn_has_icon">Request Pending <div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></button>
                        <?php elseif(isset($job->getChat->id) && $job->getChat->status == 1): ?>
                            <a href="<?php echo e(route('service_provider_chat')); ?>/<?php echo e($job->getChat->group_id); ?>" class="btn btn_black btn_has_icon">Message<div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></a>
                        <?php else: ?>
                            <a href="<?php echo e(route('request_contact')); ?>/<?php echo e($job->id); ?>" class="btn btn_black btn_has_icon">Request Contact <div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row custom_rowGap">
                        <div class="col-md-12">
                            <div class="explore_card_user">
                                <div class="custom_categories">
                                    <a href="javascript:void(0)" class="" style="pointer-events: none; cursor: default;">
                                        Title :  <?php echo e($job->project_title ?? '---'); ?> </a>
                                </div>
                                <div class="logo_and_name">
                                    <div class="logo_content_rating">
                                        <div class="img_container">
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/inside_logo.png" alt="">
                                        </div>
                                        <h3><?php echo e($job->user->name??'---'); ?></h3>
                                    </div>
                                    <div class="rating_star">
                                        <div class="seller_rating">
                                            <span class="fa fa-star <?php if($job->user->ratingSum >= 1): ?> checked <?php endif; ?>"></span>
                                            <span class="fa fa-star <?php if($job->user->ratingSum >= 2): ?> checked <?php endif; ?>"></span>
                                            <span class="fa fa-star <?php if($job->user->ratingSum >= 3): ?> checked <?php endif; ?>"></span>
                                            <span class="fa fa-star <?php if($job->user->ratingSum >= 4): ?> checked <?php endif; ?>"></span>
                                            <span class="fa fa-star <?php if($job->user->ratingSum >= 5): ?> checked <?php endif; ?>"></span>
                                            <h6><?php echo e($job->user->ratingSum); ?> Stars</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="card_info">
                                <h5>Email: <span> <?php echo e($job->user->email ?? '---'); ?> </span></h5>
                                <h5>Posted On: <span> <?php echo e($job->created_at->format('d-m-y') ??'---'); ?> </span></h5>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="custom_categories">
                                <a href="javascript:void(0)" class="" style="pointer-events: none; cursor: default;">
                                    <?php echo e($job->category->name ?? '----'); ?> / <?php echo e($job->subCategory->name ?? '----'); ?></a>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <h5>Budget:</h5>
                            <h3>$<?php echo e($job->project_budget_min??'0'); ?> - $<?php echo e($job->project_budget_max??'0'); ?></h3>
                        </div>

                        <div class="col-md-12">
                            <h5>Photos</h5>
                            <div class="row custom_row">
                                <?php if(!empty($job->jobFiles)): ?>
                                    <?php $__currentLoopData = $job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $fileUrl = $file->file ?? '---';
                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                        ?>
                                        <div class="col-md-3 col-sm-4 col-6">
                                            <?php if($isImage): ?>
                                                <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                    <div class="project_photos">
                                                        <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid" loading="lazy">
                                                    </div>
                                                </a>
                                            <?php elseif($isVideo): ?>
                                                <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery" data-type="video">
                                                    <div class="project_photos">
                                                        <video controls preload="metadata" class="img-fluid video-player">
                                                            <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                            Your browser does not support this video format. <a href="<?php echo e($fileUrl); ?>" download>Download the video</a>
                                                        </video>
                                                    </div>
                                                </a>
                                            <?php else: ?>
                                                <div class="project_photos">
                                                    <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if(!empty($job->jobQuestionAnswer)): ?>
                            <?php $__currentLoopData = $job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-12">
                                    <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                    <?php
                                        $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                    ?>
                                    <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="gallery">
                                        <div class="questionnaire_img">
                                            <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp|heic)$/i', $value)): ?>
                                                <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="max-width: 100%;">
                                            <?php else: ?>
                                        </div>
                                    </a>
                                         <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                         <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>



















                                <?php
                                $hasDocuments = $job->jobAssignStaffDocuments && $job->jobAssignStaffDocuments->isNotEmpty()
                                ?>

                            <?php if($hasDocuments): ?>
                                <div class="col-md-12">
                                    <div class="project_detail">
                                        <div class="">
                                            <h3>Staff Further Details</h3>
                                        </div>
                                        <div class="">
                                            <h5>Media</h5>
                                        </div>
                                        <div class="media_download">
                                            <?php $__currentLoopData = $job->jobAssignStaffDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    $fileUrl = $file->image ?? '---';
                                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                ?>

                                                <?php if($isImage): ?>
                                                    <div class="images_with_text">
                                                        <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                            <div class="custom_images">
                                                                <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="">
                                                            </div>
                                                        </a>
                                                        <p><?php echo e($file->name??''); ?></p>
                                                    </div>
                                                <?php elseif($isVideo): ?>
                                                    <div class="images_with_text">
                                                        <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                            <div class="custom_images staff_video">
                                                                <div class="downloadable_video">
                                                                    <video controls preload="metadata" class="video-player">
                                                                        <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                        Your browser does not support this video format.
                                                                    </video>
                                                                </div>
                                                            </div>
                                                        </a>
                                                        <p><?php echo e($file->name??''); ?></p>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="custom_images">
                                                        <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>


                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
    <script>
        $(document).ready(function() {
            $('[data-fancybox="gallery"]').fancybox({
                protect: false,
                clickOutside: false,
                closeExisting: false,
            });
        });
        const fixedBidBtn = document.getElementById("editOrFixed");

        if (fixedBidBtn) {
            fixedBidBtn.addEventListener("click", function (e) {
                e.preventDefault();

                Swal.fire({
                    title: 'What type of bid do you want to make?',
                    showCancelButton: true,
                    showDenyButton: true,
                    confirmButtonText: 'Fixed Bid',
                    denyButtonText: 'Edit Estimate',
                    cancelButtonText: 'Cancel',
                    icon: 'question'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = '<?php echo e(url("services_bid_offer/$job->id")); ?>';
                    } else if (result.isDenied) {
                        window.location.href = '<?php echo e(url("estimate_bid_offer/$job->id")); ?>';
                    }
                });
            });
        }

        const offerBtn = document.getElementById("make_offer_btn");
        if (offerBtn) {
            offerBtn.addEventListener("click", function(e) {
                e.preventDefault();
                Swal.fire({
                    title: 'Do you want to make a Fixed Bid or Estimate?',
                    showCancelButton: true,
                    confirmButtonText: 'Fixed Bid',
                    cancelButtonText: 'Estimate',
                    icon: 'question'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = '<?php echo e(url("services_bid_offer/$job->id")); ?>';
                    } else if (result.dismiss === Swal.DismissReason.cancel) {
                        window.location.href = '<?php echo e(url("estimate_bid_offer/$job->id")); ?>';
                    }
                });
            });
        }
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home1/backendpro/public_html/aftab/mrdoall/resources/views/website/serviceProvider/explore_view_project.blade.php ENDPATH**/ ?>