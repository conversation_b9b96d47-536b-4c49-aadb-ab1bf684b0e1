<?php

namespace App\Http\Controllers;
use App\AssignEvent;
use App\Contact;
use App\EstimateOffer;
use App\Job;
use App\JobMilestone;
use App\JobOffer;
use App\AboutPage;
use App\CmsHomePage;
use App\ContactPage;
use App\Notification;
use App\Testimonial;
use App\PrivacyPolicy;
use App\TermAndCondition;
use App\PortfolioAttachment;
use App\RequestWithdrawal;
use App\Salutation;
use App\SellerExpertise;
use App\User;
use App\Profile;
use App\UserAttachment;
use App\JobQuestion;
use App\JobSubcategory;
use App\JobCategory;
use App\Message;
use App\GroupChat;
use App\DisputeMilestone;
use App\MessageViewed;
use Mockery\Exception;
use App\RatingReview;
use Session;
use Storage;
use App\Portfolio;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use ZipArchive;
use Laravel\Socialite\Facades\Socialite;

class WebsiteController extends Controller
{
    //website
    public function emailAuthentication(){
        return view("website.login.email_authentication");
    }
    public function resetPassword(){
        return view("website.login.reset_password");
    }
    public function registrationRole(){
        return view("website.login.registration_role");
    }

    public function finalizeProfile(Request $request){
        return view("website.login.finalize_profile");
    }//ends finalizeProfile

//    public function serviceProviderExplore(Request $request) {
//        $user = auth()->user();
//        if ($user->getUserAttachments()->where('status', 1)->exists()) {
//            $preferences = $user->getUserGeographicalPreference;
//            $restrictedStates = $user->getUserRestrictedState->pluck('state');
//
//            // Base job query
//            $jobsQuery = Job::where('status', 'posted')->orderBy('id', 'desc');
//
//            // Exclude restricted states
//            if ($restrictedStates->isNotEmpty()) {
//                $jobsQuery->whereNotIn('state', $restrictedStates);
//            }
//
//            // Apply geographical preferences with city matching and optional radius
//            if ($preferences && $preferences->isNotEmpty()) {
//                $jobsQuery->where(function ($query) use ($preferences) {
//                    foreach ($preferences as $preference) {
//                        $query->orWhere(function ($subQuery) use ($preference) {
//                            // Match by city (case-insensitive)
//
//                            $subQuery->whereRaw('LOWER(city) = ?', [strtolower($preference->city ?? '')]);
//
//                            // Apply radius filtering if coordinates are available
//                            if ($preference->latitude && $preference->longitude) {
//                                $latitude = $preference->latitude;
//                                $longitude = $preference->longitude;
//                                $radius = $preference->radius ?? 1760; // Default to 1 mile (1760 yards)
//                                $radiusInMiles = $radius / 1760;
//
//                                $subQuery->orWhereRaw("
//                                ( 3959 * acos(
//                                            cos(radians(?)) *
//                                            cos(radians(latitude)) *
//                                            cos(radians(longitude) - radians(?)) +
//                                            sin(radians(?)) *
//                                            sin(radians(latitude))
//                                ) ) <= ?
//                            ", [$latitude, $longitude, $latitude, $radiusInMiles])
//                                    ->whereNotNull('latitude')
//                                    ->whereNotNull('longitude');
//                            }
//                        });
//                    }
//                });
//            }
//            $userCategories = $user->userCategory()->where('status', 'approved')->get();
//            if ($userCategories->isNotEmpty()) {
//                $categoryIds = $userCategories->pluck('category_id')->toArray();
//                if (!empty($categoryIds)) {
//                    $jobsQuery->whereIn('category_id', $categoryIds);
//                }
//                \Log::info('User categories found: ' . implode(', ', $categoryIds));
//            }
//            // Get jobs with distance calculation for display
//            if ($preferences && $preferences->isNotEmpty()) {
//                $jobs = $jobsQuery->get()->map(function ($job) use ($preferences) {
//                    $minDistance = null;
//                    foreach ($preferences as $pref) {
//                        if ($job->latitude && $job->longitude && $pref->latitude && $pref->longitude) {
//                            $distance = 3959 * acos(
//                                    cos(deg2rad($pref->latitude)) *
//                                    cos(deg2rad($job->latitude)) *
//                                    cos(deg2rad($job->longitude) - deg2rad($pref->longitude)) +
//                                    sin(deg2rad($pref->latitude)) *
//                                    sin(deg2rad($job->latitude))
//                                );
//                            if (is_null($minDistance) || $distance < $minDistance) {
//                                $minDistance = $distance;
//                            }
//                        }
//                    }
//                    $job->distance = $minDistance ?? null;
//                    return $job;
////                })->sortBy('distance');
//                })->sortBy([
//                    ['created_at', 'desc']
////                    ['distance', 'asc']
//                ]);
//            } else {
//                $jobs = $jobsQuery->get();
//            }
//
//
//            return view('website.serviceProvider.explore', compact('jobs'));
//        }else{
//            $jobs = [];
//            return view('website.serviceProvider.explore', compact('jobs'));
//        }
//    } //old code by me
//

    public function serviceProviderExplore(Request $request)
    {
        $user = auth()->user();

        if (!$user->getUserAttachments()->where('status', 1)->exists()) {
            return view('website.serviceProvider.explore', ['jobs' => []]);
        }

        $preferences = $user->getUserGeographicalPreference;
        $restrictedStates = $user->getUserRestrictedState->pluck('state');
        $userCategories = $user->userCategory()->where('status', 'approved')->get();
        $categoryIds = $userCategories->pluck('category_id')->toArray();

        $jobsQuery = Job::where('status', 'posted')->orderBy('id', 'desc');

        if (!empty($categoryIds)) {
            $jobsQuery->whereIn('category_id', $categoryIds);
        }

        if ($restrictedStates->isNotEmpty()) {
            $jobsQuery->whereNotIn('state', $restrictedStates);
        }

        if ($preferences && $preferences->isNotEmpty()) {
            $jobsQuery->where(function ($query) use ($preferences) {
                foreach ($preferences as $preference) {
                    $query->orWhere(function ($subQuery) use ($preference) {
                        $subQuery->whereRaw('LOWER(city) = ?', [strtolower($preference->city ?? '')]);

                        if ($preference->latitude && $preference->longitude) {
                            $latitude = $preference->latitude;
                            $longitude = $preference->longitude;
                            $radiusInMiles = ($preference->radius ?? 1760) / 1760;

                            $subQuery->orWhereRaw("
                            (3959 * acos(
                                cos(radians(?)) *
                                cos(radians(latitude)) *
                                cos(radians(longitude) - radians(?)) +
                                sin(radians(?)) *
                                sin(radians(latitude))
                            )) <= ?
                        ", [$latitude, $longitude, $latitude, $radiusInMiles])
                                ->whereNotNull('latitude')
                                ->whereNotNull('longitude');
                        }
                    });
                }
            });
        }

        $jobs = $jobsQuery->get();

        // Optional distance calculation (only if preferences exist)
        if ($preferences && $preferences->isNotEmpty()) {
            $jobs = $jobs->map(function ($job) use ($preferences) {
                $minDistance = null;
                foreach ($preferences as $pref) {
                    if ($job->latitude && $job->longitude && $pref->latitude && $pref->longitude) {
                        $distance = 3959 * acos(
                                cos(deg2rad($pref->latitude)) *
                                cos(deg2rad($job->latitude)) *
                                cos(deg2rad($job->longitude) - deg2rad($pref->longitude)) +
                                sin(deg2rad($pref->latitude)) *
                                sin(deg2rad($job->latitude))
                            );
                        if (is_null($minDistance) || $distance < $minDistance) {
                            $minDistance = $distance;
                        }
                    }
                }
                $job->distance = $minDistance ?? null;
                return $job;
            })->sortBy([
                ['created_at', 'desc']
            ]);
        }

        return view('website.serviceProvider.explore', compact('jobs'));
    }

//---------New


    public function registrationDocument(Request $request){
        return view("website.login.registration_document");
    }//ends registrationDocument

    public function home(){
//       return Notification::get();
        $home = CmsHomePage::first();
        $hometest = Testimonial::get();

        return view("website.main.home",compact('home','hometest'));
    }
    public function aboutUs(){
        $about = AboutPage::first();
        return view("website.main.about_us",compact('about'));
    }
    public function contact(){
        $contact = ContactPage::first();
        return view("website.main.contact",compact('contact'));
    }
    public function privacyPolicy(){
        $policies = PrivacyPolicy::first();
        return view("website.main.privacy_policy",compact('policies'));
    }
    public function termsCondition(){
        $terms = TermAndCondition::first();
        return view("website.main.terms_condition",compact('terms'));
    }

//    Dashboard
    public function projects(){
        $jobs = Job::orderBy('id','DESC')->get();
        return view("dashboard.admin.projects",compact('jobs'));
    }//ends function projects

    public function projectsOngoingView($job_id = null){
        $job = Job::find($job_id);
        $milestones = JobMilestone::where('job_id',$job_id)->where('staff_id',$job->assign_seller_id)->get();
        return view("dashboard.admin.projects_ongoing_view",compact('job','milestones'));
    }//ends function projectsOngoingView

    public function document(){
        $sellers = User::wherehas('roles',function($query){
            $query->where('name','seller');
        })->get();
        return view("dashboard.admin.document",compact('sellers'));
    }
    public function staffProjects(Request $request, $is_staff_required = null)
    {
        $query = Job::where('assign_staff_id', auth()->user()->id)->orderBy('id', 'desc');

        if ($is_staff_required == 0) {
            $query->where('is_staff_required', 0);
        } else {
            $query->where('is_staff_required', 1);
        }//ends

        $jobs = $query->get();
        return view("dashboard.staff.staff_projects", compact('jobs'));
    }

    public function projectsPending($job_id = null){
        $job = Job::with('jobAssignStaffMeasurements' , 'jobAssignStaffDocuments')->find($job_id);
        $staff = User::wherehas('roles',function($query){ $query->where('name','staff');})->get();
        $milestones = JobMilestone::where('job_id',$job_id)->where('staff_id',$job->assign_seller_id)->get();
        return view("dashboard.admin.projects_pending",compact('job','staff','milestones'));
    }//ends function projectsPending

    public function staffCalendar(){
        $jobs = Job::where('assign_staff_id',auth()->user()->id)->orderBy('id','desc')->get();
        $events = AssignEvent::where('user_id',auth()->user()->id)->get();
        return view("dashboard.staff.staff_calendar",compact('jobs','events'));
    }//ends function staffCalendar...

    public function staffProjectDetails(Request $request, $job_id = null){
        $job = Job::with('jobAssignStaffDocuments','getJobReview')->find($job_id);
        return view("dashboard.staff.staff_project_details",compact('job'));
    }//ends function staffProjectDetails

    public function dispute(){
        return view("dashboard.staff.dispute");
    }

    public function adminStaffManagement(){
        $staff = User::wherehas('roles',function($query){
            $query->where('name','staff');
        })->get();
        return view("dashboard.admin.admin_staff_management",compact('staff'));
    }//ends function adminStaffManagement

    public function adminStaffViewManagement($staff_id = null)
    {
        $staff = User::with('assignStaffToJob.disputedJobs')->find($staff_id);

        if (!$staff) {
            return redirect()->back()->with('error', 'Staff not found');
        }

        $count = $staff->assignStaffToJob()->count();
        // Count the number of disputed jobs where 'released' = 1
        $resolvedDisputedMilestone = $staff->assignStaffToJob
            ->pluck('disputedJobs') // Get all disputed jobs
            ->flatten() // Convert nested collections into a single collection
            ->where('released', 1) // Filter only released disputes
            ->count(); // Get the count
        return view("dashboard.admin.admin_staff_view_management", compact('staff', 'count', 'resolvedDisputedMilestone'));
    }

    public function notifications(){
        return view("dashboard.notifications");
    }
    public function adminChatManagement(){
        $chats = GroupChat::get();
        return view("dashboard.admin.admin_chat_management",compact('chats'));
    }
    public function adminDisputeManagement(){
         $disputes = DisputeMilestone::get();
        return view("dashboard.admin.admin_dispute_management",compact('disputes'));
    }//ends function adminDisputeManagement

    public function adminEwallet()
    {
        // Get all request withdrawals and milestones
        $reqWithdrawal = RequestWithdrawal::orderBy('id', 'desc')->get();
        $milestones = JobMilestone::orderBy('id', 'desc')->get();

        // Calculate monthly data for the graph
        $monthlyData = [
            'releasedMilestones' => JobMilestone::where('requested', 'released')
                ->selectRaw('MONTH(created_at) as month, COALESCE(SUM(amount), 0) as total')
//                ->groupBy('month')
//                ->orderBy('month')
                ->pluck('total', 'month')
                ->toArray(), // Ensure it is an array
            'approvedWithdrawals' => RequestWithdrawal::where('status', 'approved')
                ->selectRaw('MONTH(created_at) as month, COALESCE(SUM(amount), 0) as total')
//                ->groupBy('month')
//                ->orderBy('month')
                ->pluck('total', 'month')
                ->toArray(), // Ensure it is an array
            'rejectedWithdrawals' => RequestWithdrawal::where('status', 'rejected')
                ->selectRaw('MONTH(created_at) as month, COALESCE(SUM(amount), 0) as total')
//                ->groupBy('month')
//                ->orderBy('month')
                ->pluck('total', 'month')
                ->toArray(), // Ensure it is an array
        ];

        // Ensure all months are present in the data, fill missing months with 0
        for ($month = 1; $month <= 12; $month++) {
            $monthlyData['releasedMilestones'][$month] = $monthlyData['releasedMilestones'][$month] ?? 0;
            $monthlyData['approvedWithdrawals'][$month] = $monthlyData['approvedWithdrawals'][$month] ?? 0;
            $monthlyData['rejectedWithdrawals'][$month] = $monthlyData['rejectedWithdrawals'][$month] ?? 0;
        }
        // Pass data to the view
        return view("dashboard.admin.admin_ewallet", compact('reqWithdrawal', 'milestones', 'monthlyData'));
    }
    public function userServiceProvider($user_id = null){
        $user = User::find($user_id);
        return view("dashboard.admin.user_service_provider",compact('user'));
    }//ends function userServiceProvider

    public function userBuyer(){
        return view("dashboard.admin.user_buyer");
    }
    public function staffDispute(){
        $jobs           = Job::where('assign_staff_id',Auth::id())->pluck('id')->toArray();
        $group_chat     = GroupChat::whereIn('admin_id',$jobs)->groupBy('admin_id')->orderBy('order_by', 'DESC')->get();
        $number_of_chat = GroupChat::whereIn('admin_id',$jobs)->get();
        return view("dashboard.staff.staff_dispute",compact('group_chat','number_of_chat'));
    }//ends function staffDispute
    public function adminDisputeChat(){
        return view("dashboard.admin.admin_dispute_chat");
    }

    public function sellerProfileEdit(){
        $user          = User::where('id',Auth::user()->id)->first();
        $salutations   = Salutation::where('status',1)->get();
        $jobCategories = JobCategory::get();
        return view("website.serviceProvider.seller-profile-edit",compact('user','salutations','jobCategories'));
    }

    public function adminChat(){
        return view("dashboard.admin.admin_chat");
    }
    public function buyerHome(){
//        return Auth::user();c
//        return Notification::where('id',auth()->user()->id)->get();
        $category = JobCategory::get();
        return view("website.buyer.buyer_home",compact('category'));
    }
    public function detailsReview($job_id){
        $job  = Job::find($job_id);
        $portfolio = Portfolio::where('user_id',$job->getSeller->id??'')->get();
        return view("website.buyer.details_review",compact('job','portfolio'));
    }
    public function postProject(){
        return view("website.buyer.post_project");
    }

    public function myProjects(){
        $jobs = Job::where('user_id',auth()->user()->id)->orderBy('id','desc')->get();
        return view("website.buyer.my_projects",compact('jobs'));
    }//ends function myProjects

    public function myProfile(Request $request, $id = null){
        $userId = $id ?? Auth::id();
        $user = User::find($userId);

        if (Auth::user()->hasRole('buyer')){
            $role = 'buyer';
        }else{
            $role = 'seller';
        }

        return view("website.buyer.my_profile",compact('user','role'));
    }//ends function myProfile

    public function editProfile(){
        $user = User::where('id',Auth::user()->id)->first();
        return view("website.buyer.edit_profile",compact('user'));
    }

    public function myProjectsOngoingView($job_id = null){
        $job = Job::find($job_id);
        $milestones = JobMilestone::where('job_id',$job_id)->where('staff_id',$job->assign_seller_id)->get(); //staff_id is the seller id
//        $disputeMilestones = JobMilestone::where('job_id',$job_id)->where('staff_id',$job->assign_seller_id)->whereNot('requested','released')->get();
//        $milestones = JobMilestone::where('job_id',$job_id)
//            ->where('staff_id',$job->assign_seller_id)
//            ->whereNot('requested','released')
//            ->whereDoesntHave('disputeMilestones')
//            ->get();

//        $dispute = DisputeMilestone::where('job_offer_id',$job->jobOffersPaid->id)->where('status',0)->count();
        $dispute = DisputeMilestone::where('job_offer_id', $job->jobOffersPaid->id)
            ->whereIn('status', [0, 1])
            ->count();
        if(Auth::user()->id == $job->user_id){
        return view("website.buyer.my_projects_ongoing_view",compact('job','milestones','dispute'));
        }
        return redirect()->route('home')->with([
            'title'   => 'Access Denied',
            'message' => 'You are not authorized to view this project.',
            'type'    => 'error'
        ]);
    }//ends function myProjectsOngoingView

    public function postedView(Request $request, $job_id  =  null){
        $job = Job::with( 'jobAssignStaffMeasurements' , 'jobAssignStaffDocuments','jobMilestone')->find($job_id);
//        if(Auth::user()->id == $job->user_id){
            return view("website.buyer.posted_view",compact('job'));
//        }

//        return redirect()->route('home')->with([
//            'title'   => 'Access Denied',
//            'message' => 'You are not authorized to view this project.',
//            'type'    => 'error'
//        ]);
    }//ends function postedView

    public function myProjectsCompletedView($job_id = null){
        $job = Job::with('getJobReview')->find($job_id);
        $milestones = JobMilestone::where('job_id',$job_id)->where('staff_id',$job->assign_seller_id)->get();
        $dispute = DisputeMilestone::where('job_offer_id',$job->jobOffersPaid->id)->where('status',0)->count();
        return view("website.buyer.my_projects_completed_view",compact('job','milestones','dispute'));
    }

//    public function explore(){
//        return view("website.serviceProvider.explore");
//    }

    public function exploreViewProject($job_id = null){
        $job = Job::find($job_id);
        if (auth()->user()->hasRole('seller')){
            if (auth()->user()->user_status == 'suspended'){
                return back()->with(['type' => 'error', 'message' => 'Your account is suspended. Please contact support for more information.']);
            }
            if ($job->status == "posted"){
                return view("website.serviceProvider.explore_view_project",compact('job'));
            }else{
                return redirect('all_projects')->with(['title' => 'Already Awarded','message' => 'This project has already been awarded.','type' => 'info']);
            }

        }else{
            if ($job->status == "on_going"){
                return redirect('/')->with(['title' => 'Error','message' => 'You are not authorized to access this page.','type' => 'error']);
            }else{
                return view("website.serviceProvider.explore_view_project",compact('job'));
            }//ends else
        }
    }//ends function exploreViewProject

    public function profileSetting(){
        return view("website.serviceProvider.profile_setting");
    }
    public function serviceProfile(Request $request, $id = null)
    {
        $userId = $id ?? Auth::id();

        $user = User::with('ratingReview')->find($userId);

        if (Auth::user()->hasRole('buyer')){
            $role = 'buyer';
        }else{
            $role = 'seller';
        }
        $expertise = SellerExpertise::get();
        $portfolio = Portfolio::with('portfolioAttachments')->where('user_id', $userId)->get();
        return view("website.serviceProvider.service_profile", compact('user','role', 'expertise', 'portfolio'));
    }
    public function allProjects(){
       $projects = Job::where('assign_seller_id',auth()->user()->id)->orderBy('id','desc')->get();
        return view("website.serviceProvider.all_projects",compact('projects'));
    }//ends function allProjects

    public function ewallet(Request $request){
        $jobMilestone = JobMilestone::where('staff_id',auth()->user()->id)->get();
        $reqWithdrawal = RequestWithdrawal::where('seller_id',auth()->user()->id)->get();
        return view("website.serviceProvider.ewallet",compact('jobMilestone','reqWithdrawal'));
    }//ends function ewallet

    public function buyerChat(){
        return view("website.buyer.buyer_chat");
    }

    public function ServiceProviderProjects(){
        return view("website.serviceProvider.service_provider_projects");
    }
    public function ServiceProviderChat(){
        return view("website.serviceProvider.service_provider_chat");
    }

    public function storeReviewRating(Request $request){
        RatingReview::create(
            [
                'user_id'  =>  $request->user_id,
                'stars'    =>  $request->rating,
                'review'   =>  $request->review,
                'sender_id'=>  Auth::id(),
                'job_id'   =>  $request->job_id
            ]
        );
        return back()->with([
            'title' => 'Success',
            'message' => 'Review has been added successfully.',
            'type' => 'success',
        ]);
    }

    public function ServiceCompletedProjects(Request $request, $job_id = null){
        // return $request->all();
        try {
            $job = Job::where('assign_seller_id', auth()->user()->id)->find($job_id);
            if (!$job) {

                return redirect('home')->with([
                    'title' => 'Error',
                    'message' => 'Job not found or you are not authorized to access this job.',
                    'type' => 'error',
                ]);
            }
            $jobOffer = JobOffer::where('job_id',$job_id)->where('staff_id',$job->assign_seller_id)->first();

            $milestones = JobMilestone::where('job_id',$job_id)->where('staff_id',$job->assign_seller_id)->get();
//            $disputeMilestones = JobMilestone::where('job_id',$job_id)->where('staff_id',$job->assign_seller_id)->where('requested','requested')->get();

            $disputeMilestones = JobMilestone::where('job_id', $job_id)
                ->where('staff_id', $job->assign_seller_id)
                ->where('requested', 'requested')
                ->whereDoesntHave('disputeMilestones')
                ->get();



//            $dispute = DisputeMilestone::where('job_offer_id',$job->jobOffersPaid->id)->where('status',0)->count();
            $dispute = DisputeMilestone::where('job_offer_id', $job->jobOffersPaid->id)
                ->whereIn('status', [0, 1])
                ->count();

            return view("website.serviceProvider.service_completed_projects",compact('job','milestones','jobOffer','dispute','disputeMilestones'));
        } catch (Exception $e) {
            return redirect('home')->with([
                'title' => 'Error',
                'message' => 'Something went wrong. Please try again later.',
                'type' => 'error',
            ]);
        }

    }//ends function ServiceCompletedProjects

    public function adminContracts(Request $request){
        $adminContracts = JobOffer::where('payment_type','wire_transfer')->get();
        return view("dashboard.admin.admin_contracts",compact('adminContracts'));
    }//ends function adminContracts

    public function contactUs(){
        return view("website.contact_us");
    }
    public function websiteNotification(){
//    $notifications = Notification::where('notifiable_id',auth()->user()->id)->orderBy('id','desc')->get();
//        $unreadCount = Notification::where('notifiable_id', auth()->user()->id)
//            ->whereNull('read_at')
//            ->count();
//    return view("website.website_notification",compact('notifications','unreadCount'));
    return view("website.website_notification");
    }//ends function websiteNotification

    public function contentManagementSystem(){
        $homePage = CmsHomePage::first();
        $about = AboutPage::first();
        $contact = ContactPage::first();
        $terms = TermAndCondition::first();
        $privacy = PrivacyPolicy::first();
        $testimonials = Testimonial::get();
        return view("dashboard.admin.content_management_system",compact('homePage','about','contact','terms','privacy','testimonials'));
    }
    public function disputeChat(){
        return view("website.dispute_chat");
    }
    public function furtherDetails(){
        return view("dashboard.staff.further_details");
    }

    public function plumbing(){
        return view("website.categories.plumbing");
    }

    public function servicesBidOffer($job_id = null){
        $job = Job::find($job_id);
        $offer = JobOffer::where('job_id',$job_id)->where('staff_id',auth()->user()->id)->first(); // staff_id is the seller id
        $milestones = JobMilestone::where('job_id',$job_id)->where('staff_id',auth()->user()->id)->get(); // staff_id is the seller id
        return view("website.serviceProvider.services_bid_offer",compact('job','offer','milestones'));
    }//ends function servicesBidOffer

    public function estimateBidOffer(Request $request, $job_id = null){
        $job = Job::find($job_id);
        $offer = EstimateOffer::where('job_id',$job_id)->where('seller_id',auth()->user()->id)->first(); // staff_id is the seller id
        return view("website.serviceProvider.estimate_bid_offer",compact('job', 'offer'));
    }//ends function estimateBidOffer

    public function sellerBids(Request $request){
        $jobOffers = JobOffer::where('staff_id',auth()->user()->id)->get();
        $jobEstimateOffers = EstimateOffer::where('seller_id',auth()->user()->id)->get();
        return view("website.serviceProvider.seller_bids",compact('jobOffers','jobEstimateOffers'));
    }//ends function sellerBids

    public function viewSellersBid(Request $request, $job_id = null){
        $job = Job::with( 'jobAssignStaffMeasurements' , 'jobAssignStaffDocuments')->find($job_id);
        return view("website.serviceProvider.view_sellers_bid",compact('job'));
    }//ends function viewSellersBid

    public function projectContract(Request $request, $job_offer_id = null ,$job_id = null){
        $job = Job::find($job_id);
        $jobOffer = JobOffer::find($job_offer_id);// staff_id is the seller id
        $milestones = JobMilestone::where('job_id',$job_id)->where('staff_id',$jobOffer->staff_id)->get();// staff_id is the seller id
        return view("website.project_contract",compact('job','jobOffer','milestones'));
    }//ends function projectContract..

    public function emailVerification(Request $request, $job_offer_id = null)
    {
        $jobOffer = JobOffer::find($job_offer_id);
        if (!$jobOffer) {
            return redirect('/')->with([
                'title' => 'Error',
                'message' => 'Job Offer not found.',
                'type' => 'error',
            ]);
        }
        if ($jobOffer->status_paid === 'paid') {
            return redirect(url('my_projects'))->with([
                'title' => 'success',
                'message' => 'Your status has been changed to: ' . $jobOffer->status_paid . '.',
                'type' => 'success',
            ]);
        }
        return view("website.email_verification");
    }

    public function uploadUsersAttachment(Request $request){

        $images = "";
        if ($request->hasFile('file')) {
            $fileName = $this->storeImage('users_attachment', $request->file('file'));
            return response()->json(['filename' => $fileName]);
        }
        return response()->json(['error' => 'No file uploaded'], 400);
    }
    public function uploadJobsAttachment(Request $request)
    {
        $images = "";
        if ($request->hasFile('file')) { // Use the key name directly
            $fileName = $this->storeImage('job_question', $request->file('file'));
            return response()->json(['filename' => $fileName]);
        }
        return response()->json(['error' => 'No file uploaded'], 400);
    }
    public function removeUsersAttachment(Request $request)
    {
        if ($request->id == 0) {
            return ($this->deleteImage($request->image_name)); // Delete only the image
        } else {
            UserAttachment::where('id', $request->id)->delete(); // Delete the record
            return ($this->deleteImage($request->image_name)); // Delete the image as well
        }
    }
    public function createUser(Request $request){
        return $request->all();
    }//ends createUser

    public function getSubCategory(Request $request){
         extract($request->all());
           $subcategory = JobSubcategory::where('job_category_id',$id)->get();
           return (string) view('website.ajax.get_sub_category',compact('subcategory'));
    }//ends getSubCategory

    public function getSubCategoryQuestion(Request $request, $id = null){
         extract($request->all());
         $job_question = JobQuestion::where('job_sub_category_id',$id)->get();
         return (string) view('website.ajax.get_sub_category_question',compact('job_question'));
    }//ends createUser
    public function getSideBarUser(Request $request){
        extract($request->all());
        $Active =   Session::get('group_id_actived');
        if (isset($job_id) && $job_id != 0) {
           $milestones_id = JobMilestone::where('job_id',$job_id)->pluck('id')->toArray();
        }else{
           $milestones_id = ['0'];
        }
        if (Auth::user()->hasRole('buyer')){
            $groups = GroupChat::where(function($query) use ($milestones_id) {
                $query->where('sender_id', Auth::user()->id)->whereIn('milestone_id',$milestones_id);
            })->orWhere(function ($query) use ($milestones_id) {
                $query->where('receiver', Auth::user()->id)->whereIn('milestone_id',$milestones_id);
            })->orderBy('order_by', 'DESC')->get();
        }elseif(Auth::user()->hasRole('seller')){
            $groups = GroupChat::where('status',1)->where(function($query) use ($milestones_id) {
                $query->where('sender_id', Auth::user()->id)->whereIn('milestone_id',$milestones_id);
            })->orWhere(function ($query) use ($milestones_id) {
                $query->where('receiver', Auth::user()->id)->whereIn('milestone_id',$milestones_id);
            })->orderBy('order_by', 'DESC')->get();
        }elseif(Auth::user()->hasRole('staff')){
            $groups = GroupChat::where(function($query) use ($milestones_id) {
                $query->whereIn('milestone_id',$milestones_id)->where('show_group',1);
            })->orWhere(function ($query) use ($milestones_id) {
                $query->whereIn('milestone_id',$milestones_id)->where('show_group',1);
            })->orderBy('order_by', 'DESC')->get();
        }elseif(Auth::user()->hasRole('user')){
            $groups = GroupChat::with('disputeMilestone')->where(function($query) use ($milestones_id) {
                $query->whereIn('milestone_id',$milestones_id);
            })->orWhere(function ($query) use ($milestones_id) {
                $query->whereIn('milestone_id',$milestones_id);
            })->orderBy('order_by', 'DESC')->get();
        }
        return (string) view('website.ajax.get_side_bar_user',compact('groups','Active'));
    }//end function Proposal.


    public function testing(){
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $checkoutSession = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card','us_bank_account'], // Supports 'card', 'us_bank_account', etc.
            'line_items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'product_data' => [
                        'name' => 'Sample Product',
                    ],
                    'unit_amount' =>10000, // Amount in cents
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'success_url' => 'https://mrdoall.thebackendprojects.com/?session_id={CHECKOUT_SESSION_ID}',
            'cancel_url' => url('/stripe-payment-cancel'),
        ]);

    }

    public function updatePortfolio(Request $request){
        extract($request->all());
        try {
            $portfolio = Portfolio::find($portfolio_id);
            $portfolio->update([
                'user_id'       => auth()->user()->id,
                'title'         => $title,
                'description'   => $description,
            ]);
            if (!empty($request->file('portfolio_attachments'))){
                foreach ($request->file('portfolio_attachments') as $attachment) {
                    $filePath = $this->storeImage('portfolio_attachments', $attachment);
                    PortfolioAttachment::create([
                        'portfolio_id' => $portfolio->id,
                        'file'   => $filePath,
                    ]);
                }//ends foreach
            }//ends
            return redirect()->back()->with([
                'message' => 'Portfolio has been updated successfully',
                'type'    => 'success',
                'title'   => 'Done'
            ]);

        } catch (\Exception $e) {
            return redirect()->back()->with([
                'message' => 'Unable to add portfolio: ' . $e->getMessage(),
                'type'    => 'error',
                'title'   => 'Fail'
            ]);
        }
    }//ends function updatePortfolio



    public function editPortfolio(Request $request)
    {
        try {
            $portfolio = Portfolio::create([
                'user_id' => $request->user_id,
                'title'   => $request->title,
                'description'   => $request->description,
            ]);
            // Process each file
            foreach ($request->file('portfolio_attachments') as $attachment) {
                    $filePath = $this->storeImage('portfolio_attachments', $attachment);
                    PortfolioAttachment::create([
                        'portfolio_id' => $portfolio->id,
                        'file'   => $filePath,
                    ]);
            }

            return redirect()->back()->with([
                'message' => 'Portfolio has been added successfully',
                'type'    => 'success',
                'title'   => 'Done'
            ]);

        } catch (\Exception $e) {
            return redirect()->back()->with([
                'message' => 'Unable to add portfolio: ' . $e->getMessage(),
                'type'    => 'error',
                'title'   => 'Fail'
            ]);
        }
    }

    public function  contactUsStore(Request $request){
        $contact = Contact::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'message' => $request->message,
        ]);
        if($contact){
            return redirect()->back()->with(['message'=>'Your response has been recorded successfully','type'=>'success','title'=>'Done']);
        }else{
            return redirect()->back()->with(['message'=>'Unable to submit your request try again..!','type'=>'error','title'=>'Fail']);
        }
    }


    public function dashboard(Request $request)
    {
        //for admin side
        $jobs = Job::get();
        $openProjects = $jobs->where('status', 'on_going');
        $closedProjects = $jobs->where('status', 'completed');
        $topStaff = JobMilestone::where('requested', 'released')
            ->selectRaw('staff_id, job_id, SUM(amount) as total_amount') //staff_id is the seller id
            ->groupBy('staff_id', 'job_id') // Group by both staff_id and job_id
            ->orderByDesc('total_amount')
            ->take(3)
            ->get();

//            $topStaff = JobMilestone::where('requested', 'released')
//            ->selectRaw('staff_id, SUM(amount) as total_amount') // Sum amount per staff
//            ->groupBy('staff_id') // Group only by staff_id
//            ->orderByDesc('total_amount') // Order by highest earning
//            ->take(3) // Get top 3 staff
//            ->get();

        $openProjectPrice = $openProjects->count();
        $closedProjectPrice = $closedProjects->count();
        $totalSalesDetail = JobMilestone::where('requested', 'released')->get();//sum('amount');
        $totalSales =  $totalSalesDetail->sum('amount');
        $amounts = $totalSalesDetail->pluck('amount')->toArray(); // Extract the 'amount' column values
        $reqWithdrawal = RequestWithdrawal::get();
        $approvedAmount = $reqWithdrawal->where('status', 'approved')->sum('amount') ?? 0; // Total approved amount
        $adminCommission = ($approvedAmount * 12) / 100; // Calculate 12% commission
//        $adminComm = $adminCommission ?? 0;
        $adminComm = $totalSales * 0.12;
        $totalContractors = User::whereHas('roles', function ($query) { $query->where('name', 'seller'); })->count();
        $totalBuyer = User::whereHas('roles', function ($query) { $query->where('name', 'buyer'); })->count();
        //for staff side
        $scheduledVisits    = $jobs->where('assign_staff_id',auth()->user()->id)->where('staff_status', 'accepted')->count();
        $staffAssign        = $jobs->where('assign_staff_id', auth()->user()->id)->pluck('id')->toArray();
        $disputedChats      = GroupChat::whereIn('admin_id', $staffAssign)->groupBy('admin_id')->get();
        $openDisputed       = $disputedChats->where('dispute_status',0)->count();
        $closedDisputed     = $disputedChats->where('dispute_status',1)->count();
        $todayProjectsPending  = $jobs->where('assign_staff_id',auth()->user()->id)->where('status','pending')->count();
        $UpcomingSiteVisits = $jobs->where('assign_staff_id',auth()->user()->id)->where('status', 'pending');
        $liveSiteVisits     = $jobs->where('assign_staff_id',auth()->user()->id)->where('status', 'on_going');
        return view('dashboard.index', compact(
                'openProjectPrice',
                'totalContractors',
                'totalBuyer',
                'amounts',
                'totalSales',
                'closedProjectPrice',
                'adminComm',
                'jobs',
                'topStaff',
                'scheduledVisits',
                'openDisputed',
                'closedDisputed',
                'todayProjectsPending',
                'UpcomingSiteVisits',
                'liveSiteVisits',
            ));
    }//ends function dashboard


    public function removeImagePortfolio(Request $request, $portfolio_id = null){
        $portfolioAttachment = PortfolioAttachment::find($portfolio_id);
        $portfolioAttachment->delete();
        return response()->json(['message' => 'Image deleted successfully', 'success' => 'success']);
    }//ends function removeImagePortfolio

    public function subscribe(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);
        $email = $request->input('email');
        $apiKey = env('MAILCHIMP_API_KEY');
        $listId = env('MAILCHIMP_LIST_ID');
        $dataCenter = substr($apiKey, strpos($apiKey, '-') + 1);
        $url = "https://{$dataCenter}.api.mailchimp.com/3.0/lists/{$listId}/members/" . md5(strtolower($email));
        $response = Http::withHeaders(['Authorization' => 'Basic ' . base64_encode('user:' . $apiKey), 'Content-Type' => 'application/json',])
            ->put($url, ['email_address' => $email, 'status' => 'subscribed',]);
        if ($response->successful()) {
            return response()->json(['message' => 'Subscribed successful!']);
        } else {
            return response()->json(['error' => $response->json()], 400);
        }
    }

//    public function downloadAllDocument($jobId)
//    {
//        $job = Job::find($jobId);
//        if ($job->jobFiles->count() > 0) {
//           return $documents = $job->jobFiles;
//            $zipFileName = 'job-'.$job->project_number. '.zip';
//            $zipPath = public_path('website/' . $zipFileName);
//            $zip = new ZipArchive;
//            if ($zip->open($zipPath, ZipArchive::CREATE) === TRUE) {
//                foreach ($documents as $file) {
//                    $filePath = Storage::disk('website')->path($file->file);
//                    if (Storage::disk('website')->exists($file->file)){
//                        $zip->addFile($filePath, basename($filePath));
//                    } else {
//                        \Log::warning("File does not exist and was skipped: {$filePath}");
//                    }//ends if
//                }//ends foreach
//                $zip->close();
//
//                try {
//                    if (file_exists($zipPath) && is_readable($zipPath)) {
//                        return response()->download($zipPath)->deleteFileAfterSend(true);
//                    }
//                }catch (\Exception $e) {
//
//                }//ends try catch
//            }
//        } else {
//            return redirect()->back()->with('not_permitted', '"Sorry! The zip file could not be created or retrieved because there are no documents associated with this job.');
//
//        }//ends if
//    }//ends function downloadAllDocuments.


    public function downloadAllDocument($jobId)
    {
        $job = Job::find($jobId);
        if ($job->jobFiles->count() > 0) {
            $documents = $job->jobFiles;
            $zipFileName = 'job-' . $job->project_number . '.zip';
            $zipPath = public_path('website/' . $zipFileName);
            $zip = new ZipArchive;
            // Temporary directory for storing downloaded files
            $tempDir = public_path('website/temp_' . uniqid());
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0777, true);
            }

            if ($zip->open($zipPath, ZipArchive::CREATE) === TRUE) {
                foreach ($documents as $file) {
                    try {

                        // Fetch the file from the URL
                        $fileUrl = $file->file;

                        $fileContent = file_get_contents($fileUrl);

                        if ($fileContent === false) {
                            continue;
                        }

                        // Determine the file extension and name
                        $fileName = basename($fileUrl);

                        $tempFilePath = $tempDir . '/' . $fileName;
                        // Save the file temporarily
                        file_put_contents($tempFilePath, $fileContent);

                        // Add the file to the ZIP
                        if (file_exists($tempFilePath)) {
                            $zip->addFile($tempFilePath, $fileName);
                        } else {
                        }

                    } catch (\Exception $e) {
                        continue;
                    }
                }

                $zip->close();

                // Clean up temporary files
                foreach (glob($tempDir . '/*') as $tempFile) {
                    @unlink($tempFile);
                }
                @rmdir($tempDir);
                // Download the ZIP file
                try {
                    if (file_exists($zipPath) && is_readable($zipPath)) {
                        return response()->download($zipPath)->deleteFileAfterSend(true);
                    } else {
                        return back()->with(['type' => 'error', 'message' => 'Sorry! The zip file could not be created or retrieved.']);
                    }
                } catch (\Exception $e) {
                    return back()->with(['type' => 'error', 'message' => 'Sorry! An error occurred while downloading the zip file.']);
                }

            } else {
                return back()->with(['type' => 'error', 'message' => 'Sorry! The zip file could not be created.']);
            }

        } else {
            return back()->with(['type' => 'error', 'message' => 'Sorry! The zip file could not be created or retrieved because there are no documents associated with this job.']);
        }
    }


    public function redirectToGoogle($type)
    {
        session(['google_auth_type' => $type]);
//        \Log::info('Google Auth Type from redirect route: ' , ['type'=>session('google_auth_type')]);
        return Socialite::driver('google')
//            ->with(['prompt' => 'select_account']) // Forces Google to ask for email
            ->redirect();
    }

    public function handleGoogleCallback(Request $request)
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            $authType = session('google_auth_type'); // Get if it's login or signup
            session()->forget('google_auth_type'); // Clear session after use
            if (!$googleUser || !$googleUser->getEmail()) {
                return redirect()->route('login')->with('error', 'Google authentication failed.');
            }
            $existingUser = User::where('email', $googleUser->getEmail())->first();
            if ($authType === 'login') {
                // If user clicked "Sign in with Google"
                if ($existingUser) {
                    if (!$existingUser->google_id) {
                        $existingUser->update(['google_id' => $googleUser->getId()]);
                    }
//                    return Socialite::driver('google')->with(['prompt' => 'select_account'])->redirect();
                    Auth::login($existingUser);
                    if ($existingUser->hasRole('buyer')) {
                        return redirect()->route('buyer_home')->with('success', 'Logged in as Buyer!');
                    } elseif ($existingUser->hasRole('seller')) {
                        return redirect()->route('seller_home')->with('success', 'Logged in as Seller!');
                    } else {
                        return redirect()->route('home')->with('error', 'No role assigned, please update your profile.');
                    }
                } else {
                    return redirect()->route('login')->with('error', 'No account found. Please sign up first.');
                }
            } elseif ($authType === 'register') {
                // If user clicked "Sign up with Google"
                if ($existingUser) {
                    // If user already exists, log them in instead of registering
                    Auth::login($existingUser);
                    return redirect()->route('home')->with('success', 'You already have an account and have been logged in.');
                }//ends if

                // If new user, store their Google info and redirect to role selection

                session([
                    'google_user' => [
                        'name' => $googleUser->getName(),
                        'email' => $googleUser->getEmail(),
                        'google_id' => $googleUser->getId(),
                        'address' => $googleUser->user['address'] ?? null, // Check if address is available
                    ]
                ]);
                return redirect()->route('registration_role'); // Redirect to role selection
            }
            return redirect()->route('login')->with('error', 'Invalid authentication request.');
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'Google login failed. Please try again.');
        }
    }

    public function editTestimonial(Request $request, $id)
    {
        $test = Testimonial::findOrFail($id);
        $test->update([
            'name' => $request->testimonial_name,
            'role' => $request->testimonial_role,
            'description' => $request->testimonial_description,
        ]);
        if($request->hasFile('testimonial_image')){
            $test->update([
                'image' => $this->storeImage('cms', $request->testimonial_image),
            ]);
            $test->save();
        }
        return response()->json(['status' => 200, 'message' => 'Testimonial updated successfully', 'testimonial' => $test]);
    }

    public function deleteTestimonial($id = null)
    {
        $testimonial = Testimonial::findOrFail($id);
        $testimonial->delete();
        return response()->json(['message' => 'Testimonial deleted successfully']);
    }

    public function markAsRead($notificationId)
    {
        $notification = Notification::where('notifiable_id', auth()->user()->id)->findOrFail($notificationId);
        $notification->markAsRead();
        return response()->json(['message' => 'Notification marked as read']);
    }

    public function markAllAsRead()
    {
        Notification::where('notifiable_id', auth()->user()->id)->unread()->update(['read_at' => now()]);
        return response()->json(['message' => 'All notifications marked as read']);
    }

}
